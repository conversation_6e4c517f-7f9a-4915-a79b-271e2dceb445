<?php

namespace App\Adapters;

use App\Models\FormIntegrationSetting;

class CsiFormIntegrationAdapter extends BaseFormIntegrationAdapter
{
    /**
     * Get the ERP system this adapter supports
     */
    public function getSupportedErp(): string
    {
        return 'CSI';
    }

    /**
     * Get supported process types for this ERP
     */
    public function getSupportedProcesses(): array
    {
        return ['Misc Issue', 'Misc Receipt', 'Quantity Move'];
    }

    /**
     * Apply CSI-specific transformations
     */
    protected function applyErpSpecificTransformations(array $transformedData, FormIntegrationSetting $integrationSetting): array
    {
        $processType = $integrationSetting->endpointConfiguration->process_selection;

        switch ($processType) {
            case 'Misc Issue':
                return $this->transformForMiscIssue($transformedData, $integrationSetting);
            
            case 'Misc Receipt':
                return $this->transformForMiscReceipt($transformedData, $integrationSetting);
            
            case 'Quantity Move':
                return $this->transformForQuantityMove($transformedData, $integrationSetting);
            
            default:
                return $transformedData;
        }
    }

    /**
     * Apply CSI-specific validation rules
     */
    protected function validateErpSpecificRules($value, array $fieldDefinition, string $formField): array
    {
        $errors = [];
        $fieldName = $fieldDefinition['name'] ?? 'unknown';

        // CSI-specific validation rules
        switch ($fieldName) {
            case 'item_code':
                if (!empty($value) && !preg_match('/^[A-Z0-9-]+$/', $value)) {
                    $errors[] = "CSI Item Code '{$fieldName}' must contain only uppercase letters, numbers, and hyphens.";
                }
                break;
            
            case 'location_code':
                if (!empty($value) && strlen($value) > 10) {
                    $errors[] = "CSI Location Code '{$fieldName}' cannot exceed 10 characters.";
                }
                break;
            
            case 'quantity':
                if (!empty($value) && $value <= 0) {
                    $errors[] = "CSI Quantity '{$fieldName}' must be greater than zero.";
                }
                break;
        }

        return $errors;
    }

    /**
     * Apply CSI-specific API preparation
     */
    protected function applyErpSpecificApiPreparation(array $apiData, FormIntegrationSetting $integrationSetting): array
    {
        // Add CSI-specific headers or data structure modifications
        $apiData['headers'] = [
            'Content-Type' => 'application/json',
            'X-CSI-Version' => '1.0',
            'X-Integration-Source' => 'Form-Integration-System'
        ];

        // Add CSI-specific authentication or session data if needed
        $apiData['auth'] = [
            'type' => 'bearer', // or whatever CSI uses
            'credentials' => config('csi.api_credentials', [])
        ];

        return $apiData;
    }

    /**
     * Transform data for Misc Issue process
     */
    private function transformForMiscIssue(array $data, FormIntegrationSetting $integrationSetting): array
    {
        // Add any Misc Issue specific transformations
        if (isset($data['quantity'])) {
            $data['quantity'] = abs($data['quantity']); // Ensure positive for issue
        }

        // Add default values for CSI Misc Issue if not provided
        $data['transaction_type'] = $data['transaction_type'] ?? 'ISSUE';
        $data['reason_code'] = $data['reason_code'] ?? 'MISC';

        return $data;
    }

    /**
     * Transform data for Misc Receipt process
     */
    private function transformForMiscReceipt(array $data, FormIntegrationSetting $integrationSetting): array
    {
        // Add any Misc Receipt specific transformations
        if (isset($data['quantity'])) {
            $data['quantity'] = abs($data['quantity']); // Ensure positive for receipt
        }

        // Add default values for CSI Misc Receipt if not provided
        $data['transaction_type'] = $data['transaction_type'] ?? 'RECEIPT';
        $data['reason_code'] = $data['reason_code'] ?? 'MISC';

        return $data;
    }

    /**
     * Transform data for Quantity Move process
     */
    private function transformForQuantityMove(array $data, FormIntegrationSetting $integrationSetting): array
    {
        // Add any Quantity Move specific transformations
        if (isset($data['quantity'])) {
            $data['quantity'] = abs($data['quantity']); // Ensure positive for move
        }

        // Add default values for CSI Quantity Move if not provided
        $data['transaction_type'] = $data['transaction_type'] ?? 'MOVE';

        // Ensure both from and to locations are provided
        if (empty($data['from_location']) || empty($data['to_location'])) {
            // This will be caught in validation, but we can set defaults if needed
        }

        return $data;
    }
}
