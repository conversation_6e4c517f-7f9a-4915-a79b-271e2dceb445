<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('endpoint_configurations', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('Configuration name for identification');
            $table->text('url')->comment('API endpoint URL');
            $table->json('body_data_field')->nullable()->comment('JSON body data field mapping');
            $table->enum('erp_selection', ['CSI', 'SAP'])->default('CSI')->comment('ERP system selection');
            $table->enum('process_selection', ['Misc Issue', 'Misc Receipt', 'Quantity Move'])->comment('Process type selection');
            $table->enum('endpoint_type', ['API', 'Stored Procedure'])->default('API')->comment('Endpoint type selection');
            $table->boolean('is_active')->default(true)->comment('Whether this configuration is active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('endpoint_configurations');
    }
};
