
<?php $__env->startSection('pageTitle', 'Dashboard'); ?>
<?php $__env->startPush('styles'); ?>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.form.io/js/formio.full.min.css">
    <link rel="stylesheet" href="https://cdn.form.io/js/formio.form.min.css">
    <style>
        .builder {
            width: auto;
            right: 0px;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <!--<div class="content">-->

    <div class="card card-default">
        <div class="card-header">
            Dashboard
        </div>
        <div class="card-body">
            <div id="builder"></div>
            <div id="formio"></div>

        </div>

    </div>

    <!--</div>-->
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script src="https://cdn.form.io/js/formio.form.min.js"></script>
    <script src="https://cdn.form.io/js/formio.full.min.js"></script>


    <script>
        var schema = {
            builder: {
                basic: false,
                advanced: false,
                premium: false,
                data: false,
                customBasic: {
                    title: 'Basic Components',
                    default: true,
                    weight: 0,
                    components: {
                        textfield: true,
                        textarea: true,
                        email: true,
                        phoneNumber: true
                    }
                },
                // custom: {
                //     title: 'User Fields',
                //     weight: 10,
                //     components: {
                //         firstName: {
                //             title: 'First Name',
                //             key: 'firstName',
                //             icon: 'terminal',
                //             schema: {
                //                 label: 'First Name',
                //                 type: 'textfield',
                //                 key: 'firstName',
                //                 input: true
                //             }
                //         },
                //         lastName: {
                //             title: 'Last Name',
                //             key: 'lastName',
                //             icon: 'terminal',
                //             schema: {
                //                 label: 'Last Name',
                //                 type: 'textfield',
                //                 key: 'lastName',
                //                 input: true
                //             }
                //         },
                //         email: {
                //             title: 'Email',
                //             key: 'email',
                //             icon: 'at',
                //             schema: {
                //                 label: 'Email',
                //                 type: 'email',
                //                 key: 'email',
                //                 input: true
                //             }
                //         },
                //         phoneNumber: {
                //             title: 'Mobile Phone',
                //             key: 'mobilePhone',
                //             icon: 'phone-square',
                //             schema: {
                //                 label: 'Mobile Phone',
                //                 type: 'phoneNumber',
                //                 key: 'mobilePhone',
                //                 input: true
                //             }
                //         }
                //     }
                // },
                layout: {
                    components: {
                        table: false
                    }
                }
            },
            editForm: {
                textfield: [{
                    key: 'api',
                    ignore: true
                }]
            }
        };

        var formB = Formio.builder(document.getElementById('builder'), {}, schema).then(function(builder) {

            builder.on('saveComponent', function() {
                // console.log(builder.schema);
            });
            builder.on("change", function(e) {
                console.log(builder.schema);
            });
        });

        Formio.createForm(document.getElementById('formio'), {
            components: [{
                    type: 'textfield',
                    key: 'firstName',
                    label: 'First Name',
                    case: "uppercase",
                    truncateMultipleSpaces: true,
                },
                {
                    type: 'textfield',
                    key: 'lastName',
                    label: 'Last Name'
                },
                {
                    type: 'email',
                    key: 'email',
                    label: 'Email'
                },
                {
                    type: 'button',
                    key: 'submit',
                    label: 'Submit'
                }
            ]
        }).then(function(form) {
            // form.on('change', function(submission) {
            //     console.log(submission);
            // });
            form.on('submit', function(submission) {
                console.log(submission.data);
            });
        });
    </script>
    
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Git Data Capture\application\resources\views/welcome.blade.php ENDPATH**/ ?>