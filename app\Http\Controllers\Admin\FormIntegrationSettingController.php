<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FormIntegrationSetting;
use App\Models\Form;
use App\Models\EndpointConfiguration;
use App\Http\Requests\StoreFormIntegrationSettingRequest;
use App\Http\Requests\UpdateFormIntegrationSettingRequest;
use App\Services\FormIntegrationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;

class FormIntegrationSettingController extends Controller
{
    protected FormIntegrationService $formIntegrationService;

    public function __construct()
    {
        $this->formIntegrationService = app(FormIntegrationService::class);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        try {
            if ($request->ajax()) {
                $user = Auth::user();
                $query = FormIntegrationSetting::with(['form', 'endpointConfiguration', 'createdBy'])
                    ->accessibleByUser($user)
                    ->orderBy('created_at', 'desc');

                return DataTables::of($query)
                    ->addColumn('form_name', function ($setting) {
                        return $setting->form ? $setting->form->title : 'N/A';
                    })
                    ->addColumn('endpoint_name', function ($setting) {
                        return $setting->endpointConfiguration ? $setting->endpointConfiguration->name : 'N/A';
                    })
                    ->addColumn('erp_system', function ($setting) {
                        return $setting->endpointConfiguration ? $setting->endpointConfiguration->erp_selection : 'N/A';
                    })
                    ->addColumn('process_type', function ($setting) {
                        return $setting->endpointConfiguration ? $setting->endpointConfiguration->process_selection : 'N/A';
                    })
                    ->addColumn('status', function ($setting) {
                        return $setting->is_active
                            ? '<span class="badge badge-success">Active</span>'
                            : '<span class="badge badge-secondary">Inactive</span>';
                    })
                    ->addColumn('created_by_name', function ($setting) {
                        return $setting->createdBy ? $setting->createdBy->name : 'System';
                    })
                    ->addColumn('actions', function ($setting) {
                        return view('admin.form-integration-settings.partials.actions', compact('setting'))->render();
                    })
                    ->rawColumns(['status', 'actions'])
                    ->make(true);
            }

            $statistics = $this->formIntegrationService->getIntegrationStatistics();

            return view('admin.form-integration-settings.index', compact('statistics'));
        } catch (\Exception $e) {
            Log::error('Form Integration Settings Index Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            if ($request->ajax()) {
                return response()->json(['error' => 'An error occurred: ' . $e->getMessage()], 500);
            }

            return redirect()->back()->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $forms = Form::active()->get(['id', 'title']);
        $endpointConfigurations = EndpointConfiguration::active()->get(['id', 'name', 'erp_selection', 'process_selection', 'endpoint_type']);
        $erpOptions = EndpointConfiguration::getErpOptions();
        $processOptions = EndpointConfiguration::getProcessOptions();
        $endpointTypeOptions = EndpointConfiguration::getEndpointTypeOptions();

        return view('admin.form-integration-settings.create', compact(
            'forms',
            'endpointConfigurations',
            'erpOptions',
            'processOptions',
            'endpointTypeOptions'
        ));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreFormIntegrationSettingRequest $request)
    {
        try {
            $data = $request->validated();
            $data['created_by'] = Auth::id();
            $data['updated_by'] = Auth::id();

            $integrationSetting = FormIntegrationSetting::create($data);

            // Validate the integration setting
            $validationErrors = $this->formIntegrationService->validateIntegrationSetting($integrationSetting);

            if (!empty($validationErrors)) {
                Log::warning('Form integration setting created with validation warnings', [
                    'integration_setting_id' => $integrationSetting->id,
                    'warnings' => $validationErrors
                ]);
            }

            return redirect()
                ->route('admin.form-integration-settings.index')
                ->with('success', 'Form integration setting created successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to create form integration setting', [
                'error' => $e->getMessage(),
                'request_data' => $request->validated()
            ]);

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to create form integration setting: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(FormIntegrationSetting $formIntegrationSetting)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $formIntegrationSetting->load(['form', 'endpointConfiguration', 'createdBy', 'updatedBy']);

        // Get validation status
        $validationErrors = $this->formIntegrationService->validateIntegrationSetting($formIntegrationSetting);

        return view('admin.form-integration-settings.show', compact('formIntegrationSetting', 'validationErrors'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(FormIntegrationSetting $formIntegrationSetting)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $formIntegrationSetting->load(['form', 'endpointConfiguration']);

        $forms = Form::active()->get(['id', 'title']);
        $endpointConfigurations = EndpointConfiguration::active()->get(['id', 'name', 'erp_selection', 'process_selection', 'endpoint_type']);
        $erpOptions = EndpointConfiguration::getErpOptions();
        $processOptions = EndpointConfiguration::getProcessOptions();
        $endpointTypeOptions = EndpointConfiguration::getEndpointTypeOptions();

        return view('admin.form-integration-settings.edit', compact(
            'formIntegrationSetting',
            'forms',
            'endpointConfigurations',
            'erpOptions',
            'processOptions',
            'endpointTypeOptions'
        ));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateFormIntegrationSettingRequest $request, FormIntegrationSetting $formIntegrationSetting)
    {
        try {
            $data = $request->validated();
            $data['updated_by'] = Auth::id();

            $formIntegrationSetting->update($data);

            // Validate the updated integration setting
            $validationErrors = $this->formIntegrationService->validateIntegrationSetting($formIntegrationSetting);

            if (!empty($validationErrors)) {
                Log::warning('Form integration setting updated with validation warnings', [
                    'integration_setting_id' => $formIntegrationSetting->id,
                    'warnings' => $validationErrors
                ]);
            }

            return redirect()
                ->route('admin.form-integration-settings.index')
                ->with('success', 'Form integration setting updated successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to update form integration setting', [
                'integration_setting_id' => $formIntegrationSetting->id,
                'error' => $e->getMessage(),
                'request_data' => $request->validated()
            ]);

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to update form integration setting: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FormIntegrationSetting $formIntegrationSetting)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        try {
            $formIntegrationSetting->delete();

            return redirect()
                ->route('admin.form-integration-settings.index')
                ->with('success', 'Form integration setting deleted successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to delete form integration setting', [
                'integration_setting_id' => $formIntegrationSetting->id,
                'error' => $e->getMessage()
            ]);

            return redirect()
                ->back()
                ->with('error', 'Failed to delete form integration setting: ' . $e->getMessage());
        }
    }

    /**
     * Get form fields for AJAX request
     */
    public function getFormFields(Request $request)
    {
        $formId = $request->input('form_id');

        if (!$formId) {
            return response()->json(['error' => 'Form ID is required'], 400);
        }

        try {
            $formFields = $this->formIntegrationService->getFormFields($formId);
            return response()->json(['fields' => $formFields]);

        } catch (\Exception $e) {
            Log::error('Failed to get form fields', [
                'form_id' => $formId,
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to get form fields'], 500);
        }
    }

    /**
     * Get endpoint fields for AJAX request
     */
    public function getEndpointFields(Request $request)
    {
        $endpointId = $request->input('endpoint_id');

        if (!$endpointId) {
            return response()->json(['error' => 'Endpoint ID is required'], 400);
        }

        try {
            $endpointFields = $this->formIntegrationService->getEndpointFields($endpointId);
            return response()->json(['fields' => $endpointFields]);

        } catch (\Exception $e) {
            Log::error('Failed to get endpoint fields', [
                'endpoint_id' => $endpointId,
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to get endpoint fields'], 500);
        }
    }

    /**
     * Get field mapping suggestions for AJAX request
     */
    public function getFieldMappingSuggestions(Request $request)
    {
        $formId = $request->input('form_id');
        $endpointId = $request->input('endpoint_id');

        if (!$formId || !$endpointId) {
            return response()->json(['error' => 'Both form ID and endpoint ID are required'], 400);
        }

        try {
            $formFields = $this->formIntegrationService->getFormFields($formId);
            $endpointFields = $this->formIntegrationService->getEndpointFields($endpointId);
            $suggestions = $this->formIntegrationService->suggestFieldMappings($formFields, $endpointFields);

            return response()->json([
                'form_fields' => $formFields,
                'endpoint_fields' => $endpointFields,
                'suggestions' => $suggestions
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get field mapping suggestions', [
                'form_id' => $formId,
                'endpoint_id' => $endpointId,
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to get field mapping suggestions'], 500);
        }
    }
}
