<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
    <title>FixedColumns example - Server-side processing</title>
    <link rel="stylesheet" type="text/css" href="../../../media/css/jquery.dataTables.css">
    <link rel="stylesheet" type="text/css" href="../css/dataTables.fixedColumns.css">
    <link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
    <link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
    <style type="text/css" class="init">
    /* Ensure that the demo table scrolls */
    
    th,
    td {
      white-space: nowrap;
    }
    
    div.dataTables_wrapper {
      width: 600px;
      margin: 0 auto;
    }
    /* Lots of padding for the cells as SSP has limited data in the demo */
    
    th,
    td {
      padding-left: 40px !important;
      padding-right: 40px !important;
    }
    </style>
    <script type="text/javascript" language="javascript" src="../../../media/js/jquery.js"></script>
    <script type="text/javascript" language="javascript" src="../../../media/js/jquery.dataTables.js"></script>
    <script type="text/javascript" language="javascript" src="../js/dataTables.fixedColumns.js"></script>
    <script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
    <script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>
    <script type="text/javascript" language="javascript" class="init">
    $(document).ready(function()
    {
      var table = $('#example').DataTable(
      {
        scrollY: "300px",
        scrollX: true,
        scrollCollapse: true,
        ajax: "../../../examples/server_side/scripts/server_processing.php",
        serverSide: true
      });
      new $.fn.dataTable.FixedColumns(table);
    });
    </script>
  </head>
  <body class="dt-example">
    <div class="container">
      <section>
        <h1>FixedColumns example <span>Server-side processing</span></h1>
        <div class="info">
          <p>This example shows how FixedColumns can be used with server-side processing in DataTables to cope with very large tables. No special considerations are required, just initialise FixedColumns as you normally would!</p>
          <p>Note that the table width is constrained in this example to allow scrolling to occur as the server-side processing data set has a limited number of columns in this demo!</p>
        </div>
        <table id="example" class="stripe row-border order-column" cellspacing="0" width="100%">
          <thead>
            <tr>
              <th>Name</th>
              <th>Position</th>
              <th>Office</th>
              <th>Extn.</th>
              <th>Start date</th>
              <th>Salary</th>
            </tr>
          </thead>
          <tfoot>
            <tr>
              <th>Name</th>
              <th>Position</th>
              <th>Office</th>
              <th>Extn.</th>
              <th>Start date</th>
              <th>Salary</th>
            </tr>
          </tfoot>
        </table>
        <ul class="tabs">
          <li class="active">Javascript</li>
          <li>HTML</li>
          <li>CSS</li>
          <li>Ajax</li>
          <li>Server-side script</li>
        </ul>
        <div class="tabs">
          <div class="js">
            <p>The Javascript shown below is used to initialise the table shown in this example:</p><code class="multiline language-js">$(document).ready(function() {
	var table = $('#example').DataTable( {
		scrollY:        &quot;300px&quot;,
		scrollX:        true,
		scrollCollapse: true,
		ajax: &quot;../../../examples/server_side/scripts/server_processing.php&quot;,
		serverSide: true
	} );
	new $.fn.dataTable.FixedColumns( table );
} );</code>
            <p>In addition to the above code, the following Javascript library files are loaded for use in this example:</p>
            <ul>
              <li><a href="../../../media/js/jquery.js">../../../media/js/jquery.js</a></li>
              <li><a href="../../../media/js/jquery.dataTables.js">../../../media/js/jquery.dataTables.js</a></li>
              <li><a href="../js/dataTables.fixedColumns.js">../js/dataTables.fixedColumns.js</a></li>
            </ul>
          </div>
          <div class="table">
            <p>The HTML shown below is the raw HTML table element, before it has been enhanced by DataTables:</p>
          </div>
          <div class="css">
            <div>
              <p>This example uses a little bit of additional CSS beyond what is loaded from the library files (below), in order to correctly display the table. The additional CSS used is shown below:</p><code class="multiline language-css">/* Ensure that the demo table scrolls */
	th, td { white-space: nowrap; }
	div.dataTables_wrapper {
		width: 600px;
		margin: 0 auto;
	}
	/* Lots of padding for the cells as SSP has limited data in the demo */
	th,
	td {
		padding-left: 40px !important;
		padding-right: 40px !important;
	}</code>
            </div>
            <p>The following CSS library files are loaded for use in this example to provide the styling of the table:</p>
            <ul>
              <li><a href="../../../media/css/jquery.dataTables.css">../../../media/css/jquery.dataTables.css</a></li>
              <li><a href="../css/dataTables.fixedColumns.css">../css/dataTables.fixedColumns.css</a></li>
            </ul>
          </div>
          <div class="ajax">
            <p>This table loads data by Ajax. The latest data that has been loaded is shown below. This data will update automatically as any additional data is loaded.
            </p>
          </div>
          <div class="php">
            <p>The script used to perform the server-side processing for this table is shown below. Please note that this is just an example script using PHP. Server-side processing scripts can be written in any language, using <a href="//datatables.net/manual/server-side">the protocol described in the DataTables
					documentation</a>.</p>
          </div>
        </div>
      </section>
    </div>
    <section>
      <div class="footer">
        <div class="gradient"></div>
        <div class="liner">
          <h2>Other examples</h2>
          <div class="toc">
            <div class="toc-group">
              <h3><a href="./index.html">Examples</a></h3>
              <ul class="toc active">
                <li><a href="./left_right_columns.html">Left and right fixed columns</a></li>
                <li><a href="./simple.html">Basic initialisation</a></li>
                <li><a href="./two_columns.html">Multiple fixed columns</a></li>
                <li><a href="./right_column.html">Right column only</a></li>
                <li><a href="./rowspan.html">Complex headers</a></li>
                <li><a href="./colvis.html">ColVis integration</a></li>
                <li class="active"><a href="./server-side-processing.html">Server-side processing</a></li>
                <li><a href="./css_size.html">CSS row sizing</a></li>
                <li><a href="./size_fixed.html">Assigned column width</a></li>
                <li><a href="./size_fluid.html">Fluid column width</a></li>
                <li><a href="./col_filter.html">Individual column filtering</a></li>
                <li><a href="./bootstrap.html">Bootstrap</a></li>
                <li><a href="./index_column.html">Index column</a></li>
              </ul>
            </div>
          </div>
          <div class="epilogue">
            <p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.
              <br> Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and <a href="http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of DataTables.</p>
            <p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015
              <br> DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
          </div>
        </div>
      </div>
    </section>
  </body>
</html>