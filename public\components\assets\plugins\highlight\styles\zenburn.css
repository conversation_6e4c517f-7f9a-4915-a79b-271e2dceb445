/*

Zenburn style from voldmar.ru (c) <PERSON> <<EMAIL>>
based on dark.css by <PERSON>

*/

.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  background: #3f3f3f;
  color: #dcdcdc;
  -webkit-text-size-adjust: none;
}

.hljs-keyword,
.hljs-tag,
.css .hljs-class,
.css .hljs-id,
.lisp .hljs-title,
.nginx .hljs-title,
.hljs-request,
.hljs-status,
.clojure .hljs-attribute {
  color: #e3ceab;
}

.django .hljs-template_tag,
.django .hljs-variable,
.django .hljs-filter .hljs-argument {
  color: #dcdcdc;
}

.hljs-number,
.hljs-date {
  color: #8cd0d3;
}

.dos .hljs-envvar,
.dos .hljs-stream,
.hljs-variable,
.apache .hljs-sqbracket {
  color: #efdcbc;
}

.dos .hljs-flow,
.diff .hljs-change,
.python .exception,
.python .hljs-built_in,
.hljs-literal,
.tex .hljs-special {
  color: #efefaf;
}

.diff .hljs-chunk,
.hljs-subst {
  color: #8f8f8f;
}

.dos .hljs-keyword,
.hljs-decorator,
.hljs-title,
.hljs-type,
.diff .hljs-header,
.ruby .hljs-class .hljs-parent,
.apache .hljs-tag,
.nginx .hljs-built_in,
.tex .hljs-command,
.hljs-prompt {
  color: #efef8f;
}

.dos .hljs-winutils,
.ruby .hljs-symbol,
.ruby .hljs-symbol .hljs-string,
.ruby .hljs-string {
  color: #dca3a3;
}

.diff .hljs-deletion,
.hljs-string,
.hljs-tag .hljs-value,
.hljs-preprocessor,
.hljs-pragma,
.hljs-built_in,
.hljs-javadoc,
.smalltalk .hljs-class,
.smalltalk .hljs-localvars,
.smalltalk .hljs-array,
.css .hljs-rules .hljs-value,
.hljs-attr_selector,
.hljs-pseudo,
.apache .hljs-cbracket,
.tex .hljs-formula,
.coffeescript .hljs-attribute {
  color: #cc9393;
}

.hljs-shebang,
.diff .hljs-addition,
.hljs-comment,
.hljs-annotation,
.hljs-template_comment,
.hljs-pi,
.hljs-doctype {
  color: #7f9f7f;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.5;
}

