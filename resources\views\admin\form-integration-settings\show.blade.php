@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title">Form Integration Setting Details</h3>
                        <div>
                            <a href="{{ route('admin.form-integration-settings.edit', $formIntegrationSetting->id) }}" class="btn btn-primary">
                                <i class="fa fa-edit"></i> Edit
                            </a>
                            <a href="{{ route('admin.form-integration-settings.index') }}" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> Back to List
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">Name:</th>
                                    <td>{{ $formIntegrationSetting->name }}</td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        @if($formIntegrationSetting->is_active)
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-secondary">Inactive</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Description:</th>
                                    <td>{{ $formIntegrationSetting->description ?: 'No description provided' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">Created By:</th>
                                    <td>{{ $formIntegrationSetting->createdBy->name ?? 'System' }}</td>
                                </tr>
                                <tr>
                                    <th>Created At:</th>
                                    <td>{{ $formIntegrationSetting->created_at->format('Y-m-d H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <th>Updated By:</th>
                                    <td>{{ $formIntegrationSetting->updatedBy->name ?? 'System' }}</td>
                                </tr>
                                <tr>
                                    <th>Updated At:</th>
                                    <td>{{ $formIntegrationSetting->updated_at->format('Y-m-d H:i:s') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Form Information -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5>Form Information</h5>
                        </div>
                        <div class="card-body">
                            @if($formIntegrationSetting->form)
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-borderless">
                                            <tr>
                                                <th width="30%">Form Title:</th>
                                                <td>{{ $formIntegrationSetting->form->title }}</td>
                                            </tr>
                                            <tr>
                                                <th>Form Status:</th>
                                                <td>
                                                    @if($formIntegrationSetting->form->is_active)
                                                        <span class="badge badge-success">Active</span>
                                                    @else
                                                        <span class="badge badge-warning">Inactive</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-borderless">
                                            <tr>
                                                <th width="30%">User Group:</th>
                                                <td>{{ $formIntegrationSetting->form->userGroup->name ?? 'No group assigned' }}</td>
                                            </tr>
                                            <tr>
                                                <th>Form Created:</th>
                                                <td>{{ $formIntegrationSetting->form->created_at->format('Y-m-d H:i:s') }}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>

                                <!-- Form Fields -->
                                <div class="mt-3">
                                    <h6>Form Fields</h6>
                                    <div class="row">
                                        @foreach($formIntegrationSetting->form_fields as $field)
                                            <div class="col-md-4 mb-2">
                                                <div class="border p-2 rounded">
                                                    <strong>{{ $field['label'] ?? $field['key'] }}</strong>
                                                    @if($field['required'] ?? false)
                                                        <span class="badge badge-danger">Required</span>
                                                    @endif
                                                    <br>
                                                    <small class="text-muted">Key: {{ $field['key'] }} | Type: {{ $field['type'] }}</small>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <i class="fa fa-exclamation-triangle"></i> The associated form is no longer available.
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Endpoint Configuration Information -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5>Endpoint Configuration Information</h5>
                        </div>
                        <div class="card-body">
                            @if($formIntegrationSetting->endpointConfiguration)
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-borderless">
                                            <tr>
                                                <th width="30%">Endpoint Name:</th>
                                                <td>{{ $formIntegrationSetting->endpointConfiguration->name }}</td>
                                            </tr>
                                            <tr>
                                                <th>ERP System:</th>
                                                <td>
                                                    <span class="badge badge-primary">{{ $formIntegrationSetting->endpointConfiguration->erp_selection }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>Process Type:</th>
                                                <td>
                                                    <span class="badge badge-info">{{ $formIntegrationSetting->endpointConfiguration->process_selection }}</span>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-borderless">
                                            <tr>
                                                <th width="30%">Endpoint Type:</th>
                                                <td>
                                                    <span class="badge badge-secondary">{{ $formIntegrationSetting->endpointConfiguration->endpoint_type }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>Endpoint Status:</th>
                                                <td>
                                                    @if($formIntegrationSetting->endpointConfiguration->is_active)
                                                        <span class="badge badge-success">Active</span>
                                                    @else
                                                        <span class="badge badge-warning">Inactive</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>URL:</th>
                                                <td><code>{{ $formIntegrationSetting->endpointConfiguration->url }}</code></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>

                                <!-- Endpoint Fields -->
                                <div class="mt-3">
                                    <h6>Endpoint Fields</h6>
                                    <div class="row">
                                        @foreach($formIntegrationSetting->endpoint_fields as $field)
                                            <div class="col-md-4 mb-2">
                                                <div class="border p-2 rounded">
                                                    <strong>{{ $field['name'] }}</strong>
                                                    @if($field['required'] ?? false)
                                                        <span class="badge badge-danger">Required</span>
                                                    @endif
                                                    @if($field['datatype'] ?? false)
                                                        <span class="badge badge-info">{{ $field['datatype'] }}</span>
                                                    @endif
                                                    <br>
                                                    <small class="text-muted">{{ $field['description'] ?? 'No description' }}</small>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <i class="fa fa-exclamation-triangle"></i> The associated endpoint configuration is no longer available.
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Field Mappings -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5>Field Mappings</h5>
                        </div>
                        <div class="card-body">
                            @if($formIntegrationSetting->field_mappings && count($formIntegrationSetting->field_mappings) > 0)
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Form Field</th>
                                                <th>Endpoint Field</th>
                                                <th>Form Field Type</th>
                                                <th>Endpoint Field Type</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($formIntegrationSetting->field_mappings as $formField => $endpointField)
                                                @php
                                                    $formFieldInfo = collect($formIntegrationSetting->form_fields)->firstWhere('key', $formField);
                                                    $endpointFieldInfo = collect($formIntegrationSetting->endpoint_fields)->firstWhere('name', $endpointField);
                                                @endphp
                                                <tr>
                                                    <td>
                                                        <strong>{{ $formFieldInfo['label'] ?? $formField }}</strong>
                                                        <br><small class="text-muted">{{ $formField }}</small>
                                                    </td>
                                                    <td>
                                                        <strong>{{ $endpointField }}</strong>
                                                        @if($endpointFieldInfo && ($endpointFieldInfo['required'] ?? false))
                                                            <span class="badge badge-danger">Required</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-secondary">{{ $formFieldInfo['type'] ?? 'Unknown' }}</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-info">{{ $endpointFieldInfo['datatype'] ?? 'Unknown' }}</span>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <i class="fa fa-exclamation-triangle"></i> No field mappings configured.
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Validation Status -->
                    @if(!empty($validationErrors))
                        <div class="card mt-4">
                            <div class="card-header bg-warning">
                                <h5 class="text-white">
                                    <i class="fa fa-exclamation-triangle"></i> Validation Issues
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <ul class="mb-0">
                                        @foreach($validationErrors as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="card mt-4">
                            <div class="card-header bg-success">
                                <h5 class="text-white">
                                    <i class="fa fa-check-circle"></i> Validation Status
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-success">
                                    <i class="fa fa-check"></i> This integration setting is valid and ready for use.
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
.badge {
    font-size: 0.75em;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
}

.card {
    box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
    margin-bottom: 1rem;
}
</style>
@endsection
