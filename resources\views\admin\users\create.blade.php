@extends('layouts.admin')
@section('pageTitle', 'Create User')

@section('content')
<div class="card card-default">
    <div class="card-header separator">
        <div class="card-title mainheading">
            <h4>Create User</h4>
        </div>
        <div class="card-controls">
            <ul>
                <li>
                    <a class="btn btn-secondary" href="{{ route('admin.users.index') }}">
                        Back to List
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <div class="card-block">
        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form method="POST" action="{{ route('admin.users.store') }}">
            @csrf
            
            <div class="form-group row">
                <label for="name" class="col-md-3 col-form-label">Name <span class="text-danger">*</span></label>
                <div class="col-md-9">
                    <input type="text" class="form-control" id="name" name="name" value="{{ old('name') }}" required>
                </div>
            </div>

            <div class="form-group row">
                <label for="email" class="col-md-3 col-form-label">Email <span class="text-danger">*</span></label>
                <div class="col-md-9">
                    <input type="email" class="form-control" id="email" name="email" value="{{ old('email') }}" required>
                </div>
            </div>

            <div class="form-group row">
                <label for="password" class="col-md-3 col-form-label">Password <span class="text-danger">*</span></label>
                <div class="col-md-9">
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>
            </div>

            <div class="form-group row">
                <label for="password_confirmation" class="col-md-3 col-form-label">Confirm Password <span class="text-danger">*</span></label>
                <div class="col-md-9">
                    <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                </div>
            </div>

            <div class="form-group row">
                <label for="type" class="col-md-3 col-form-label">Role <span class="text-danger">*</span></label>
                <div class="col-md-9">
                    <select class="form-control" id="type" name="type" required>
                        <option value="">Select Role</option>
                        <option value="1" {{ old('type') == '1' ? 'selected' : '' }}>Super Admin</option>
                        <option value="2" {{ old('type') == '2' ? 'selected' : '' }}>Admin</option>
                        <option value="3" {{ old('type') == '3' ? 'selected' : '' }}>User</option>
                    </select>
                </div>
            </div>

            <div class="form-group row">
                <label for="user_group_id" class="col-md-3 col-form-label">User Group</label>
                <div class="col-md-9">
                    <select class="form-control" id="user_group_id" name="user_group_id">
                        <option value="">Select User Group</option>
                        @foreach($userGroups as $id => $name)
                            <option value="{{ $id }}" {{ old('user_group_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="form-group row">
                <div class="col-md-9 offset-md-3">
                    <button type="submit" class="btn btn-primary">Create User</button>
                    <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">Cancel</a>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection
