<?php

namespace App\Adapters;

use App\Contracts\FormIntegrationAdapterInterface;
use App\Models\FormIntegrationSetting;

abstract class BaseFormIntegrationAdapter implements FormIntegrationAdapterInterface
{
    /**
     * Transform form data according to the integration setting
     */
    public function transformFormData(array $formData, FormIntegrationSetting $integrationSetting): array
    {
        $transformedData = [];
        $fieldMappings = $integrationSetting->field_mappings ?? [];
        $endpointFields = $integrationSetting->endpoint_fields;

        foreach ($fieldMappings as $formField => $endpointField) {
            if (isset($formData[$formField])) {
                $fieldDefinition = $this->getFieldDefinition($endpointField, $endpointFields);
                $transformedData[$endpointField] = $this->convertDataType($formData[$formField], $fieldDefinition);
            }
        }

        return $this->applyErpSpecificTransformations($transformedData, $integrationSetting);
    }

    /**
     * Validate the form data against the integration setting
     */
    public function validateFormData(array $formData, FormIntegrationSetting $integrationSetting): array
    {
        $errors = [];
        $fieldMappings = $integrationSetting->field_mappings ?? [];
        $endpointFields = $integrationSetting->endpoint_fields;

        foreach ($fieldMappings as $formField => $endpointField) {
            $fieldDefinition = $this->getFieldDefinition($endpointField, $endpointFields);
            
            if (!$fieldDefinition) {
                continue;
            }

            $value = $formData[$formField] ?? null;

            // Check required fields
            if (($fieldDefinition['required'] ?? false) && empty($value)) {
                $errors[] = "Field '{$formField}' is required for endpoint field '{$endpointField}'.";
                continue;
            }

            // Validate data type
            $typeValidation = $this->validateDataType($value, $fieldDefinition);
            if (!empty($typeValidation)) {
                $errors = array_merge($errors, $typeValidation);
            }

            // Apply ERP-specific validations
            $erpValidation = $this->validateErpSpecificRules($value, $fieldDefinition, $formField);
            if (!empty($erpValidation)) {
                $errors = array_merge($errors, $erpValidation);
            }
        }

        return $errors;
    }

    /**
     * Handle data type conversion based on endpoint field requirements
     */
    public function convertDataType($value, array $fieldDefinition)
    {
        if (empty($value) && $value !== 0 && $value !== '0') {
            return $fieldDefinition['default'] ?? null;
        }

        $dataType = $fieldDefinition['datatype'] ?? 'string';

        switch ($dataType) {
            case 'integer':
                return (int) $value;
            
            case 'float':
            case 'decimal':
                return (float) $value;
            
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            
            case 'string':
            default:
                $maxLength = $fieldDefinition['maxlength'] ?? null;
                $stringValue = (string) $value;
                
                if ($maxLength && strlen($stringValue) > $maxLength) {
                    return substr($stringValue, 0, $maxLength);
                }
                
                return $stringValue;
        }
    }

    /**
     * Prepare data for API submission
     */
    public function prepareApiData(array $transformedData, FormIntegrationSetting $integrationSetting): array
    {
        $endpointConfig = $integrationSetting->endpointConfiguration;
        
        $apiData = [
            'endpoint_url' => $endpointConfig->url,
            'endpoint_type' => $endpointConfig->endpoint_type,
            'erp_selection' => $endpointConfig->erp_selection,
            'process_selection' => $endpointConfig->process_selection,
            'data' => $transformedData,
            'metadata' => [
                'form_id' => $integrationSetting->form_id,
                'integration_setting_id' => $integrationSetting->id,
                'timestamp' => now()->toISOString(),
            ]
        ];

        return $this->applyErpSpecificApiPreparation($apiData, $integrationSetting);
    }

    /**
     * Get field definition from endpoint fields
     */
    protected function getFieldDefinition(string $fieldName, array $endpointFields): ?array
    {
        foreach ($endpointFields as $field) {
            if ($field['name'] === $fieldName) {
                return $field;
            }
        }
        return null;
    }

    /**
     * Validate data type against field definition
     */
    protected function validateDataType($value, array $fieldDefinition): array
    {
        $errors = [];
        $dataType = $fieldDefinition['datatype'] ?? 'string';
        $fieldName = $fieldDefinition['name'] ?? 'unknown';

        if (empty($value) && $value !== 0 && $value !== '0') {
            return $errors; // Skip validation for empty values
        }

        switch ($dataType) {
            case 'integer':
                if (!is_numeric($value) || (int) $value != $value) {
                    $errors[] = "Field '{$fieldName}' must be an integer.";
                }
                break;
            
            case 'float':
            case 'decimal':
                if (!is_numeric($value)) {
                    $errors[] = "Field '{$fieldName}' must be a number.";
                }
                break;
            
            case 'string':
                $maxLength = $fieldDefinition['maxlength'] ?? null;
                if ($maxLength && strlen((string) $value) > $maxLength) {
                    $errors[] = "Field '{$fieldName}' exceeds maximum length of {$maxLength} characters.";
                }
                break;
        }

        return $errors;
    }

    /**
     * Apply ERP-specific transformations (to be implemented by concrete adapters)
     */
    abstract protected function applyErpSpecificTransformations(array $transformedData, FormIntegrationSetting $integrationSetting): array;

    /**
     * Apply ERP-specific validation rules (to be implemented by concrete adapters)
     */
    abstract protected function validateErpSpecificRules($value, array $fieldDefinition, string $formField): array;

    /**
     * Apply ERP-specific API preparation (to be implemented by concrete adapters)
     */
    abstract protected function applyErpSpecificApiPreparation(array $apiData, FormIntegrationSetting $integrationSetting): array;
}
