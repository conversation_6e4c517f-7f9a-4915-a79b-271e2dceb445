<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="content-type" content="text/html;charset=UTF-8" />
        <meta charset="utf-8" />
        <title><?php echo e(trans('global.login')); ?> | <?php echo e(env("APP_NAME")); ?></title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no" />
        <link rel="apple-touch-icon" href="/components/pages/ico/60.png">
        <link rel="apple-touch-icon" sizes="76x76" href="/components/pages/ico/76.png">
        <link rel="apple-touch-icon" sizes="120x120" href="/components/pages/ico/120.png">
        <link rel="apple-touch-icon" sizes="152x152" href="/components/pages/ico/152.png">
        <link rel="icon" type="image/x-icon" href="favicon.ico" />
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-touch-fullscreen" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="default">
        <meta content="" name="description" />
        <meta content="" name="author" />
        <link href="/components/assets/plugins/pace/pace-theme-flash.css" rel="stylesheet" type="text/css" />
        <link href="/components/assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
        <link href="/components/assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css" />
        <link href="/components/assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css" media="screen" />
        <link href="/components/assets/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css" media="screen" />
        <link href="/components/assets/plugins/switchery/css/switchery.min.css" rel="stylesheet" type="text/css" media="screen" />
        <link href="/components/pages/css/pages-icons.css" rel="stylesheet" type="text/css">
        <link class="main-stylesheet" href="/components/pages/css/themes/corporate.css" rel="stylesheet" type="text/css" />
                <link href="/components/assets/css/style.css?ref=<?php echo e(rand(1,2000)); ?>" rel="stylesheet" type="text/css" />

        <script type="text/javascript">
            window.onload = function ()
            {
                // fix for windows 8
                if (navigator.appVersion.indexOf("Windows NT 6.2") != -1)
                    document.head.innerHTML += '<link rel="stylesheet" type="text/css" href="/components/pages/css/windows.chrome.fix.css" />'
            }
        </script>
    </head>
    <body class="fixed-header menu-pin menu-behind">
        <div class="login-wrapper ">
            <!-- START Login Background Pic Wrapper-->
            <div class="bg-pic">
                <!-- START Background Pic-->
                
                <!-- END Background Pic-->
                <!-- START Background Caption-->
                <div class="bg-caption pull-bottom sm-pull-bottom text-white p-l-20 m-b-20">
                    

                </div>
                <!-- END Background Caption-->
            </div>
            <!-- END Login Background Pic Wrapper-->
            <!-- START Login Right Container-->
            <div class="login-container bg-white">
                <div class="p-l-50 m-l-20 p-r-50 m-r-20 p-t-50 m-t-30 sm-p-l-15 sm-p-r-15 sm-p-t-40">
                    
                    <h5 class="p-t-35"><?php echo e(trans('global.login')); ?></h5>
                    <!-- START Login Form -->
                    <?php if(session()->has('message')): ?>
                    <p class="alert alert-info">
                        <?php echo e(session()->get('message')); ?>

                    </p>
                    <?php endif; ?>
                    <form id="form-login" class="p-t-15" method="POST" role="form" action="<?php echo e(route('login')); ?>">
                        <!-- START Form Control-->
                        <?php echo csrf_field(); ?>
                        <div class="form-group form-group-default">
                            <label><?php echo e(trans('global.login_email')); ?></label>
                            <div class="controls">
                                <input id="email" type="email" class="form-control<?php echo e($errors->has('email') ? ' is-invalid' : ''); ?>" required autocomplete="email" autofocus placeholder="<?php echo e(trans('global.login_email')); ?>" name="email" value="<?php echo e(old('email', null)); ?>">
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <label id="email-error" class="error" for="email"><?php echo e($message); ?></label>

                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <!-- END Form Control-->
                        <!-- START Form Control-->
                        <div class="form-group form-group-default">
                            <label><?php echo e(trans('global.login_password')); ?></label>
                            <div class="controls">
                                <input id="password" type="password" class="form-control<?php echo e($errors->has('password') ? ' is-invalid' : ''); ?>" name="password" required placeholder="<?php echo e(trans('global.login_password')); ?>">
                                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <label id="password-error" class="error" for="password"><?php echo e($message); ?></label>

                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <!-- START Form Control-->
                        <div class="row">
                            <div class="col-md-12 no-padding sm-p-l-10">
                                <div class="checkbox ">
                                    <input type="checkbox" name="remember" id="remember">

                                    <label for="checkbox1"><?php echo e(trans('global.remember_me')); ?></label>
                                </div>
                            </div>

                        </div>
                        <!-- END Form Control-->
                        <button type="submit" class="btn btn-lg btn-info  btn-cons m-t-10 btn-block btn-flat">

                            <?php echo e(trans('global.login')); ?>

                        </button>
                        <!--<a class="btn btn-success btn-cons m-t-10" href="/register" >Sign Up</a>-->

                    </form>
                    <?php if(Route::has('password.request')): ?>
                    <p class="mb-2 mt-2">
                        <a href="<?php echo e(route('password.request')); ?>">
                            <?php echo e(trans('global.forgot_password')); ?>

                        </a>
                    </p>
                    <?php endif; ?>
                    <p class="mb-2">
                        <a class="text-center" href="<?php echo e(route('register')); ?>">
                            <?php echo e(trans('global.register')); ?>

                        </a>
                    </p>
                    <!--END Login Form-->

                </div>
            </div>
            <!-- END Login Right Container-->
        </div>
        <!-- START OVERLAY -->

        <!-- END OVERLAY -->
        <!-- BEGIN VENDOR JS -->
        <script src="/components/assets/plugins/pace/pace.min.js" type="text/javascript"></script>
        <script src="/components/assets/plugins/jquery/jquery-1.11.1.min.js" type="text/javascript"></script>
        <script src="/components/assets/plugins/modernizr.custom.js" type="text/javascript"></script>
        <script src="/components/assets/plugins/jquery-ui/jquery-ui.min.js" type="text/javascript"></script>
        <script src="/components/assets/plugins/tether/js/tether.min.js" type="text/javascript"></script>
        <script src="/components/assets/plugins/bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
        <script src="/components/assets/plugins/jquery/jquery-easy.js" type="text/javascript"></script>
        <script src="/components/assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
        <script src="/components/assets/plugins/jquery-ios-list/jquery.ioslist.min.js" type="text/javascript"></script>
        <script src="/components/assets/plugins/jquery-actual/jquery.actual.min.js"></script>
        <script src="/components/assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js"></script>
        <script type="text/javascript" src="/components/assets/plugins/select2/js/select2.full.min.js"></script>
        <script type="text/javascript" src="/components/assets/plugins/classie/classie.js"></script>
        <script src="/components/assets/plugins/switchery/js/switchery.min.js" type="text/javascript"></script>
        <script src="/components/assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
        <!-- END VENDOR JS -->
        <script src="/components/pages/js/pages.min.js"></script>
        <script>
            $(function ()
            {
                $('#form-login').validate()
            })
        </script>
    </body>
</html><?php /**PATH D:\Git Data Capture\application\resources\views/auth/login.blade.php ENDPATH**/ ?>