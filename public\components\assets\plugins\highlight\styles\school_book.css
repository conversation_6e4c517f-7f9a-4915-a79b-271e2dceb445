/*

School Book style from goldblog.com.ua (c) <PERSON>aripov <PERSON> <<EMAIL>>

*/

.hljs {
  display: block;
  overflow-x: auto;
  padding: 15px 0.5em 0.5em 30px;
  font-size: 11px !important;
  line-height:16px !important;
  -webkit-text-size-adjust: none;
}

pre{
  background:#f6f6ae url(./school_book.png);
  border-top: solid 2px #d2e8b9;
  border-bottom: solid 1px #d2e8b9;
}

.hljs-keyword,
.hljs-literal,
.hljs-change,
.hljs-winutils,
.hljs-flow,
.nginx .hljs-title,
.tex .hljs-special {
  color:#005599;
  font-weight:bold;
}

.hljs,
.hljs-subst,
.hljs-tag .hljs-keyword {
  color: #3e5915;
}

.hljs-string,
.hljs-title,
.hljs-type,
.hljs-tag .hljs-value,
.css .hljs-rules .hljs-value,
.hljs-preprocessor,
.hljs-pragma,
.ruby .hljs-symbol,
.ruby .hljs-symbol .hljs-string,
.ruby .hljs-class .hljs-parent,
.hljs-built_in,
.django .hljs-template_tag,
.django .hljs-variable,
.smalltalk .hljs-class,
.hljs-javadoc,
.ruby .hljs-string,
.django .hljs-filter .hljs-argument,
.smalltalk .hljs-localvars,
.smalltalk .hljs-array,
.hljs-attr_selector,
.hljs-pseudo,
.hljs-addition,
.hljs-stream,
.hljs-envvar,
.apache .hljs-tag,
.apache .hljs-cbracket,
.nginx .hljs-built_in,
.tex .hljs-command,
.coffeescript .hljs-attribute {
  color: #2c009f;
}

.hljs-comment,
.hljs-annotation,
.hljs-decorator,
.hljs-template_comment,
.hljs-pi,
.hljs-doctype,
.hljs-deletion,
.hljs-shebang,
.apache .hljs-sqbracket {
  color: #e60415;
}

.hljs-keyword,
.hljs-literal,
.css .hljs-id,
.hljs-phpdoc,
.hljs-dartdoc,
.hljs-title,
.hljs-type,
.vbscript .hljs-built_in,
.rsl .hljs-built_in,
.smalltalk .hljs-class,
.xml .hljs-tag .hljs-title,
.diff .hljs-header,
.hljs-chunk,
.hljs-winutils,
.bash .hljs-variable,
.apache .hljs-tag,
.tex .hljs-command,
.hljs-request,
.hljs-status {
  font-weight: bold;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.5;
}
