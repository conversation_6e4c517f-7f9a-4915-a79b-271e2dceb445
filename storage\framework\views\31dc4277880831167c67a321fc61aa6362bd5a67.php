<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title">Form Integration Settings</h3>
                        <a href="<?php echo e(route('admin.form-integration-settings.create')); ?>" class="btn btn-primary">
                            <i class="fa fa-plus"></i> Create New Integration
                        </a>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo e($statistics['total_settings']); ?></h4>
                                            <p class="mb-0">Total Settings</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-cogs fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo e($statistics['active_settings']); ?></h4>
                                            <p class="mb-0">Active Settings</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo e($statistics['inactive_settings']); ?></h4>
                                            <p class="mb-0">Inactive Settings</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-pause-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo e(count($statistics['available_erp_systems'])); ?></h4>
                                            <p class="mb-0">ERP Systems</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-server fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ERP Distribution -->
                    <?php if(!empty($statistics['settings_by_erp'])): ?>
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>Settings by ERP System</h5>
                            <div class="row">
                                <?php $__currentLoopData = $statistics['settings_by_erp']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $erp => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h3 class="text-primary"><?php echo e($count); ?></h3>
                                            <p class="mb-0"><?php echo e($erp); ?> Integrations</p>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Data Table -->
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="form-integration-settings-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Form</th>
                                    <th>Endpoint</th>
                                    <th>ERP System</th>
                                    <th>Process Type</th>
                                    <th>Status</th>
                                    <th>Created By</th>
                                    <th>Created At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    $('#form-integration-settings-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?php echo e(route("admin.form-integration-settings.index")); ?>',
            type: 'GET'
        },
        columns: [
            { data: 'name', name: 'name' },
            { data: 'form_name', name: 'form.title' },
            { data: 'endpoint_name', name: 'endpointConfiguration.name' },
            { data: 'erp_system', name: 'endpointConfiguration.erp_selection' },
            { data: 'process_type', name: 'endpointConfiguration.process_selection' },
            { data: 'status', name: 'is_active', orderable: false, searchable: false },
            { data: 'created_by_name', name: 'createdBy.name' },
            { data: 'created_at', name: 'created_at' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[7, 'desc']],
        pageLength: 25,
        responsive: true,
        language: {
            processing: '<i class="fa fa-spinner fa-spin"></i> Loading...'
        }
    });

    // Handle delete confirmation
    $(document).on('click', '.delete-btn', function(e) {
        e.preventDefault();
        
        if (confirm('Are you sure you want to delete this form integration setting?')) {
            $(this).closest('form').submit();
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<style>
.card {
    box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
    margin-bottom: 1rem;
}

.bg-primary { background-color: #007bff !important; }
.bg-success { background-color: #28a745 !important; }
.bg-warning { background-color: #ffc107 !important; }
.bg-info { background-color: #17a2b8 !important; }

.table th {
    background-color: #f8f9fa;
    border-top: none;
}

.badge {
    font-size: 0.75em;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Git Data Capture\application\resources\views/admin/form-integration-settings/index.blade.php ENDPATH**/ ?>