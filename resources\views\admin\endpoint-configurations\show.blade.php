@extends('layouts.admin')
@section('pageTitle', 'View Endpoint Configuration')

@push('styles')
<style>
    .table th {
        width: 25%;
        background-color: #f8f9fa;
    }
    pre {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        max-height: 200px;
        overflow-y: auto;
    }
    .badge {
        font-size: 0.875rem;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card card-default">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4><i class="fa fa-eye"></i> {{ trans('global.show') }} {{ trans('cruds.endpointConfiguration.title_singular') }}</h4>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group" role="group">
                                <a class="btn btn-info" href="{{ route('admin.endpoint-configurations.edit', $endpointConfiguration->id) }}">
                                    <i class="fa fa-edit"></i> {{ trans('global.edit') }}
                                </a>
                                <a class="btn btn-secondary" href="{{ route('admin.endpoint-configurations.index') }}">
                                    <i class="fa fa-arrow-left"></i> {{ trans('global.back_to_list') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
            <table class="table table-bordered table-striped">
                <tbody>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.id') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->id }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.name') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->name }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.url') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->url }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.body_data_field') }}
                        </th>
                        <td>
                            @if($endpointConfiguration->body_data_field)
                                @php
                                    $fields = is_string($endpointConfiguration->body_data_field)
                                        ? json_decode($endpointConfiguration->body_data_field, true)
                                        : $endpointConfiguration->body_data_field;
                                @endphp
                                @if(is_array($fields) && count($fields) > 0)
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead class="thead-light">
                                                <tr>
                                                    <th>Field Name</th>
                                                    <th>Data Type</th>
                                                    <th>Max Length</th>
                                                    <th>Required</th>
                                                    <th>Default</th>
                                                    <th>Description</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($fields as $field)
                                                    <tr>
                                                        <td><strong>{{ $field['name'] ?? 'N/A' }}</strong></td>
                                                        <td><span class="badge badge-info">{{ $field['datatype'] ?? 'string' }}</span></td>
                                                        <td>{{ $field['maxlength'] ?? 'N/A' }}</td>
                                                        <td>
                                                            <span class="badge badge-{{ ($field['required'] ?? false) ? 'danger' : 'secondary' }}">
                                                                {{ ($field['required'] ?? false) ? 'Yes' : 'No' }}
                                                            </span>
                                                        </td>
                                                        <td>{{ $field['default'] ?? 'N/A' }}</td>
                                                        <td>{{ $field['description'] ?? 'N/A' }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <span class="text-muted">No field mapping configured</span>
                                @endif
                            @else
                                <span class="text-muted">No field mapping configured</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.erp_selection') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->erp_selection }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.process_selection') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->process_selection }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.endpoint_type') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->endpoint_type }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.is_active') }}
                        </th>
                        <td>
                            <span class="badge badge-{{ $endpointConfiguration->is_active ? 'success' : 'danger' }}">
                                {{ $endpointConfiguration->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.created_at') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->created_at }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.updated_at') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->updated_at }}
                        </td>
                    </tr>
                </tbody>
            </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
