@extends('layouts.admin')
@section('pageTitle', 'View Endpoint Configuration')

@section('content')
<div class="card card-default">
    <div class="card-header">
        <h4>{{ trans('global.show') }} {{ trans('cruds.endpointConfiguration.title_singular') }}</h4>
    </div>
    <div class="card-body">
        <div class="form-group">
            <div class="form-group">
                <a class="btn btn-default" href="{{ route('admin.endpoint-configurations.index') }}">
                    {{ trans('global.back_to_list') }}
                </a>
            </div>
            <table class="table table-bordered table-striped">
                <tbody>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.id') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->id }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.name') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->name }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.url') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->url }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.body_data_field') }}
                        </th>
                        <td>
                            <pre>{{ $endpointConfiguration->body_data_field }}</pre>
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.erp_selection') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->erp_selection }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.process_selection') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->process_selection }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.endpoint_type') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->endpoint_type }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.is_active') }}
                        </th>
                        <td>
                            <span class="badge badge-{{ $endpointConfiguration->is_active ? 'success' : 'danger' }}">
                                {{ $endpointConfiguration->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.created_at') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->created_at }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.updated_at') }}
                        </th>
                        <td>
                            {{ $endpointConfiguration->updated_at }}
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="form-group">
                <a class="btn btn-default" href="{{ route('admin.endpoint-configurations.index') }}">
                    {{ trans('global.back_to_list') }}
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
