<div class="btn-group" role="group">
    <a class="btn btn-sm btn-primary" title="View" href="<?php echo e(route('admin.endpoint-configurations.show', $row->id)); ?>">
        <i class="fa fa-eye"></i>
    </a>
    <a class="btn btn-sm btn-info" title="Edit" href="<?php echo e(route('admin.endpoint-configurations.edit', $row->id)); ?>">
        <i class="fa fa-edit"></i>
    </a>
    <form class="d-inline" action="<?php echo e(route('admin.endpoint-configurations.destroy', $row->id)); ?>" method="POST">
        <?php echo csrf_field(); ?>
        <?php echo method_field('DELETE'); ?>
        <button type="button" title="Delete" class="btn btn-sm btn-danger delete-confirm" data-id="<?php echo e($row->id); ?>">
            <i class="fa fa-trash"></i>
        </button>
    </form>
</div>

<?php $__env->startPush('scripts'); ?>
<?php echo \Illuminate\View\Factory::parentPlaceholder('scripts'); ?>
<script>
    $(document).ready(function() {
        $('.delete-confirm').on('click', function(e) {
            e.preventDefault();
            const id = $(this).data('id');
            const form = $(this).closest('form');
            
            swal({
                title: "Are you sure?",
                text: "This endpoint configuration will be permanently deleted!",
                icon: "warning",
                buttons: ["Cancel", "Yes, delete it!"],
                dangerMode: true,
            }).then((willDelete) => {
                if (willDelete) {
                    form.submit();
                }
            });
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\Git Data Capture\application\resources\views/admin/endpoint-configurations/partials/actions.blade.php ENDPATH**/ ?>