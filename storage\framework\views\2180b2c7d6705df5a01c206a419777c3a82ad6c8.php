<?php if($message = Session::get('success')): ?>
<div class="alert alert-success sucess_notif">
    <button class="close" data-dismiss="alert"></button>
    <p><?php echo e($message); ?></p>
</div>
<?php endif; ?>
<?php if($warningSticky = Session::get('warningSticky')): ?>

<div class="alert alert-warning">
    <button class="close" data-dismiss="alert"></button>
    <p><?php echo e($warningSticky); ?></p>
</div>
<?php endif; ?>
<?php if(count($errors) > 0): ?>
<div class="alert alert-danger">
    <button class="close" data-dismiss="alert"></button>
    <strong>Whoops!</strong> There were some problems with your input.<br><br>
    <ul>
        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li><?php echo e($error); ?></li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>
</div>
<?php endif; ?><?php /**PATH D:\Git Data Capture\application\resources\views/partials/alerts.blade.php ENDPATH**/ ?>