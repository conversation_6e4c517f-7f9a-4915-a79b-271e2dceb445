@extends('layouts.admin')
@section('pageTitle', 'User Groups')

@section('styles')
    @parent
@endsection

@section('content')
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>User Groups List</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <a class="btn btn-success" href="{{ route('admin.usergroups.create') }}">
                            Add User Group
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="card-block">
            <div class="table-responsive">
                <table class="table table-bordered table-striped table-hover datatable" id="usergroups-table">
                    <thead>
                        <tr>
                            <th width="10">
                                <input type="checkbox" id="select-all">
                            </th>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Users Count</th>
                            <th>Status</th>
                            <th>Created At</th>
                            <th width="150">Action</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    @parent

    <script>
        $(function() {
            var table = $('#usergroups-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: "{{ route('admin.usergroups.index') }}",
                columns: [{
                        data: 'checkbox',
                        name: 'checkbox',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {
                        data: 'description',
                        name: 'description'
                    },
                    {
                        data: 'users_count',
                        name: 'users_count'
                    },
                    {
                        data: 'status',
                        name: 'is_active'
                    },
                    {
                        data: 'created_at',
                        name: 'created_at'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                order: [
                    [5, 'desc']
                ],
                pageLength: 25,
                responsive: true,
                dom: 'Bfrtip',
                buttons: [
                    buttonsDefault,
                    {
                        text: 'Delete',
                        className: 'btn btn-danger',
                        action: function(e, dt, node, config) {
                            var checkedBoxes = $(
                                '#usergroups-table tbody input[type="checkbox"]:checked');
                            var ids = [];

                            checkedBoxes.each(function() {
                                ids.push($(this).val());
                            });

                            if (ids.length === 0) {
                                alert('Please select records to delete');
                                return;
                            }

                            if (confirm('Are you sure you want to delete selected records?')) {
                                $.ajax({
                                        headers: {
                                            'x-csrf-token': $('meta[name="csrf-token"]').attr(
                                                'content')
                                        },
                                        method: 'POST',
                                        url: "{{ route('admin.usergroups.massDestroy') }}",
                                        data: {
                                            ids: ids,
                                            _method: 'DELETE'
                                        }
                                    })
                                    .done(function() {
                                        location.reload();
                                    })
                                    .fail(function(xhr) {
                                        alert('Error deleting records');
                                    });
                            }
                        }
                    }
                ]
            });

            // Handle select all checkbox
            $('#select-all').on('click', function() {
                var rows = table.rows({
                    'search': 'applied'
                }).nodes();
                $('input[type="checkbox"]', rows).prop('checked', this.checked);
            });
        });
    </script>
@endsection
