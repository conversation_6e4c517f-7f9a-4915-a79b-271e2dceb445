# Form Integration Settings - User Guide

## 📋 Overview

The Form Integration Settings feature allows you to connect forms with ERP system endpoints, enabling automatic data submission from form responses to your ERP systems (CSI, SAP, etc.).

## 🚀 Getting Started

### Prerequisites

1. **Forms**: You need at least one active form in the system
2. **Endpoint Configurations**: You need at least one active endpoint configuration
3. **Permissions**: Admin or Super Admin access (currently all authenticated users have access)

### Accessing Form Integration Settings

1. Log into the admin panel
2. Navigate to **Form Integration Settings** in the sidebar menu
3. You'll see the main dashboard with statistics and a list of existing integrations

## 📊 Dashboard Overview

The main dashboard shows:

- **Total Settings**: Number of integration settings created
- **Active Settings**: Number of currently active integrations
- **Inactive Settings**: Number of disabled integrations
- **ERP Systems**: Number of different ERP systems configured
- **Settings by ERP**: Breakdown of integrations per ERP system

## ➕ Creating a New Integration

### Step 1: Basic Information

1. Click **"Create New Integration"** button
2. Fill in the basic information:
   - **Integration Name**: A descriptive name for this integration
   - **Status**: Active or Inactive
   - **Description**: Optional description of what this integration does

### Step 2: Select Form

1. Choose a form from the **"Select Form"** dropdown
2. The system will automatically load and display the form's fields
3. Only active forms are available for selection

### Step 3: Select Endpoint Configuration

1. Use the filters to narrow down endpoint options:
   - **ERP System**: Filter by CSI, SAP, etc.
   - **Process Type**: Filter by Misc Issue, Misc Receipt, Quantity Move
   - **Endpoint Type**: Filter by API or Stored Procedure

2. Select an endpoint configuration from the filtered list
3. The system will automatically load and display the endpoint's fields

### Step 4: Configure Field Mappings

Once both form and endpoint are selected, the field mapping section appears:

#### Automatic Suggestions
1. Click **"Suggest Mappings"** to get AI-powered field mapping suggestions
2. The system will automatically match fields based on names and descriptions
3. Review and adjust the suggestions as needed

#### Manual Mapping
1. Click **"Add Mapping"** to create a new field mapping
2. Select a form field from the left dropdown
3. Select the corresponding endpoint field from the right dropdown
4. Repeat for all required fields

#### Field Information
- **Form Fields**: Shows field name, type, and whether it's required
- **Endpoint Fields**: Shows field name, data type, and whether it's required
- **Required Fields**: Fields marked with red "Required" badges must be mapped

### Step 5: Save Integration

1. Review all mappings to ensure they're correct
2. Click **"Create Integration Setting"** to save
3. The system will validate all mappings before saving

## ✏️ Editing an Integration

1. From the main list, click the **Edit** button (pencil icon) for any integration
2. Modify any of the settings using the same interface as creation
3. The field mappings will be preserved and displayed
4. Click **"Update Integration Setting"** to save changes

## 👁️ Viewing Integration Details

1. Click the **View** button (eye icon) to see detailed information about an integration
2. The detail view shows:
   - Basic integration information
   - Form details and fields
   - Endpoint configuration details and fields
   - Complete field mapping table
   - Validation status

### Validation Status

The system automatically validates each integration and shows:
- ✅ **Valid**: Integration is properly configured and ready to use
- ⚠️ **Warnings**: Issues that should be addressed but don't prevent usage
- ❌ **Errors**: Critical issues that prevent the integration from working

## 🗑️ Deleting an Integration

1. Click the **Delete** button (trash icon) for any integration
2. Confirm the deletion in the popup dialog
3. The integration will be soft-deleted (can be recovered if needed)

## 🔍 Filtering and Searching

### DataTable Features
- **Search**: Use the search box to find integrations by name, form, or endpoint
- **Sorting**: Click column headers to sort by any field
- **Pagination**: Navigate through multiple pages of integrations
- **Per Page**: Adjust how many integrations are shown per page

### Status Filtering
- Filter integrations by active/inactive status
- View creation and modification dates
- See who created or last modified each integration

## 🎯 Best Practices

### Naming Conventions
- Use descriptive names that indicate the form and ERP system
- Example: "Inventory Issue Form - CSI Integration"
- Include the process type if multiple processes are used

### Field Mapping Guidelines
1. **Map Required Fields First**: Ensure all required endpoint fields are mapped
2. **Data Type Compatibility**: Check that form field types match endpoint expectations
3. **Test with Sample Data**: Verify mappings work with actual form submissions
4. **Document Complex Mappings**: Use the description field for complex integrations

### Maintenance
1. **Regular Reviews**: Periodically review integrations for accuracy
2. **Update After Changes**: Update mappings when forms or endpoints change
3. **Monitor Status**: Keep integrations active only when needed
4. **Backup Configurations**: Document important field mappings externally

## 🚨 Troubleshooting

### Common Issues

#### "Form field does not exist"
- **Cause**: The form has been modified and the field was removed
- **Solution**: Update the field mapping or recreate the integration

#### "Endpoint field does not exist"
- **Cause**: The endpoint configuration has been modified
- **Solution**: Check the endpoint configuration and update mappings

#### "Required endpoint field not mapped"
- **Cause**: A required endpoint field has no corresponding form field mapped
- **Solution**: Map a form field to the required endpoint field or make the endpoint field optional

#### Integration appears inactive
- **Cause**: Either the form or endpoint configuration is inactive
- **Solution**: Activate the form and endpoint configuration, or update the integration

### Getting Help

1. **Validation Messages**: Check the validation status on the detail view
2. **Error Logs**: Contact system administrator for detailed error logs
3. **Documentation**: Refer to the technical specification for advanced configuration
4. **Support**: Contact the development team for complex integration issues

## 📈 Monitoring Integration Performance

### Statistics Dashboard
- Monitor the number of active integrations
- Track integrations by ERP system
- Review creation and modification patterns

### Integration Health
- Regularly check integration validation status
- Monitor for inactive forms or endpoints
- Update integrations when underlying systems change

## 🔄 Future Enhancements

The Form Integration Settings feature is designed to be extensible:

- **Additional ERP Systems**: New ERP adapters can be added
- **Advanced Field Transformations**: Complex data transformations
- **Real-time Monitoring**: Live integration status monitoring
- **Batch Processing**: Handle multiple form submissions efficiently
- **Integration Testing**: Built-in testing tools for validations

## 📞 Support

For technical support or feature requests:
- Contact the system administrator
- Refer to the technical documentation
- Submit feature requests through the appropriate channels

This user guide provides comprehensive instructions for using the Form Integration Settings feature effectively.
