/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
(function ($) {
    $.mlp = { x: 0, y: 0 }; // Mouse Last Position
    function documentHandler() {
        var $current = this === document ? $(this) : $(this).contents();
        $current.mousemove(function (e) { jQuery.mlp = { x: e.pageX, y: e.pageY } });
        $current.find("iframe").load(documentHandler);
    }
    $(documentHandler);
    $.fn.ismouseover = function (overThis) {
        var result = false;
        this.eq(0).each(function () {
            var $current = $(this).is("iframe") ? $(this).contents().find("body") : $(this);
            var offset = $current.offset();
            result = offset.left <= $.mlp.x && offset.left + $current.outerWidth() > $.mlp.x &&
                offset.top <= $.mlp.y && offset.top + $current.outerHeight() > $.mlp.y;
        });
        return result;
    };
})(jQuery);
if (!!window.performance && window.performance.navigation.type === 2) {
    // value 2 means "The page was accessed by navigating into the history"
    console.log('Reloading');
    window.location.reload(); // reload whole page

}


window.onpageshow = function (event) {
    if (event.persisted) {
        window.location.reload(true);
    }
};

jQuery.fn.serializeObject = function () {
    var formData = {};
    var formArray = this.serializeArray();


    for (var i = 0, n = formArray.length; i < n; ++i)
        formData[formArray[i].name] = formArray[i].value;
    return formData;
};
function clearDatatableFilters() {

    if ($(".dt-checkboxes-cell input:checked").val() == "on") {
        // $(".dt-checkboxes-cell input:checked").prop('checked', false).trigger('change');
    }
    $('.searchDataTableParam:not(".select2")').val("").trigger('change');

    $('.searchDataTable').val("").trigger('change');
    $('.searchDataTableParam .select2_ajax').each(function () {
        var attr = $(this).attr('multiple');

        // For some browsers, `attr` is undefined; for others,
        // `attr` is false.  Check for both.
        // console.log(attr);
        if (typeof attr !== 'undefined' && attr !== false) {
            // ...
            $(this).val([]).trigger('change');

        } else {
            $(this).val("").trigger('change');

        }
    })
    $('.searchDataTableParam.select2_ajax').each(function () {
        var attr = $(this).attr('multiple');
        // console.log(attr);
        // For some browsers, `attr` is undefined; for others,
        // `attr` is false.  Check for both.
        if (typeof attr !== 'undefined' && attr !== false) {
            // ...
            $(this).val([]).trigger('change');

        } else {
            $(this).val("").trigger('change');

        }
    })
    $('.searchDataTableParam.select2').each(function () {
        var attr = $(this).attr('multiple');

        // For some browsers, `attr` is undefined; for others,
        // `attr` is false.  Check for both.
        if (typeof attr !== 'undefined' && attr !== false) {
            // ...
            $(this).val([]).trigger('change');

        } else {
            $(this).val("").trigger('change');

        }
    })
    $('.searchDataTable.select2').each(function () {
        var attr = $(this).attr('multiple');

        // For some browsers, `attr` is undefined; for others,
        // `attr` is false.  Check for both.
        if (typeof attr !== 'undefined' && attr !== false) {
            // ...
            $(this).val([]).trigger('change');

        } else {
            $(this).val("").trigger('change');

        }
    })
    $(".searchDataTableParam")[0].focus();
}
var generatePDFCustom = function (title, control_number) {
    const dateField = new Date().toLocaleDateString("en-US");
    const dateGenerated = new Date().toLocaleString();
    var newPdf = new jsPDF({
        unit: 'pt',
        orientation: 'landscape',
    });
    //    var control_number = $("#invoice_number").val();
    // first row
    newPdf.setFontType('bold');
    newPdf.text('HIGHLAND Tractor Parts Inc.', 16, 25);

    if (typeof control_number != "undefined") {
        newPdf.setFontSize(14);
        newPdf.setFontType('normal');

        newPdf.text('CONTROL NO. ' + control_number, 620, 25);
    }


    // 2nd 3rd row
    newPdf.setFontSize(12);
    newPdf.text('180 4th Avenue (West)', 16, 41);
    newPdf.text('Caloocan City Tel No(s). 361-9692', 16, 57);


    newPdf.setFontSize(14);
    newPdf.setFontType('bold');
    newPdf.text(title, 380, 80);

    return newPdf;
}
var priceFormateToSubmit = function () {
    $(".price_formate").each(function () {
        var val = $(this).val();
        if (val != '') {
            var formated = val.replace(/,/g, '');
            $(this).val(formated);
        }
    });
}
var generatePDF = function (title, control_number) {
    const dateField = new Date().toLocaleDateString("en-US");
    console.log(title, control_number);
    const dateGenerated = new Date().toLocaleString();
    var newPdf = new jsPDF({
        unit: 'pt',
        orientation: 'landscape',
    });
    var control_number = $("#invoice_number").val();
    // first row
    newPdf.setFontType('bold');
    newPdf.text('HIGHLAND Tractor Parts Inc.', 16, 25);

    if (typeof control_number != "undefined") {
        newPdf.setFontSize(14);
        newPdf.setFontType('normal');

        newPdf.text('CONTROL NO. ' + control_number, 620, 25);
    }


    // 2nd 3rd row
    newPdf.setFontSize(12);
    newPdf.text('180 4th Avenue (West)', 16, 41);
    newPdf.text('Caloocan City Tel No(s). 361-9692', 16, 57);

    newPdf.text('DATE: ' + dateField, 620, 41);

    newPdf.setFontSize(14);
    newPdf.setFontType('bold');
    newPdf.text(title, 380, 80);

    return newPdf;
}
var loading_duration = 500;
jQuery(".preloader-outer").delay(loading_duration).fadeOut();
jQuery(".pins").delay(loading_duration).fadeOut("slow");

//$('.numeric').autoNumeric({
//    allowDecimalPadding: "never"
//});
$(".price_input");
$(document).on("input", ".price_input", function () {
    var enterd_val = this.value.replace(/[^0-9\.]/g, '');
    if (this.value != enterd_val) {
        this.value = enterd_val;
    }
}
);
// $(document).on("click", ".dt-buttons .btn,:submit", function () {
// console.log("1");
$(document).on("click", ".dt-buttons .btn,:submit:not('.compose_submit')", function () {

    var aaa = $(this);
    setTimeout(function () {
        aaa.prop('disabled', true);
    }, 100);

    setTimeout(function () {
        aaa.prop('disabled', false);
    }, 2000);

});
function focusNext(next) {
    var nextTagName = $(next)[0].tagName;
    // console.log(nextTagName);
    setTimeout(() => {
        $(next).focus();
        var nextTagName = $(next)[0].tagName;

        if (nextTagName == "SELECT") {
            $(next).select2('open');

        }
        if (nextTagName == "A" || nextTagName == "BUTTON") {
            $(next).click();

        }
    }, 500);

}

function moveNextElem(elem, next) {
    // console.log(elem,next);
    if ($(elem).length == 0)
        return;
    var tag_name = $(elem)[0].tagName;

    if (tag_name == "SELECT") {
        $(elem).on('select2:select', function (e) {

            focusNext(next);
            // return false;
        })

    }
    $(elem).on('keypress', function (e) {
        var keyCode = e.keyCode || e.which;
        if (keyCode === 13) {
            e.preventDefault();

            focusNext(next);
            // return false;
        }
    });
}
function moveNext(event, id) {
    var whichKey = event.which || event.keyCode;

    if (whichKey == 13) {
        console.log(id);
        event.preventDefault();
        $("#" + id).focus();

        if (id == 'addTransferItem') {
            console.log('button')
            $('#addTransferItem').click();
        }
        if (id == 'customer') {
            $("#customer").select2('open');
        }
        if (id == 'part_number_id_2') {
            $("#part_number_id_2").select2('open');
        }
    }
}
$(document).on("keyup keypress", ".numeric", function () {
    this.value = this.value.replace(/\D/g, '');

});

$(document).on("keyup keypress", ".ref_number", function () {
    //    var numeric_part = this.value.replace(/\D/g, '');
    //    var alpha_part = this.value.replace(/\D/g, '');
    //    console.log(numeric_part);

});

$('._select2').each(function () {
    //console.log($(this).attr('placeholder'));
    $(this).select2({
        width: '100% ',
        placeholder: $(this).attr('placeholder'),
        //        allowClear: true

    });
});
$('._select2_purpose').each(function () {
    //console.log($(this).attr('placeholder'));
    $(this).select2({
        width: '100% ',
        placeholder: $(this).attr('placeholder'),
        //        allowClear: true

    });
});
$(".filterBlockd:nth-child(1n)").accordion({
    collapsible: true,
    autoHeight: false
    //        active: false
});

$('a[data-toggle="tab"]').on('shown.bs.tab click', function (e) {
    //    $(".filterBlock").accordion("resize");
    //    $(".filterBlock").accordion({
    //        collapsible: true,
    //        autoHeight: false
    ////        active: false
    //    });
    //    console.log('as');



    $($.fn.dataTable.tables(true)).DataTable()
        .columns.adjust();
    //            datatableNisl.columns.adjust();
});
$(document).on('change apply.daterangepicker', '.searchDataTable', function () {

    // console.log('as');
    let strict = $(this).attr('strict') || false
    let index = $(this).data('column');
    let value = strict && this.value ? "^" + this.value + "$" : this.value;
    var search = $.fn.dataTable.util.throttle(function (val) {
        table
            .column(index)
            .search(val, false, false, true)
            .draw();
    });
    search(value);
});

jQuery(document).on('keyup', function (evt) {
    if (evt.keyCode == 27) {
        // alert('Esc key pressed.');
        // $(".clearFilters").click();
        clearDatatableFilters();
        $('.modal .close').click();
        $(".searchDataTableParam")[0].focus()
        // console.log("esc")

    }
});
$(document).on("keydown", function (e) {
    if ((e.keyCode == 10 || e.keyCode == 13) && e.ctrlKey) {
        $(".searchDataTableParam")[0].select()
        $(".searchDataTableParam")[0].focus()
    }
});
$('.searchDataTableParam').keypress(function (e) {
    if (e.which == 13) {
        table.draw();
        var elm = $(this);
        setTimeout(function () {
            $(elm).blur()
        }, 500)

        return false;    //<---- Add this line
    }
});
$(document).on('keypress', '.select2-container', function (e) {

    // console.log("as")
    if (e.which == 13) {
        table.draw();
        var elm = $(this);
        setTimeout(function () {
            $(elm).blur()
        }, 500)
        return false;    //<---- Add this line
    }
});

$(document).on('apply.daterangepicker', '.searchDataTableParam', function () {
    // console.log("sas");
    table.draw();
    var elm = $(this);
    setTimeout(function () {
        $(elm).blur()
    }, 500)
    //    $($.fn.dataTable.tables(true)).draw();

});
$('.datatable').on('dblclick', 'tr', function (e) {
    e.stopPropagation()
    let editLink = $(this).find('.editLink').attr('href')
    if (typeof editLink != "undefined") {
        return window.location.href = editLink;

    }
    return true;
})
$(function () {
    $(".filterBlock").wrap("<div class='searchFiltersForm'></div>");
    $(".filterBlockd").wrap("<div class='searchFiltersForm'></div>");

    $(".filterBlock").accordion({
        collapsible: true,
        autoHeight: false
        //        active: false
    });
    $(document).on("change", '.currency_trigger', function () {
        var currency = $(this).val();
        var selector = $(this).data('selector');
        var target = $(this).data('target');

        if ($(selector).length && $(target).length) {
            var val = $(selector).val();
            var val_conv = intVal(val);
            //           
            currency_rate = currency_rates[currency] ? currency_rates[currency] : 1;
            // console.log(currency_rate);
            $(target).val(val_conv * currency_rate);
        }

    });
    $(".currency_trigger").change();
    $(".currencies").change();

    // old records

    // $(document).on("change", '.currencies', function () {

    //     var val = $(this).val();
    //     var selector = $(this).data('selector');
    //     var target = $(this).data('target');

    //     if ($(selector).length && $(target).length) {
    //         var currency = $(selector).val();
    //         var val_conv = intVal(val);
    //         console.log(val_conv);
    //         currency_rate = currency_rates[currency] ? currency_rates[currency] : 1;
    //         $(target).val(val_conv * currency_rate);
    //     }

    // });


    $(document).on("change", '.quantity_currency', function () {

        var parent = $(this).parents('tr');
        var quantity = $(this).val();

        var selector = $(this).data('selector');

        var target = $(this).data('target');

        var source = $(this).data('source');
        //        var quantity = $(this).data('quantity');


        if ($(selector).length && $(target).length && $(source).length) {
            var currency = $(selector).val();
            //            console.log(currency);
            var val = $(source).val();
            var val_conv = intVal(val);
            //            console.log(val_conv);
            qt = parseFloat(quantity)

            currency_rate = currency_rates[currency] ? currency_rates[currency] : 1;
            //            console.log(qt, val_conv, currency, currency_rate)
            var amount = val_conv * qt * currency_rate;
            amount = amount.toFixed(2);
            $(target).val(amount);
        }

    });
    $(document).on("focus", '.currencies', function () {
        if ("0.00" == $(this).val() || "0.0000" == $(this).val()) {
            $(this).val("");
        }
    });
    $(document).on("change", '.quantityd:not(".sale_invoice_item_input")', function () {
        var parent = $(this).parents('tr');
        var quantity = $(this).val();
        var item_id = $(parent).data('id');
        // console.log(item_id, quantity);
        var token = $('meta[name="csrf-token"]').attr('content');

        $.ajax({
            type: "POST",
            url: updateQtyUrl,
            data: {
                quantity: quantity,
                item_id: item_id,
                _token: token,
                module_type: $("#module_type").val(),
                pm: $("#pm").val(),
            }, // serializes the form's elements.
            success: function (data) {
                //console.log(data);


            }
        });

    });
    $(document).on("change", '.currencies', function () {

        var parent = $(this).parents('tr');
        var val = $(this).val();
        // console.log(val);
        var selector = $(this).data('selector');

        var target = $(this).data('target');

        var quantity = $(parent).find('.quantity').val();
        //        var quantity = $(this).data('quantity');


        if ($(selector).length && $(target).length && quantity) {
            var currency = $(selector).val();
            //            console.log(currency);
            var val_conv = intVal(val);
            //            console.log(val_conv);
            qt = parseFloat(quantity)

            currency_rate = currency_rates[currency] ? currency_rates[currency] : 1;
            //            console.log(qt, val_conv, currency, currency_rate)
            var amount = val_conv * qt * currency_rate;
            if ($(this).hasClass('unit_price')) {
                amount = amount.toFixed(4);
            } else {
                amount = amount.toFixed(2);
            }

            $(target).val(amount);
        }

    });

    $(document).on('click', '#template-preview_cont_files .dz-filename', function () {
        var parent = $(this).parents('.dz-preview');
        //        console.log(parent.data('href'));
        //        return;
        //        window.location.href = parent.data('href');
        var win = window.open(parent.data('href'), '_blank');
        win.focus();
    })
    $(document).on('click', '.template-preview_cont .dz-filename', function () {
        var parent = $(this).parent();
        var image = parent.find('.dz-imageThumb').css('display', "block");
    })
    $('.searchFiltersManual').click(function () {

        table.draw();
    })
    $('.clearFiltersManual').click(function () {

        $(".searchDataTableParamManual").val("").trigger('change');
        setTimeout(function () {
            table.draw();
        }, 200)
    })
    $(".clearFilters").parent().prepend("<button class='btn btn-success applyFilters'><i class='fa fa-check-circle'></i></button>");
    $(".clearFilters").html("<i class='fa fa-times-circle'></i>");


    $(document).on('click', '.applyFilters', function () {
        // setTimeout(function () {
        table.rows().deselect();
        table.draw();
        // }, 500);

    })

    $('.clearFilters').click(function () {

        table.rows().deselect();
        clearDatatableFilters();
        setTimeout(() => {
            table.draw();
        }, 500);

        //        table.ajax.reload();
    })
});

$('.confirm_form').submit(function (e) {
    e.preventDefault();
    //console.log("ass");
    return false;
    var form = $(this);
    swal({
        title: "Are you sure?",
        text: "The settings will be updated!",
        icon: "warning",
        buttons: [
            'No',
            'Yes'
        ],
        dangerMode: true,
    }).then(function (isConfirm) {
        if (isConfirm) {
            form.submit();
        } else {

        }
    })


    return false;
})


window.Parsley.addValidator('metrocityLimit', {
    validate: function (value, _value, _el) {

        var added_rows_metro = $(".metrocity_row").length;
        if (added_rows_metro > 10) {
            return false;
        } else {
            return true;
        }
    },
    messages: {
        en: 'You can only enter top 10 metro cities',
    }
});
$("#tab-3 > li > a").on("shown.bs.tab", function (e) {
    var id = $(this).data('target').substr(1);
    //    //console.log(id);
    $('html, body').animate({
        scrollTop: $('#' + id).offset().top - 200,
    }, 250);
    //    document.getElementById(id).scrollIntoView();
    //    $("#" + id).animate({scrollTop: 0}, "fast");
    //    window.location.hash = id;
});
window.Parsley.on('field:error', function (a) {
    // This global callback will be called for any field that fails validation.
    //console.log(a);
    this.$element.closest('.tab-pane').addClass('validation_error');
    $('.tab-content .tab-pane.validation_error:eq(0)').addClass('first_tab_with_errors');
    current_tab_id = $('.tab-content .tab-pane.validation_error.first_tab_with_errors').attr('id');
    //    //console.log(current_tab_id);
    //    //console.log($('.nav-tabs a[data-target="' + '#' + current_tab_id + '"]'));
    //    $('.nav-tabs a[data-target="' + '#' + current_tab_id + '"]').addClass('validation_errors')
    $('.nav-tabs a[data-target="' + '#' + (this.$element.closest('.tab-pane').attr('id')) + '"]').addClass('validation_errors');
    //    //console.log(gritterFieldError);
    if (typeof gritterFieldError != "undefined") {
        gritterFieldError = GritterAlert.showErrorText("Please check all the fields.");
    }

});
window.Parsley.on('field:success', function () {
    // This global callback will be called for any field that fails validation.
    if (this.$element.closest('.validation_error').children().find('.parsley-error').length) {
    } else {
        $('.nav-tabs a[data-target="' + '#' + (this.$element.closest('.tab-pane').attr('id')) + '"]').removeClass('validation_errors')
        ////console.log(this.$element.attr('id'));
    }
});
$('#confirm_form_validate').parsley();
$('#confirm_form_validate').on("submit", function (e) {
    e.preventDefault();
    var form = $(this);
    var instance = $(this).parsley();
    if (instance.isValid()) {
        swal({
            title: "Are you sure?",
            text: "The settings will be updated!",
            icon: "warning",
            buttons: [
                'No',
                'Yes'
            ],
            dangerMode: true,
        }).then(function (isConfirm) {
            if (isConfirm) {
                //                //console.log('aaa')
                e.currentTarget.submit();
                return true;
            } else {
                return false;
            }
        })
    } else {
        GritterAlert.showErrorText("Error. Please check all the fields.");
    }
    return false;
});
//Dropzone.autoDiscover = false;
var maxFilesReachedMulti = false;
if ($("#maiasura-dropzone").length) {
    var myDropzone = new Dropzone("#maiasura-dropzone", {
        //        previewTemplate: document.querySelector('#template-container').innerHTML,
        url: "/upload_image",
        paramName: "file", // The name that will be used to transfer the file
        maxFilesize: 7, // MB
        maxFiles: 10,
        parallelUploads: 1,
        maxThumbnailFilesize: 7,
        acceptedFiles: "image/*",
        addRemoveLinks: true,
        timeout: 1800000,
        init: function () {
            this.on('addedfile', function (file) {
                if (this.files.length > 10) {
                    //                    alert("Max files limit reached.")
                    maxFilesReachedMulti = true;
                    this.removeFile(file);
                }
                if (maxFilesReachedMulti) {
                    alert("Max files limit reached.")
                    //                        maxFilesReached = false;
                }
            })
            this.on('removedfile', function (file) {
                if (this.files.length < 10) {
                    //                    alert("Max files limit reached.")
                    maxFilesReachedMulti = false;
                }
            })


        },
        accept: function (file, done) {
            // FileReader() asynchronously reads the contents of files (or raw data buffers) stored on the user's computer.
            var wid = 0;
            var hgt = 0;
            var reader = new FileReader();
            reader.onload = (function (entry) {
                // The Image() constructor creates a new HTMLImageElement instance.
                var image = new Image();
                image.src = entry.target.result;
                image.onload = function () {
                    wid = this.width;
                    hgt = this.height;
                    var ascpetRatio = (wid / hgt).toFixed(1);
                    //console.log(wid, ascpetRatio);
                    if (wid >= 800 && ascpetRatio >= 1.2 && ascpetRatio <= 2) {
                        done();
                    } else {

                        if (wid < 800) {
                            return done("Please upload correct, Image width is too small.");
                        }
                        if (ascpetRatio < 1.2) {
                            done("Please upload correct, Image is too high.");
                        }
                        if (ascpetRatio > 2) {
                            done("Please upload correct, Image is too wide.");
                        }
                        //                        done("Please upload correct dimension images.");
                    }

                };
            });
            reader.readAsDataURL(file);
        }

    });
    //data-dz-remove
    //    myDropzone.on("maxfilesexceeded", function (file) {
    //        /* Maybe display some more file information on your page */
    ////    file.previewElement.id = res.id;
    //        //console.log(file);
    //        alert('Maximum file limit reached.')
    //        myDropzone.removeFile(file);
    //
    ////    //console.log(file);
    ////    //console.log(res);
    //    });
    myDropzone.on("error", function (file, res) {
        /* Maybe display some more file information on your page */

        file.previewElement.addEventListener("click", function () {
            myDropzone.removeFile(file);
        });
        //    file.previewElement.id = res.id;
        //        if (res && res.success == true) {
        //        $(file.previewElement).attr("data-dz-remove", "true");
        //            $(file.previewElement).append("<input type='hidden' name='gallery_images[]' value='" + res.id + "' />");
        //        }

        //    //console.log(file);
        //    //console.log(res);
    });
    myDropzone.on("success", function (file, res) {
        /* Maybe display some more file information on your page */
        //    file.previewElement.id = res.id;
        if (res && res.success == true) {
            $(file.previewElement).attr("id", res.id);
            $(file.previewElement).append("<input type='hidden' name='gallery_images[]' value='" + res.id + "' />");
        }

        //    //console.log(file);
        //    //console.log(res);
    });
    myDropzone.on("addedfile", function (file) {
        /* Maybe display some more file information on your page */
        //    file.previewElement.id = res.id;
        //        //console.log(file);
        if (file.id) {
            $(file.previewElement).attr("id", file.id);
            $(file.previewElement).append("<input type='hidden' name='gallery_images[]' value='" + file.id + "' />");
        }

        //    //console.log(file);
        //    //console.log(res);
    });
    myDropzone.on("removedfile", function (file) {
        /* Maybe display some more file information on your page */

        var id = $(file.previewElement).attr("id");
        var token = $('meta[name="csrf-token"]').attr('content');
        if (typeof id != "undefined") {
            var data = { id: id, _token: token };
            $.post('/remove_image', data, function (res) {
                //            
                //console.log(res);
            })
        }
    });
    myDropzone.on('sending', function (file, xhr, formData) {
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));
    });
    $('.mock_images').each(function () {
        var name = $(this).data('name');
        var url = $(this).attr('src');
        var path = $(this).data('path');
        let file = {
            name: name,
            size: 12345,
            accepted: true,
            id: $(this).attr('id'),
            path: path,
        };
        //    myDropzone.addFile(file);

        myDropzone.emit("addedfile", file);
        myDropzone.emit("thumbnail", file, url);
        //    myDropzone.emit("success", file);
        myDropzone.emit("complete", file);
        //    myDropzone.options.addedfile.call(myDropzone, file);
        //
        //    myDropzone.options.thumbnail.call(myDropzone, file, url);
        //    myDropzone.options.complete.call(myDropzone, file);
        myDropzone.files.push(file);
    })
}

var maxFilesReachedd = false;
if ($("#maiasura-dropzone-single").length) {
    var myDropzoneSingle = new Dropzone("#maiasura-dropzone-single", {
        url: "/upload_image",
        paramName: "file", // The name that will be used to transfer the file
        maxFilesize: 7, // MB
        maxFiles: 1,
        acceptedFiles: "image/*",
        addRemoveLinks: true,
        init: function () {
            this.on('addedfile', function (file) {
                if (this.files.length > 1) {
                    //                    alert("Max files limit reached.")
                    maxFilesReachedd = true;
                    this.removeFile(file);
                    if (maxFilesReachedd) {
                        alert("Max files limit reached.")
                        maxFilesReachedd = false;
                    }


                }
            })


        },
        timeout: 1800000,
        accept: function (file, done) {
            // FileReader() asynchronously reads the contents of files (or raw data buffers) stored on the user's computer.
            var wid = 0;
            var hgt = 0;
            var reader = new FileReader();
            reader.onload = (function (entry) {
                // The Image() constructor creates a new HTMLImageElement instance.
                var image = new Image();
                image.src = entry.target.result;
                image.onload = function () {
                    wid = this.width;
                    hgt = this.height;
                    var ascpetRatio = (wid / hgt).toFixed(1);
                    //console.log(wid, ascpetRatio);
                    if (wid >= 800 && ascpetRatio >= 1.2 && ascpetRatio <= 2) {
                        done();
                    } else {
                        if (wid < 800) {
                            return done("Please upload correct, Image width is too small.");
                        }
                        if (ascpetRatio < 1.2) {
                            done("Please upload correct, Image is too high.");
                        }
                        if (ascpetRatio > 2) {
                            done("Please upload correct, Image is too wide.");
                        }
                        //                        done("Please upload correct dimension images.");
                    }

                };
            });
            reader.readAsDataURL(file);
        }

    });
    myDropzoneSingle.on("success", function (file, res) {
        /* Maybe display some more file information on your page */
        //    file.previewElement.id = res.id;
        if (res && res.success == true) {
            $(file.previewElement).attr("id", res.id);
            $("#featured_image_id").val(res.id);
        }

        //    //console.log(file);
        //    //console.log(res);
    });
    //    myDropzoneSingle.on("maxfilesexceeded", function (file) {
    //        /* Maybe display some more file information on your page */
    ////    file.previewElement.id = res.id;
    //        //console.log(file);
    //        alert('Maximum file limit reached.')
    //        myDropzoneSingle.removeFile(file);
    //
    ////    //console.log(file);
    ////    //console.log(res);
    //    });
    myDropzoneSingle.on("addedfile", function (file) {
        /* Maybe display some more file information on your page */
        //    file.previewElement.id = res.id;
        //console.log(file);
        if (file.id) {
            $("#featured_image_id").val(file.id);
            $(file.previewElement).attr("id", file.id);
        }

        //    //console.log(file);
        //    //console.log(res);
    });
    myDropzoneSingle.on("removedfile", function (file) {
        /* Maybe display some more file information on your page */

        var id = $(file.previewElement).attr("id");
        //console.log(id);
        var token = $('meta[name="csrf-token"]').attr('content');
        if (typeof id != "undefined") {
            $("#featured_image_id").val("");
            var data = { id: id, _token: token };
            $.post('/remove_image', data, function (res) {
                //            
                //console.log(res);
            })
        }
    });
    myDropzoneSingle.on('sending', function (file, xhr, formData) {
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));
    });
    myDropzoneSingle.on("error", function (file, res) {
        /* Maybe display some more file information on your page */

        file.previewElement.addEventListener("click", function () {
            myDropzoneSingle.removeFile(file);
        });
    });
    $('.mock_featured_image').each(function () {
        var name = $(this).data('name');
        var url = $(this).attr('src');
        var path = $(this).data('path');
        let file = {
            name: name,
            size: 12345,
            accepted: true,
            id: $(this).attr('id'),
            path: path,
        };
        //    myDropzone.addFile(file);

        myDropzoneSingle.emit("addedfile", file);
        myDropzoneSingle.emit("thumbnail", file, url);
        myDropzoneSingle.emit("complete", file);
        myDropzoneSingle.files.push(file);
    })
}
var maxFilesReacheddd = false;
if ($("#maiasura-dropzone-menufile").length) {
    var myDropzoneSingleMenu = new Dropzone("#maiasura-dropzone-menufile", {
        url: "/manage/restaurants/upload_menu",
        paramName: "file", // The name that will be used to transfer the file
        maxFilesize: 15, // MB
        maxFiles: 1,
        acceptedFiles: "image/*,application/pdf",
        addRemoveLinks: true,
        timeout: 1800000,
        accept: function (file, done) {

            switch (file.type) {
                case 'application/pdf':
                    this.emit("thumbnail", file, "/components/frontend/assets/images/pdf-icon.png");
                    break;
            }
            done();
        },
        init: function () {
            this.on('addedfile', function (file) {
                if (this.files.length > 1) {
                    //                    alert("Max files limit reached.")
                    maxFilesReacheddd = true;
                    this.removeFile(file);
                    if (maxFilesReacheddd) {
                        alert("Max files limit reached.")
                        maxFilesReacheddd = false;
                    }
                }
            })
        },
    });
    myDropzoneSingleMenu.on("success", function (file, res) {

        if (res && res.success == true) {
            $(file.previewElement).attr("id", res.id);
            $(".restraunt_menu_link").attr('href', res.url);
            $("#menufile_id").val(res.id);
        }

    });
    myDropzoneSingleMenu.on("addedfile", function (file) {

        if (file.id) {
            $("#menufile_id").val(file.id);
            $(file.previewElement).attr("id", file.id);
        }

    });
    myDropzoneSingleMenu.on("removedfile", function (file) {
        /* Maybe display some more file information on your page */

        var id = $(file.previewElement).attr("id");
        var token = $('meta[name="csrf-token"]').attr('content');
        if (typeof id != "undefined") {
            $("#menufile_id").val("");
            var data = { id: id, _token: token };
            $.post('/remove_image', data, function (res) {
                //            
                //console.log(res);
            })
        }
    });
    myDropzoneSingleMenu.on('sending', function (file, xhr, formData) {
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));
    });
    //    $('.mock_menufile').each(function () {
    //        var name = $(this).data('name');
    //        var url = $(this).attr('src');
    //        var path = $(this).data('path');
    //        let file = {
    //            name: name,
    //            size: 12345,
    //            accepted: true,
    //            id: $(this).attr('id'),
    //            path: path,
    //        };
    ////    myDropzone.addFile(file);
    //
    //        myDropzoneSingleMenu.emit("addedfile", file);
    //        myDropzoneSingleMenu.emit("thumbnail", file, url);
    //        myDropzoneSingleMenu.emit("complete", file);
    //        myDropzoneSingleMenu.files.push(file);
    //    })
}

$('body').on('keyup', '.quoted_price', function (e) {
    // console.log("hello world");
    if (e.keyCode == 13) {
        // console.log("entered");
        e.preventDefault();
        $(this).next().find('.quoted_price').focus();
        return true;
    }
});



var loadingGrit = erroGrit = "";
$(".ajaxSubmitForm").parsley();

function ajaxSubmitManual(form) {

    var instance = form.parsley();
    if (instance.isValid()) {
        loadingGrit = GritterAlert.showInProcessSticky();
        //                console.log(loadingGrit)
        form.ajaxSubmit({
            success: function (res) {

                //                console.log(res);
                GritterAlert.removeNotification(loadingGrit);
                if (res.success == true) {
                    if (res.message) {
                        GritterAlert.showSuccessText(res.message);
                    } else {
                        GritterAlert.showSuccess();
                    }


                    return res;

                } else {
                    swal("Error!", res.error, 'error');
                    return res;

                }
            },
            error: function (res) {
                //                        console.log(res);
                GritterAlert.removeNotification(loadingGrit);
                //                        $.gritter.remove(loadingGrit);

                showErrors(res);
                return res;
            }
        });
        //        swal({
        //            title: "Are you sure?",
        //            text: "The settings will be updated!",
        //            icon: "warning",
        //            buttons: [
        //                'No',
        //                'Yes'
        //            ],
        //            dangerMode: true,
        //        }).then(function (isConfirm) {
        //            if (isConfirm) {
        //                //console.log('aaa')
        ////                e.currentTarget.submit();
        //                
        //                return true;
        //            } else {
        //                return false;
        //            }
        //        })
    } else {
        GritterAlert.showErrorText("Error. Please check all the fields.");
    }
    return false;
}


function ajaxSubmitModalWithCallbackAfter(form, callback) {
    // console.log("as");

    var instance = form.parsley();
    if (instance.isValid()) {
        loadingGrit = GritterAlert.showInProcessSticky();
        //                console.log(loadingGrit)
        form.ajaxSubmit({
            success: function (res) {
                if (res.message) {
                    GritterAlert.showSuccessText(res.message);

                } else {
                    GritterAlert.showSuccess();

                }
                //console.log(res);
                GritterAlert.removeNotification(loadingGrit);
                callback();

            },
            error: function (res) {
                //console.log(res);
                GritterAlert.removeNotification(loadingGrit);
                //                        $.gritter.remove(loadingGrit);

                showErrors(res);
            }
        });

    }
    return false;


}
function ajaxSubmitModalWithCallbackBoth(form, precallback) {
    return new Promise(function (resolve, reject) {
        $(form).parsley();
        $(form).on("submit", function (e) {
            e.preventDefault();

            var instance = $(form).parsley();



            precallback().then(function () {
                if (instance.isValid()) {

                    $(form).ajaxSubmit({
                        success: function (res) {

                            resolve(res);

                        },
                        error: function (res) {


                            reject(res);
                        }
                    });

                }

            }).catch(function (res) {
                reject(res);
            })
        });


    });


}
function ajaxSubmitModalWithCallback(form, precallback) {
    $(form).parsley();
    $(form).on("submit", function (e) {
        //console.log("we here");
        e.preventDefault();
        var form = $(this);
        //    return;
        var instance = $(this).parsley();
        if (precallback() && instance.isValid()) {
            loadingGrit = GritterAlert.showInProcessSticky();
            //                console.log(loadingGrit)
            form.ajaxSubmit({
                success: function (res) {
                    GritterAlert.showSuccessText(res.message);
                    //console.log(res);
                    GritterAlert.removeNotification(loadingGrit);


                },
                error: function (res) {
                    //console.log(res);
                    GritterAlert.removeNotification(loadingGrit);
                    //                        $.gritter.remove(loadingGrit);

                    showErrors(res);
                }
            });

        }
        return false;
    });

}
$('.ajaxSubmitForm').on("submit", function (e) {

    e.preventDefault();

    var form = $(this);
    var instance = $(this).parsley();
    if (instance.isValid()) {
        loadingGrit = GritterAlert.showInProcessSticky();
        //                console.log(loadingGrit)
        priceFormateToSubmit();
        $(".ajaxSubmitFormSaveTrigger").prop('disabled', true);

        form.ajaxSubmit({
            success: function (res) {

                //                console.log(res);
                GritterAlert.removeNotification(loadingGrit);
                if (res.success == true) {
                    GritterAlert.showSuccess();
                    if (res.redirect && res.redirect != "") {
                        window.location.href = res.redirect;
                    }
                    else {
                        window.location.reload();
                    }

                } else {
                    swal("Error!", res.error, 'error');
                    $(".ajaxSubmitFormSaveTrigger").prop('disabled', false);
                }
            },
            error: function (res) {
                //                        console.log(res);
                GritterAlert.removeNotification(loadingGrit);
                $(".ajaxSubmitFormSaveTrigger").prop('disabled', false);

                //                        $.gritter.remove(loadingGrit);

                showErrors(res);
            }
        });
        //        swal({
        //            title: "Are you sure?",
        //            text: "The settings will be updated!",
        //            icon: "warning",
        //            buttons: [
        //                'No',
        //                'Yes'
        //            ],
        //            dangerMode: true,
        //        }).then(function (isConfirm) {
        //            if (isConfirm) {
        //                //console.log('aaa')
        ////                e.currentTarget.submit();
        //                
        //                return true;
        //            } else {
        //                return false;
        //            }
        //        })
    } else {
        $(".ajaxSubmitFormSaveTrigger").prop('disabled', false);

        GritterAlert.showErrorText("Error. Please check all the fields.");
    }
    return false;
});

// $('#form-work').on('keyup keypress', function(e) {
//     var keyCode = e.keyCode || e.which;
//     if (keyCode === 13) { 
//       e.preventDefault();
//     //   console.log($(this).attr("id"));

//     $('.quoted_price').on('keyup keypress', function(e) {
//         var keyCode = e.keyCode || e.which;
//         if (keyCode === 13) { 
//           e.preventDefault();
//           console.log("hello");
//           return false;
//         }
//       })
//       return false;
//     }
//   });

//   $('.quoted_price').on('keyup keypress', function(e) {
//     var keyCode = e.keyCode || e.which;
//     if (keyCode === 13) { 
//       e.preventDefault();
//       console.log("hello");
//       return false;
//     }
//   });



var dateFormat = "mm/dd/yy";
$(".start_date").each(function () {

    $(this).datepicker({
        defaultDate: "+1",
        changeMonth: true,
        numberOfMonths: 1,
        autoClose: true,
        showButtonPanel: true,
        changeYear: true,
        showButtonPanel: true,
        closeText: 'Clear',
        onClose: function (dateText, inst) {
            if ($(window.event.srcElement).hasClass('ui-datepicker-close')) {
                //                document.getElementById(this.id).value = '';
                jQuery("#" + this.id).val('').change();
            }
        }
    }).on("change", function () {
        var fromDate = getDate(this);
        //        console.log(getDate(this));
        $("#" + $(this).data('target')).datepicker("option", "minDate", getDate(this));
        $("#" + $(this).data('target')).datepicker("refresh");
    });
})
$('.end_date').each(function () {
    $(this).datepicker({
        defaultDate: "+1w",
        changeMonth: true,
        numberOfMonths: 1,
        autoClose: true,
        showButtonPanel: true,
        changeYear: true,
        closeText: 'Clear',
        onClose: function (dateText, inst) {
            if ($(window.event.srcElement).hasClass('ui-datepicker-close')) {
                jQuery("#" + this.id).val('').change();
                //                document.getElementById(this.id).value = '';
            }
        }
    }).on("change", function () {

        $("#" + $(this).data('target')).datepicker("option", "maxDate", getDate(this));
        $("#" + $(this).data('target')).datepicker("refresh");
    });
})



function getDate(element) {
    var date;
    try {
        date = $.datepicker.parseDate(dateFormat, element.value);
    } catch (error) {
        date = null;
    }

    return date;
}
$(".deleteItem").click(function () {
    $(this).parents('tr').find(".deleteItemTrigger").trigger('click');
})

//$(".deleteItemAjax").click(function () {
//    $(this).parents('tr').find(".deleteItemTrigger").trigger('click');
//})

$(document).on("click", ".deleteConfirmForm", function () {
    var custome_confirm_message = $('.delete_confirm_custome_message').val();
    if (typeof custome_confirm_message == 'undefined' || custome_confirm_message == '') {
        custome_confirm_message = "This item will be deleted!";
    }
    var _this = $(this);
    var dt = $(this).parents('.datatable')[0];
    var dtinstance = new $.fn.dataTable.Api(dt);

    // console.log(instance);
    // return;
    swal({
        title: "Are you sure?",
        text: custome_confirm_message,
        icon: "warning",
        buttons: [
            'No',
            'Yes'
        ],
        dangerMode: true,
    }).then(function (isConfirm) {
        if (isConfirm) {
            //            var form=_this.parent().find(".deleteItemForm");
            //            var data= form.data();
            //            _this.parent().find(".deleteItemForm").submit();

            var form = _this.parents('.deletetItemForm');
            var url = form.attr('action');
            $.ajax({
                type: "POST",
                url: url,
                data: form.serialize(), // serializes the form's elements.
                success: function (data) {
                    //console.log(data);

                    if (data.error) {
                        GritterAlert.showErrorText(data.error);
                    } else {
                        GritterAlert.showSuccess();
                        _this.parents('tr').remove();

                        if (dtinstance) {
                            dtinstance.ajax.reload();
                        }
                        if (table) {
                            table.ajax.reload();
                        }
                    }

                }
            });
            return true;
        } else {
            return false;
        }
    })
})
$(document).on("click", ".deleteItemAjax", function () {
    var _this = $(this);
    swal({
        title: "Are you sure?",
        text: "This item will be deleted!",
        icon: "warning",
        buttons: [
            'No',
            'Yes'
        ],
        dangerMode: true,
    }).then(function (isConfirm) {
        if (isConfirm) {
            //            var form=_this.parent().find(".deleteItemForm");
            //            var data= form.data();
            //            _this.parent().find(".deleteItemForm").submit();

            var form = _this.parent().find(".deleteItemForm");
            var url = form.attr('action');
            $.ajax({
                type: "POST",
                url: url,
                data: form.serialize(), // serializes the form's elements.
                success: function (data) {
                    //console.log(data);

                    if (data.error) {
                        GritterAlert.showErrorText(data.error);
                    } else {
                        GritterAlert.showSuccess();
                        _this.parents('tr').remove();
                    }

                }
            });
            return true;
        } else {
            return false;
        }
    })
})

$(document).on("click", ".summernote", function () {
    // console.log("its working he");
    var valued = $(this).val();
    var named = $(this).attr("name");

});

//$(document).on("click",".summernote").each(function() { 
//    // console.log("its working he");
//    var valued = $(this).val();
//    var named = $(this).attr("name");
//    
//});



jQuery(window).load(function () {
    // console.log($.fn.dataTable.tables(true));
    $($.fn.dataTable.tables(true)).each(function () {
        var table__ = $(this).DataTable();
        table__.on('select', function (e, dt, type, indexes) {

            var ids = $.map(table__.rows({
                selected: true
            }).data(), function (entry) {
                return entry.id
            });
            // console.log(ids.length);
            setTimeout(() => {
                $(".select-info .select-item").html("" + ids.length + " row/s selected");

            }, 50);
        })
            .on('deselect', function (e, dt, type, indexes) {
                var ids = $.map(table__.rows({
                    selected: true
                }).data(), function (entry) {
                    return entry.id
                });
                // console.log(ids);
                setTimeout(() => {
                    $(".dataTables_info").find($(".select-info")).remove();
                    $(".dataTables_info").append("<span class='select-info'><span class='select-item'>" + ids.length + " row/s selected</span></span>");
                }, 50);
            });
    })

    $(".hasDatepicker").mask('99/99/9999');
    //    $('#form-work').confirmExit('There are unsaved chnages! Are you sure you want to leave this page?');

    $(".price_formate").blur();
    //    setTimeout(function () {
    //        $(".price_formate").each(function () {
    //            var val = $(this).val();
    //
    //            if (val != '') {
    //
    //                var formated = formatCurrency(val);
    //                console.log(formated);
    //                $(this).val(formated).change();
    //            } else {
    //                $(this).val(0.00);
    //            }
    //        });
    //    }, 500)


    $(".daterangepicker_").each(function () {
        $(this).daterangepicker();
    });

    $(".datepicker_").each(function () {
        makeItDatePicker($(this))
    });
    //    $(function () {
    //        $(".filterBlock").accordion({
    //            collapsible: true
    //        });
    //    });

    $(".deleteContact").click(function () {
        var contectRemove = $(this).data('class');
        $("." + contectRemove).remove();
    })

    var telinput = document.querySelector(".phone_number");
    if ($(telinput).length) {
        var initialCountry = "auto";
        if ($(telinput).val() != "") {
            initialCountry = "";
        }
        var itit = window.intlTelInput(telinput, {
            initialCountry: initialCountry,
            hiddenInput: "phone_num",
            separateDialCode: "true",
            utilsScript: "/components/assets/plugins/phone-input/js/utils.js",
            autoPlaceholder: "aggressive",
            //        nationalMode: false,
            //        formatOnDisplay: false,
            utilsScript: "/components/assets/plugins/phone-input/js/utils.js",
            geoIpLookup: function (success, failure) {
                $.get("https://ipinfo.io", function () { }, "jsonp").always(function (resp) {
                    var countryCode = (resp && resp.country) ? resp.country : "";
                    success(countryCode);
                });
            },
        });
    }
    setTimeout(function () {
        //    $(".sucess_notif").fadeTo(1000, 0).slideUp(1000, function(){
        //        $(this).remove(); 
        //    });
        //console.log("s");
        $(".sucess_notif").fadeOut(400);
        $(".sucess_notif").slideUp(200, function () {
            $(this).alert('close');
        });
    }, 4000);
});

$("#add_address_row_trigger").click(function () {

    //    //console.log("add_language_row_trigger")
    var alreadyAdded = $(".address_").length;
    // console.log(alreadyAdded)
    var html = $(".address_template").html();
    html = html.replace(/__item_plus__/g, alreadyAdded + 1);
    html = html.replace(/__item__/g, alreadyAdded);
    var template = $("<div class='col-lg-12 address_ address_" + alreadyAdded + "'></div>").append(html);
    $(template).find(".makeItRequired").each(function () {
        $(this).attr('required', "true")
    })

    var parent = $(".address_cont");
    parent.append(template);
    //    template.find("phone_number_1").attr('required', true);

    // $('.summernote').each(function () {
    //     CKEDITOR.replace($(this).prop('id'));
    // });
    // $(document).on(".summernote").each(function() { 
    //     console.log("its working he");
    //     var valued = $(this).val();
    //     var named = $(this).attr("name");

    // });



});

$(".deleteSupplierContact").click(function () {
    var contectRemove = $(this).data('class');
    $("." + contectRemove).remove();
});

$(".add_item_addons_row_trigger").click(function () {
    //    console.log("add_language_row_trigger")

    var target = $(this).attr('data-target');
    var targetElm = $("#" + target);
    // console.log(targetElm);
    var add_language_rows = targetElm.find(".added_item_addons_row").length;
    add_language_rows++;
    // console.log(add_language_rows)
    var html = targetElm.find(".add_item_addons_row_template").html().replace(/__item__/g, add_language_rows);
    var template = $("<tr class='added_item_addons_row'></tr>").append(html);
    //    var template = $(html).removeClass("add_language_row hidden");
    template.find('.sel_1').addClass('added');
    //    $('.sel_1.added').each(function () {
    //        var valAlready = $(this).val();
    //        template.find(".sel_1 option[value='" + valAlready + "']").remove();
    //    })
    //    var val_tresh = template.find(".sel_1 option").length;
    //    if (!val_tresh) {
    //        return swal({
    //            title: "Oops..",
    //            text: "There is no more item availble to add.",
    //            icon: "warning",
    //            dangerMode: true,
    //        });
    //    }

    var parent = targetElm.find(".add_item_addons_row_body");
    parent.append(template);
    template.find('.item_sel').each(function () {
        var elmsd = $(this);
        $(this).select2();
        //        $(this).trigger('change');
    })
    template.find('.item_sel_ajax').each(function () {
        var elmsd = $(this);
        var url = $(this).data('url');
        var type = $(this).data('type');
        $(this).select2({
            minimumInputLength: 0,
            width: "100%",
            createTag: function (params) {
                var term = $.trim(params.term);
                var id = term;
                console.log(type, id);
                if (type == 'manufacturer_nos') {
                    id = "_MN_:" + id;
                }
                if (type == 'replacement_nos') {
                    id = "_RN_:" + id;
                }

                return {
                    id: id,
                    text: term,
                    newTag: true // add additional parameters
                }
            },
            ajax: {
                url: url,
                dataType: 'json',
                type: 'GET',
                delay: 200,
                data: function (params) {
                    var query = {

                        q: params.term,
                        type: type,
                        page: params.page || 1
                    }

                    return query;
                },


            }
        });
        //        $(this).trigger('change');
    })

    template.find('.parsley_req').each(function () {
        $(this).attr('required', true);
    })

});
$(document).on("change", ".show_hide_on_change", function () {
    var target = $(this).data('target');
    // console.log(target);
    if ($(this).is(":checked")) {
        $("#" + target).removeClass('hidden');
    } else {
        $("#" + target).addClass('hidden');
    }
});
$(document).on("click", ".alertTrigger", function () {
    var msg = $(this).data('message');
    return alert(msg)
});
$(document).on("click", ".removeAddableItem", function () {
    var _this = $(this);
    swal({
        title: "Are you sure?",
        text: "This item will be deleted!",
        icon: "warning",
        buttons: [
            'No',
            'Yes'
        ],
        dangerMode: true,
    }).then(function (isConfirm) {
        if (isConfirm) {
            _this.parents("tr").remove();
            return true;
        } else {
            return false;
        }
    })
})
//var table = new $.fn.dataTable.Api('.datatable');

$(".ajaxSubmitFormSaveTrigger").click(function () {
    // $(this).prop('disabled',true);
    $(".ajaxSubmitFormBtn").click();
})
$(".cancelBtn").click(function () {
    return window.history.back();
})


$('body').on('keydown', '.trigger_next', function (e) {
    if (e.which === 13) {
        var self = $(this), form = self.parents('form:eq(0)'), focusable, next;
        focusable = form.find('.trigger_next').filter(':visible');
        next = focusable.eq(focusable.index(this) + 1);
        if (next.length) {
            if ("0.00" == next.val()) {
                next.val("");
            }
            //            console.log(next.val())
            next.focus();
        }
        return false;
    }
});


resetPartNumberDropDown = function () {
    $("#part_number_after").select2("val", "0");
    $("#part_number_after").parent().addClass('hidden');
    $('#part_number_after').select2('close');
    $("#part_number_id_2").parent().removeClass('hidden')
    $('#part_number_id_2').select2('open');
    $("#part_number_id_2").select2("val", "0");

}
$("#part_number_after").change(function () {
    var val = $(this).val();
    //    console.log(val);
    if (val == "-1") {
        return;
    }
    if (!val) {

        $(this).parent().addClass('hidden');
        $("#part_number_id_2").parent().removeClass('hidden')
        $('#part_number_id_2').select2('open');
        $("#part_number_id_2").select2("val", "0");
        $('#part_number_after').select2('close');



    } else {
        //        console.log(val);
        var data = $('#part_number_after').select2('data')
        //        console.log(data[0].text);
        var alt_part_number_id = $("#part_number_id_2").val();
        //        console.log(alt_part_number_id);

        var newOption = new Option(data[0].text, val, false, false);
        $('#part_number_id_2').append(newOption);
        $("#part_number_id_2").val(val).change();
        //            $("#part_number_id_2").val(val);
        $("#part_number_after").parent().addClass('hidden');
        $("#part_number_id_2").parent().removeClass('hidden');

        setTimeout(function () {
            //            $('#part_number_id_2').append('<option value=' + val + '>' + data[0].text + '</option>');



            if (alt_part_number_id.indexOf("MN-") == 0 || alt_part_number_id.indexOf("RN-") == 0) {
                $("#alt_part_number_id").val(alt_part_number_id)
            } else {
                $("#alt_part_number_id").val("0")
            }

            //            console.log($("#part_number_id").val());
            ////            $("#part_number_id").trigger('change')
        })

    }

    //            $("#part_number_before").val($(this).val())
})

$('#part_number_after').on('select2:open', function (e) {
    // Do something
    //    var pna_data = $('#part_number_after').find("option").length


    // console.log(e);
    var elem = $("#part_number_id_2");

    var selectedCont = $("#part_number_id_2").val();

    var url = $(elem).data('ajaxurl');
    url = url + "?id=" + selectedCont;
    $.ajax({
        type: 'GET',
        url: url
    }).then(function (data) {
        // create the option and append to Select2
        // console.log(data.results);
        if (data.results.length == 2) {
            var selected = data.results[0];
            var newOption = new Option(selected.text, selected.id, false, false);
            var alt_part_number_id = $("#part_number_id_2").val();

            $('#part_number_id_2').append(newOption);
            $("#part_number_id_2").val(selected.id).change();
            $('#part_number_after').select2('close');
            $("#part_number_after").parent().addClass('hidden');
            $("#part_number_id_2").parent().removeClass('hidden');
            setTimeout(function () {
                //            $('#part_number_id_2').append('<option value=' + val + '>' + data[0].text + '</option>');



                if (alt_part_number_id.indexOf("MN-") == 0 || alt_part_number_id.indexOf("RN-") == 0) {
                    $("#alt_part_number_id").val(alt_part_number_id)
                } else {
                    $("#alt_part_number_id").val("0")
                }

                //            console.log($("#part_number_id").val());
                ////            $("#part_number_id").trigger('change')
            })
        }

    });
});
$("#part_number_id_2").change(function () {
    // console.log($(this).val())
    var parent = $(this).parents('tr');
    var partnumber = $(this).val();
    //    console.log(partnumber);

    if (partnumber == "0") {
        return;
    }
    setTimeout(function () {
        if (partnumber && partnumber != "") {
            //            

            if (partnumber.indexOf("MN-") == 0 || partnumber.indexOf("RN-") == 0) {
                $("#part_number_id_2").parent().addClass('hidden')
                $("#part_number_after").parent().removeClass('hidden');
                $('#part_number_id_2').select2('close');
                $("#part_number_after").val("");
                $('#part_number_after').select2('open');





            } else {
                //                console.log("11");
                //                console.log($("#part_number_after").val());
                $("#alt_part_number_id").val("0")
                parent.find('#quantity').focus();
            }

        } else {
            //            $('#part_number_id').select2('open');
            // parent.find('#part_number_id').focus();
        }

    });

})
$("#part_number_id").change(function () {
    //    console.log($(this).val())
    var parent = $(this).parents('tr');
    var partnumber = $("#part_number_id").val();

    setTimeout(function () {
        if (partnumber != "") {
            parent.find('#quantity').focus();

        } else {
            $('#part_number_id').select2('open');
            // parent.find('#part_number_id').focus();
        }

    });

})

$('body').on('keydown', '#quantity', function (e) {
    if (e.which === 13) {
        var self = $(this), parent = self.parents('tr');

        if ($("#part_number_id_2").val() == "" || $("#part_number_id").val() == "") {
            // parent.find("#part_number_id").focus();
            swal("Error!", "Please select part number also.", 'error');
        } else {
            if (parent.find("#unit_price").length) {
                parent.find("#unit_price").focus().select();
            } else {
                parent.find("button").trigger('click');
            }
            // parent.find("#part_number_id").focus();

        }
        // console.log("working here");
        // parent.find("#part_number_id").focus();
        return false;
    }
});
$('body').on('keydown', '#unit_price', function (e) {
    if (e.which === 13) {
        var self = $(this), parent = self.parents('tr');

        if ($("#part_number_id_2").val() == "" || $("#part_number_id").val() == "" || $("#quantity").val() == "") {
            // parent.find("#part_number_id").focus();
            swal("Error!", "Please select part number and quantity.", 'error');
        } else {
            // parent.find("#part_number_id").focus();
            parent.find("button").trigger('click');
        }
        // console.log("working here");
        // parent.find("#part_number_id").focus();
        return false;
    }
});
//$('.price_formate').autoNumeric('init');
//    $('.price_formate').bind('blur focusout keypress keyup', function () {
//        var demoGet = $('#demoDefaults').autoNumeric('get');
//        $('#demoGet').val(demoGet);
//        $('#demoSet').autoNumeric('set', demoGet);
//    });

//$(".price_formate").blur();
$(document).on("blur", '.price_formated', function () {
    var val = $(this).val();
    var textVal = $(this).parent().find('.price_formate_text');
    if (val != '') {
        var formated = new Intl.NumberFormat().format(val);
    } else {
        var formated = 0;
    }
    if (!textVal.length) {
        $(this).parent().append('<span class="price_formate_text">' + formated + '</span>')
    } else {
        $(textVal).text(formated);

    }
    $(textVal).show();

});
$(document).on("keypress", '.price_formate.quantity.negative', function (evt) {

    var charCode = evt.which ? evt.which : evt.keyCode
    // console.log()
    if (
        (charCode != 45 || $(this).val().indexOf('-') != -1) &&
        (charCode < 48 || charCode > 57))
        evt.preventDefault();
    return true;
});
$(document).on("focusin", '.price_formate.quantity.negative', function () {


    var val = $(this).val();
    if (val != '') {
        var formated = val.replace(/,/g, '');
        //        console.log(formated + ' Here');
        if (formated == 0) {
            $(this).val("");
        } else {
            $(this).val(formated);
        }

    }
});
$(document).on("blur", '.price_formate.quantity.negative', function () {
    var val = $(this).val();
    //    console.log(val);
    if (val != '') {
        var formated = new Intl.NumberFormat().format(val);
        //        console.log(formated + ' Here');
        $(this).val(formated);
    } else {
        $(this).val(0);
    }
});

$(document).on("keypress", '.price_formate.quantity:not(.negative)', function (evt) {


    evt = (evt) ? evt : window.event;
    var charCode = (evt.which) ? evt.which : evt.keyCode;
    // console.log(charCode);
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
        return false;
    }
    return true;
});
$(document).on("focusin", '.price_formate.quantity:not(.negative)', function () {


    var val = $(this).val();
    if (val != '') {
        var formated = val.replace(/,/g, '');
        //        console.log(formated + ' Here');
        if (formated == 0) {
            $(this).val("");
        } else {
            $(this).val(formated);
        }

    }
});
$(document).on("blur", '.price_formate.quantity:not(.negative)', function () {
    var val = $(this).val();
    //    console.log(val);
    if (val != '') {
        var formated = val.replace(/,/g, '');

        formated = new Intl.NumberFormat().format(formated);
        // console.log(val, formated + ' Here');
        $(this).val(formated);
    } else {
        $(this).val(0);
    }
});
$(document).on("keypress", '.price_formate:not(.quantity)', function (evt) {

    evt = (evt) ? evt : window.event;
    var charCode = (evt.which) ? evt.which : evt.keyCode;
    if (charCode != 46 && charCode > 31
        && (charCode < 48 || charCode > 57)) {
        return false;
    }
    return true;
});
$(document).on("focusin", '.price_formate:not(.quantity)', function () {


    var val = $(this).val();
    // console.log(val + ' Here');
    if (val != '') {
        var formated = val.replace(/,/g, '');

        if (formated == 0) {
            $(this).val("");
        } else {
            $(this).val(formated);
        }

    }
});
$(document).on("blur", '.price_formate:not(.quantity)', function () {
    var val = $(this).val();
    var typeConv = $(this).hasClass('unit_price') ? 4 : 2;
    if (val != '') {

        if (typeConv == 2) {
            var formated = formatCurrency(val);
        }
        else {
            var formated = formatCurrencyDecimal(val);
        }
        // console.log(formated);
        $(this).val(formated).change();
    } else {
        $(this).val(0.00);
    }
});
$(document).on("click", '.price_formate_text', function () {
    $(this).hide();
    var textVal = $(this).parent().find('input').focus();

});
$(document).on("focusin", '.price_formated', function () {

    var textVal = $(this).parent().find('.price_formate_text');

    var val = $(this).val();
    if (val != '') {
        var formated = val.replace(/,/g, '');
        if (formated == 0) {
            $(this).val("");

        } else {
            $(this).val(formated);
        }

    }
    $(textVal).hide();


});
function reformatePrice($elm) {

    var val = $elm.val();
    //    console.log(val)
    if (val != '') {
        var formated = val.replace(/,/g, '');
        $elm.val(formated);
    }

}
$(document).on("click", '.ajaxSubmitFormBtn', function () {
    // console.log("a");
    $(".price_formate").each(function () {
        var val = $(this).val();
        if (val != '') {
            var formated = val.replace(/,/g, '');
            $(this).val(formated);
        }
    });
});


$(document).on("click", '.btn', function () {
    $(".price_formate").each(function () {
        var val = $(this).val();
        if (val != '') {
            var formated = val.replace(/,/g, '');
            $(this).val(formated);
        }
    });
});
//$(document).on("submit", ".searchFiltersForm", function (event) {
//    return false;
//});

$(document).on("click", '.printPdf', function () {
    var urlRaw = $(this).data('href');
    //    var data = $(".searchFiltersForm")[0].serialize();
    var values = [];
    var dataString = "";
    let url = new URL(urlRaw);
    let params = url.searchParams;
    $("#is_printed").prop('checked', true);
    $('.searchFiltersForm .form-control').each(function () {
        if (this.value != "") {
            values[this.id] = this.value;
            params.append(this.id, this.value);

            //            dataString = dataString + "" + this.id + "=" + this.value + "&";
        }

    });
    $('.printParamsSelect').each(function () {
        // console.log($(this).val());
        if ($(this).val()) {
            values[this.id] = $(this).val();
            params.append(this.id, $(this).val());

            //            dataString = dataString + "" + this.id + "=" + this.value + "&";
        }

    });
    $('.printParamsCheck').each(function () {
        if ($(this).prop('checked')) {
            values[this.id] = 1;
            params.append(this.id, 1);

            //            dataString = dataString + "" + this.id + "=" + this.value + "&";
        } else {
            values[this.id] = 0;
            params.append(this.id, 0);
        }

    });
    //    const esc = encodeURIComponent;
    //    const queryParam = Object.keys(values)
    //            .map(k => esc(k) + '=' + esc(values[k]))
    //            .join('&');

    //use values after the loop
    //    console.log(url);
    //    return;
    //    url = url + "/?" + dataString;
    var finalUrl = url.toString();
    pdfw = window.open(finalUrl, '_blank', 'fullscreen=1,channelmode=1,status=1,resizable=1');
    pdfw.focus();
    // you need to call print after window loads like this
    pdfw.onload = function () {
        pdfw.print();
        setTimeout(function () {
            pdfw.close();
        }, 120000)
    }
});

function ajaxSubmitPromise(form) {
    return new Promise(function (resolve, reject) {
        // console.log(form);
        var instance = $(form).parsley();
        instance.validate();
        if (instance.isValid()) {
            $(".price_formate").each(function () {
                var val = $(this).val();
                if (val != '') {
                    var formated = val.replace(/,/g, '');
                    $(this).val(formated);
                }
            });
            // $(".printPdfSubmit").prop('disabled', true);
            var approveOrderUrl = $(form).attr('action');
            var data = $(form).serializeObject();
            // data['_method'] = "post";
            $.post(approveOrderUrl, data, function (res) {
                $(".printPdfSubmit").prop('disabled', false);

                // console.log(res.success);
                if (res.success == true) {
                    resolve(res);
                } else {
                    reject(res);
                }

            }).error(function (res) {
                $(".printPdfSubmit").prop('disabled', false);

                reject(res);
            });;
        }
        else {
            reject("Error. Please check all the fields.");

        }


    });


}
$(document).on("click", '.printPdfSubmit', function () {
    console.log("a");
    var urlRaw = $(this).data('href');
    //    var data = $(".searchFiltersForm")[0].serialize();
    var values = [];
    var dataString = "";
    let url = new URL(urlRaw);
    let params = url.searchParams;
    $("#is_printed").prop('checked', true);
    $('.searchFiltersForm .form-control').each(function () {
        if (this.value != "") {
            values[this.id] = this.value;
            params.append(this.id, this.value);

            //            dataString = dataString + "" + this.id + "=" + this.value + "&";
        }

    });
    $('.printParamsSelect').each(function () {
        // console.log($(this).val());
        if ($(this).val()) {
            values[this.id] = $(this).val();
            params.append(this.id, $(this).val());

            //            dataString = dataString + "" + this.id + "=" + this.value + "&";
        }

    });
    $('.printParamsCheck').each(function () {
        if ($(this).prop('checked')) {
            values[this.id] = 1;
            params.append(this.id, 1);

            //            dataString = dataString + "" + this.id + "=" + this.value + "&";
        } else {
            values[this.id] = 0;
            params.append(this.id, 0);
        }

    });
    var instance = $("#form-work").parsley();
    instance.validate();
    if (instance.isValid()) {
        $(".price_formate").each(function () {
            var val = $(this).val();
            if (val != '') {
                var formated = val.replace(/,/g, '');
                $(this).val(formated);
            }
        });
        // $(".printPdfSubmit").prop('disabled', true);
        var approveOrderUrl = $("#form-work").attr('action');
        var data = $("#form-work").serializeObject();
        // data['_method'] = "post";
        $.post(approveOrderUrl, data, function (res) {
            $(".printPdfSubmit").prop('disabled', false);

            // console.log(res.success);
            if (res.success == true) {
                var finalUrl = url.toString();
                pdfw = window.open(finalUrl, '_blank', 'fullscreen=1,channelmode=1,status=1,resizable=1');
                pdfw.focus();
                // you need to call print after window loads like this
                pdfw.onload = function () {
                    pdfw.print();
                    setTimeout(function () {
                        pdfw.close();
                    }, 120000)
                }
            } else {
                swal("Error!", res.message, "error");
            }

        }).error(function (res) {
            $(".printPdfSubmit").prop('disabled', false);

            showErrors(res);
        });;
    }
    else {
        GritterAlert.showErrorText("Error. Please check all the fields.");

    }

});
function selec2Ajax(elem) {
    var target = $(elem).data('target')
    var url = $(elem).data('ajaxurl');
    var tags = $(elem).data('tags') ? true : false;
    //    console.log(tags);
    //    var $p = $(elem).parent().parent();
    var $p = $('body');
    //    if ($(elem).parents('.modal').length) {
    //        $p = $(elem).parents('.modal');
    //    }

    $(elem).select2({
        // width: '170%',

        cache: true,
        dropdownParent: $p,
        dropdownAutoWidth: true,
        dropdownPosition: 'below',
        dropdownCssClass: "open_below",
        placeholder: $(elem).attr('placeholder'),
        tags: tags,
        minimumInputLength: 0,
        ajax: {
            type: 'GET',
            delay: 200,
            dataType: 'json',
            url: function (params) {
                //console.log(params)
                var term = "";
                if (params) {
                    term = params.term;
                }
                var selectedCont = 0;
                if (target) {
                    var selectedCont = $(target).val();
                }

                //                    var term = _this.select2('data')[0].text;
                return url + "?id=" + selectedCont;
            },
            data: function (params) {
                var query = {

                    q: params.term,

                    page: params.page || 1
                }

                // Query parameters will be ?search=[term]&type=public
                return query;
            },
        }
    }).on('select2:open', function () {
        console.log("a");
        $('.open_below.select2-dropdown--above').attr('id', 'fix');
        $('#fix').removeClass('select2-dropdown--above');
        $('#fix').addClass('select2-dropdown--below');

    });
}
$(document).ready(function () {
    $("#part_number_id_2").on("change", function () {
        // console.log($(this).val())
        var part_number = $(this).val();
        var price_trigger = $(this).hasClass('price_trigger');
        if (!price_trigger)
            return;
        // console.log($(this).val())
        if (part_number && part_number != "" && part_number != 0 && part_number != "" && part_number.indexOf("MN-") != 0 && part_number.indexOf("RN-") != 0) {

            var token = $('meta[name="csrf-token"]').attr('content');

            $.ajax({
                type: "POST",
                url: getPartNumberDetailsUrl,
                data: {

                    part_number_id: part_number,
                    _token: token,

                }, // serializes the form's elements.
                success: function (data) {
                    //console.log(data);
                    if (data.success == "true") {
                        // console.log(data.inventoryItem);
                        $("#unit_price").val(data.inventoryItem.price_rating_0).change();
                    }

                }
            });
        }
    })

    $('.select2_ajax').each(function () {
        var url = $(this).data('url');
        var type = $(this).data('type');
        var placeholder = $(this).attr('placeholder');
        var param = $(this).data('params');
        var param2 = $(this).data('params2');
        // console.log(url, type);
        // var param_val = $("#" + params).val();
        // console.log(params,param_val)
        $(this).select2({
            minimumInputLength: 0,
            width: "100%",
            createTag: function (params) {
                var term = $.trim(params.term);
                var id = term;
                console.log(type, id);
                if (type == 'manufacturer_nos') {
                    id = "_MN_:" + id;
                }
                if (type == 'replacement_nos') {
                    id = "_RN_:" + id;
                }

                return {
                    id: id,
                    text: term,
                    newTag: true // add additional parameters
                }
            },
            ajax: {

                url: url,
                dataType: 'json',
                type: 'GET',
                delay: 200,
                data: function (params) {
                    var query = {

                        q: params.term,
                        type: type,
                        param: param,
                        param_val: $("#" + param).val(),
                        param2: param2,
                        param2_val: $("#" + param2).val(),

                        page: params.page || 1
                    }

                    // Query parameters will be ?search=[term]&type=public
                    return query;
                },
                // processResults: function (data) {
                //     return {
                //         results: data.results
                //     }
                // }



            }
        });
    });

})

// const form = document.getElementById('form-work');
// if (form)
//     form.addEventListener('keypress', function (e) {
//         if (e.keyCode === 13) {
//             e.preventDefault();
//         }
//     });
