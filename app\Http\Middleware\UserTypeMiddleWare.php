<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;

class UserTypeMiddleWare
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle($request, Closure $next, ...$types)
    {
        return $next($request);
        // if (!auth()->check()) {
        //     return redirect('login');
        // }

        // if($ty[])
        $typesMap = [];
        foreach ($types as $type) {
            if ($type == "admin")
                $typeMap[] = User::TYPE_ADMIN;
            if ($type == "super_admin")
                $typeMap[] = User::TYPE_SUPER_ADMIN;
            if ($type == "user")
                $typeMap[] = User::TYPE_USER;
        }
        // dd($typeMap);
        if (in_array(auth()->user()->type, $typeMap)) {
            return $next($request);
        }

        abort(403, 'Unauthorized');
    }
}
