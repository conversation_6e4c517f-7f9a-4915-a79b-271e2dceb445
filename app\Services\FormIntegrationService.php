<?php

namespace App\Services;

use App\Contracts\FormIntegrationAdapterInterface;
use App\Models\FormIntegrationSetting;
use App\Adapters\CsiFormIntegrationAdapter;
use App\Adapters\SapFormIntegrationAdapter;
use Illuminate\Support\Facades\Log;

class FormIntegrationService
{
    private array $adapters = [];

    public function __construct()
    {
        $this->registerAdapters();
    }

    /**
     * Register all available adapters
     */
    private function registerAdapters(): void
    {
        $this->adapters['CSI'] = new CsiFormIntegrationAdapter();
        $this->adapters['SAP'] = new SapFormIntegrationAdapter();
    }

    /**
     * Get adapter for specific ERP system
     */
    public function getAdapter(string $erpSystem): FormIntegrationAdapterInterface
    {
        if (!isset($this->adapters[$erpSystem])) {
            throw new \InvalidArgumentException("No adapter found for ERP system: {$erpSystem}");
        }

        return $this->adapters[$erpSystem];
    }

    /**
     * Get all available ERP systems
     */
    public function getAvailableErpSystems(): array
    {
        return array_keys($this->adapters);
    }

    /**
     * Process form submission through integration setting
     */
    public function processFormSubmission(array $formData, FormIntegrationSetting $integrationSetting): array
    {
        try {
            $erpSystem = $integrationSetting->endpointConfiguration->erp_selection;
            $adapter = $this->getAdapter($erpSystem);

            // Validate form data
            $validationErrors = $adapter->validateFormData($formData, $integrationSetting);
            if (!empty($validationErrors)) {
                return [
                    'success' => false,
                    'errors' => $validationErrors,
                    'data' => null
                ];
            }

            // Transform form data
            $transformedData = $adapter->transformFormData($formData, $integrationSetting);

            // Prepare API data
            $apiData = $adapter->prepareApiData($transformedData, $integrationSetting);

            Log::info('Form integration processing completed', [
                'integration_setting_id' => $integrationSetting->id,
                'erp_system' => $erpSystem,
                'form_id' => $integrationSetting->form_id,
                'endpoint_id' => $integrationSetting->endpoint_configuration_id
            ]);

            return [
                'success' => true,
                'errors' => [],
                'data' => $apiData
            ];

        } catch (\Exception $e) {
            Log::error('Form integration processing failed', [
                'integration_setting_id' => $integrationSetting->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'errors' => ['An error occurred while processing the form submission: ' . $e->getMessage()],
                'data' => null
            ];
        }
    }

    /**
     * Validate integration setting configuration
     */
    public function validateIntegrationSetting(FormIntegrationSetting $integrationSetting): array
    {
        $errors = [];

        try {
            // Check if form exists and is active
            if (!$integrationSetting->form || !$integrationSetting->form->is_active) {
                $errors[] = 'The selected form is not available or inactive.';
            }

            // Check if endpoint configuration exists and is active
            if (!$integrationSetting->endpointConfiguration || !$integrationSetting->endpointConfiguration->is_active) {
                $errors[] = 'The selected endpoint configuration is not available or inactive.';
            }

            // Validate field mappings
            $mappingErrors = $integrationSetting->validateFieldMappings();
            $errors = array_merge($errors, $mappingErrors);

            // Check if adapter exists for the ERP system
            $erpSystem = $integrationSetting->endpointConfiguration->erp_selection ?? null;
            if ($erpSystem && !isset($this->adapters[$erpSystem])) {
                $errors[] = "No adapter available for ERP system: {$erpSystem}";
            }

            // Validate that the adapter supports the process type
            if ($erpSystem && isset($this->adapters[$erpSystem])) {
                $adapter = $this->adapters[$erpSystem];
                $processType = $integrationSetting->endpointConfiguration->process_selection;
                $supportedProcesses = $adapter->getSupportedProcesses();

                if (!in_array($processType, $supportedProcesses)) {
                    $errors[] = "ERP system {$erpSystem} does not support process type: {$processType}";
                }
            }

        } catch (\Exception $e) {
            $errors[] = 'An error occurred while validating the integration setting: ' . $e->getMessage();
        }

        return $errors;
    }

    /**
     * Get form fields for a specific form
     */
    public function getFormFields(int $formId): array
    {
        try {
            $form = \App\Models\Form::find($formId);
            if (!$form) {
                return [];
            }

            $integrationSetting = new FormIntegrationSetting(['form_id' => $formId]);
            $integrationSetting->setRelation('form', $form);

            return $integrationSetting->form_fields;

        } catch (\Exception $e) {
            Log::error('Failed to get form fields', [
                'form_id' => $formId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get endpoint fields for a specific endpoint configuration
     */
    public function getEndpointFields(int $endpointConfigurationId): array
    {
        try {
            $endpointConfig = \App\Models\EndpointConfiguration::find($endpointConfigurationId);
            if (!$endpointConfig) {
                return [];
            }

            return $endpointConfig->body_data_field ?? [];

        } catch (\Exception $e) {
            Log::error('Failed to get endpoint fields', [
                'endpoint_configuration_id' => $endpointConfigurationId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Create field mapping suggestions based on field names
     */
    public function suggestFieldMappings(array $formFields, array $endpointFields): array
    {
        $suggestions = [];

        foreach ($formFields as $formField) {
            $formKey = $formField['key'] ?? '';
            $formLabel = strtolower($formField['label'] ?? '');

            foreach ($endpointFields as $endpointField) {
                $endpointName = $endpointField['name'] ?? '';
                $endpointDescription = strtolower($endpointField['description'] ?? '');

                // Simple matching logic - can be enhanced
                if ($this->fieldsMatch($formKey, $formLabel, $endpointName, $endpointDescription)) {
                    $suggestions[$formKey] = $endpointName;
                    break;
                }
            }
        }

        return $suggestions;
    }

    /**
     * Check if form field matches endpoint field
     */
    private function fieldsMatch(string $formKey, string $formLabel, string $endpointName, string $endpointDescription): bool
    {
        $formKey = strtolower($formKey);
        $endpointName = strtolower($endpointName);

        // Direct key match
        if ($formKey === $endpointName) {
            return true;
        }

        // Partial matches
        $commonMappings = [
            'item' => ['item_code', 'material_number', 'product_code'],
            'quantity' => ['qty', 'amount', 'count'],
            'location' => ['loc', 'warehouse', 'storage'],
            'description' => ['desc', 'notes', 'comment'],
            'date' => ['posting_date', 'transaction_date'],
            'cost' => ['price', 'value', 'amount'],
        ];

        foreach ($commonMappings as $pattern => $variations) {
            if (strpos($formKey, $pattern) !== false || strpos($formLabel, $pattern) !== false) {
                foreach ($variations as $variation) {
                    if (strpos($endpointName, $variation) !== false || strpos($endpointDescription, $variation) !== false) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Get integration statistics
     */
    public function getIntegrationStatistics(): array
    {
        try {
            $totalSettings = FormIntegrationSetting::count();
            $activeSettings = FormIntegrationSetting::where('form_integration_settings.is_active', true)->count();
            $settingsByErp = FormIntegrationSetting::where('form_integration_settings.is_active', true)
                ->join('endpoint_configurations', 'form_integration_settings.endpoint_configuration_id', '=', 'endpoint_configurations.id')
                ->selectRaw('endpoint_configurations.erp_selection, COUNT(*) as count')
                ->groupBy('endpoint_configurations.erp_selection')
                ->pluck('count', 'erp_selection')
                ->toArray();

            return [
                'total_settings' => $totalSettings,
                'active_settings' => $activeSettings,
                'inactive_settings' => $totalSettings - $activeSettings,
                'settings_by_erp' => $settingsByErp,
                'available_erp_systems' => $this->getAvailableErpSystems()
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get integration statistics', [
                'error' => $e->getMessage()
            ]);

            return [
                'total_settings' => 0,
                'active_settings' => 0,
                'inactive_settings' => 0,
                'settings_by_erp' => [],
                'available_erp_systems' => $this->getAvailableErpSystems()
            ];
        }
    }
}
