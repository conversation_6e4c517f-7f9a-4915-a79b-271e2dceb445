<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserGroup;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Gate;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Hash;

class UsersController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = User::with('userGroup')->orderBy('created_at', 'desc');

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('action', function ($row) {
                    $btn = '<div class="btn-group" role="group">';

                    // $btn .= '<a href="' . route('admin.users.show', $row->id) . '" class="btn btn-sm btn-primary">View</a>';
                    $btn .= '<a href="' . route('admin.users.edit', $row->id) . '" class="btn btn-sm btn-info">Edit</a>';

                    $btn .= '<form method="POST" action="' . route('admin.users.destroy', $row->id) . '" style="display:inline-block;" onsubmit="return confirm(\'Are you sure?\')">';
                    $btn .= csrf_field();
                    $btn .= method_field('DELETE');
                    $btn .= '<button type="submit" class="btn btn-sm btn-danger">Delete</button>';
                    $btn .= '</form>';

                    $btn .= '</div>';

                    return $btn;
                })
                ->addColumn('user_group', function ($row) {
                    return $row->userGroup ? $row->userGroup->name : 'No Group';
                })
                ->addColumn('role_type', function ($row) {
                    switch($row->type) {
                        case User::TYPE_SUPER_ADMIN:
                            return '<span class="badge badge-danger">Super Admin</span>';
                        case User::TYPE_ADMIN:
                            return '<span class="badge badge-warning">Admin</span>';
                        case User::TYPE_USER:
                            return '<span class="badge badge-info">User</span>';
                        default:
                            return '<span class="badge badge-secondary">Unknown</span>';
                    }
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('Y-m-d');
                })
                ->rawColumns(['action', 'role_type'])
                ->make(true);
        }

        return view('admin.users.index');
    }

    public function create()
    {
        $userGroups = UserGroup::active()->pluck('name', 'id');
        return view('admin.users.create', compact('userGroups'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'type' => 'required|integer|in:1,2,3',
            'user_group_id' => 'nullable|exists:user_groups,id'
        ]);

        $data = $request->all();
        $data['password'] = Hash::make($request->password);

        User::create($data);

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    public function show(User $user)
    {
        $user->load('userGroup');
        return view('admin.users.show', compact('user'));
    }

    public function edit(User $user)
    {
        $userGroups = UserGroup::active()->pluck('name', 'id');
        return view('admin.users.edit', compact('user', 'userGroups'));
    }

    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8|confirmed',
            'type' => 'required|integer|in:1,2,3',
            'user_group_id' => 'nullable|exists:user_groups,id'
        ]);

        $data = $request->except('password');

        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        $user->update($data);

        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    public function destroy(User $user)
    {
        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    public function massDestroy(Request $request)
    {
        User::whereIn('id', $request->ids)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }
}
