@extends('layouts.admin')
@section('pageTitle', 'Endpoint Configurations')

@push('styles')
<style>
    .table th {
        background-color: #f8f9fa;
        border-top: none;
        font-weight: 600;
    }
    .badge {
        font-size: 0.75rem;
    }
    .btn-group .btn {
        margin-right: 2px;
    }
    .btn-group .btn:last-child {
        margin-right: 0;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card card-default">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4><i class="fa fa-cogs"></i> {{ trans('cruds.endpointConfiguration.title') }}</h4>
                        </div>
                        <div class="col-auto">
                            <a class="btn btn-success" href="{{ route('admin.endpoint-configurations.create') }}">
                                <i class="fa fa-plus"></i> {{ trans('global.add') }} {{ trans('cruds.endpointConfiguration.title_singular') }}
                            </a>
                        </div>
                    </div>
                </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-striped table-hover datatable datatable-EndpointConfiguration">
                <thead>
                    <tr>
                        <th width="10">

                        </th>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.name') }}
                        </th>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.url') }}
                        </th>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.erp_selection') }}
                        </th>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.process_selection') }}
                        </th>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.endpoint_type') }}
                        </th>
                        <th>
                            {{ trans('cruds.endpointConfiguration.fields.is_active') }}
                        </th>
                        <th>
                            Created By
                        </th>
                        <th>
                            Updated At
                        </th>
                        <th>
                            {{ trans('global.actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($endpointConfigurations as $key => $endpointConfiguration)
                        <tr data-entry-id="{{ $endpointConfiguration->id }}">
                            <td>

                            </td>
                            <td>
                                {{ $endpointConfiguration->name ?? '' }}
                            </td>
                            <td>
                                <span title="{{ $endpointConfiguration->url }}">
                                    {{ Str::limit($endpointConfiguration->url, 50) }}
                                </span>
                            </td>
                            <td>
                                {{ $endpointConfiguration->erp_selection ?? '' }}
                            </td>
                            <td>
                                {{ $endpointConfiguration->process_selection ?? '' }}
                            </td>
                            <td>
                                {{ $endpointConfiguration->endpoint_type ?? '' }}
                            </td>
                            <td>
                                <span class="badge badge-{{ $endpointConfiguration->is_active ? 'success' : 'danger' }}">
                                    {{ $endpointConfiguration->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </td>
                            <td>
                                {{ $endpointConfiguration->createdBy->name ?? 'N/A' }}
                                <br><small class="text-muted">{{ $endpointConfiguration->created_at->format('M d, Y') }}</small>
                            </td>
                            <td>
                                {{ $endpointConfiguration->updatedBy->name ?? 'N/A' }}
                                <br><small class="text-muted">{{ $endpointConfiguration->updated_at->format('M d, Y H:i') }}</small>
                            </td>
                            <td>
                                @include('admin.endpoint-configurations.partials.actions', ['row' => $endpointConfiguration])
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
@parent
<script>
    $(function () {
        let dtButtons = $.extend(true, [], $.fn.dataTable.defaults.buttons)

        $.extend(true, $.fn.dataTable.defaults, {
            orderCellsTop: true,
            order: [[ 1, 'desc' ]],
            pageLength: 25,
        });
        let table = $('.datatable-EndpointConfiguration:not(.ajaxTable)').DataTable({ buttons: dtButtons })
        $('a[data-toggle="tab"]').on('shown.bs.tab click', function(e){
            $($.fn.dataTable.tables(true)).DataTable()
                .columns.adjust();
        });

    })
</script>
@endsection
