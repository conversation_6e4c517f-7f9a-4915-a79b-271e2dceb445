<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserGroup;
use Illuminate\Http\Request;
use Gate;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;

class UserGroupController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = UserGroup::withCount('users')->orderBy('created_at', 'desc');

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" name="ids[]" value="' . $row->id . '" data-entry-id="' . $row->id . '">';
                })
                ->addColumn('action', function ($row) {
                    $btn = '<div class="btn-group" role="group">';

                    // $btn .= '<a href="' . route('admin.usergroups.show', $row->id) . '" class="btn btn-sm btn-primary">View</a>';
                    $btn .= '<a href="' . route('admin.usergroups.edit', $row->id) . '" class="btn btn-sm btn-info">Edit</a>';

                    $btn .= '<form method="POST" action="' . route('admin.usergroups.destroy', $row->id) . '" style="display:inline-block;" onsubmit="return confirm(\'Are you sure?\')">';
                    $btn .= csrf_field();
                    $btn .= method_field('DELETE');
                    $btn .= '<button type="submit" class="btn btn-sm btn-danger">Delete</button>';
                    $btn .= '</form>';

                    $btn .= '</div>';

                    return $btn;
                })
                ->addColumn('status', function ($row) {
                    return $row->is_active ?
                        '<span class="badge badge-success">Active</span>' :
                        '<span class="badge badge-danger">Inactive</span>';
                })
                ->addColumn('users_count', function ($row) {
                    return $row->users_count;
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('Y-m-d H:i:s');
                })
                ->rawColumns(['checkbox', 'action', 'status'])
                ->make(true);
        }

        return view('admin.usergroups.index');
    }

    public function create()
    {
        return view('admin.usergroups.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:user_groups,name',
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        UserGroup::create($request->all());

        return redirect()->route('admin.usergroups.index')
            ->with('success', 'User Group created successfully.');
    }

    public function show(UserGroup $usergroup)
    {
        $usergroup->load('users');
        return view('admin.usergroups.show', compact('usergroup'));
    }

    public function edit(UserGroup $usergroup)
    {
        return view('admin.usergroups.edit', compact('usergroup'));
    }

    public function update(Request $request, UserGroup $usergroup)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:user_groups,name,' . $usergroup->id,
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        $usergroup->update($request->all());

        return redirect()->route('admin.usergroups.index')
            ->with('success', 'User Group updated successfully.');
    }

    public function destroy(UserGroup $usergroup)
    {
        $usergroup->delete();

        return redirect()->route('admin.usergroups.index')
            ->with('success', 'User Group deleted successfully.');
    }

    public function massDestroy(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:user_groups,id'
        ]);

        UserGroup::whereIn('id', $request->ids)->delete();

        return response()->json([
            'success' => true,
            'message' => 'User Groups deleted successfully.'
        ]);
    }
}
