@extends('layouts.admin')
@section('pageTitle', 'Admin Dashboard')

@section('content')
<div class="card card-default">
    <div class="card-header">
        <h4>Admin Dashboard</h4>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-12">
                <p>Welcome to the Admin Dashboard!</p>
                
                @if(auth()->check() && auth()->user()->type === 1)
                    <div class="alert alert-info">
                        <strong>Super Admin Access:</strong> You have access to all system features including Endpoint Configuration.
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fa fa-cogs fa-3x text-primary mb-3"></i>
                                    <h5>Endpoint Configurations</h5>
                                    <p>Manage API endpoint configurations for ERP integration.</p>
                                    <a href="{{ route('admin.endpoint-configurations.index') }}" class="btn btn-primary">
                                        Manage Configurations
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
