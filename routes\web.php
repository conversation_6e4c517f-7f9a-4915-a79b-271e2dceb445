<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::redirect('/', '/login');

Auth::routes();


Route::group(
    ['prefix' => 'admin', 'as' => 'admin.', 'namespace' => 'App\Http\Controllers\Admin', 'middleware' => ['user_type:admin']],
    function () {

        // Route::middleware('user_type:admin')->group(function () {
        // Route::get('/admin', fn() => view('admin'));
        Route::delete('permissions/destroy', 'PermissionsController@massDestroy')->name('permissions.massDestroy');
        Route::resource('permissions', 'PermissionsController');

        // Roles
        Route::delete('roles/destroy', 'RolesController@massDestroy')->name('roles.massDestroy');
        Route::resource('roles', 'RolesController');

        // Users
        Route::delete('users/destroy', 'UsersController@massDestroy')->name('users.massDestroy');
        Route::resource('users', 'UsersController');

        // User Groups
        Route::delete('usergroups/destroy', 'UserGroupController@massDestroy')->name('usergroups.massDestroy');
        Route::resource('usergroups', 'UserGroupController');

        // Forms
        Route::get('forms/{form}/preview', 'FormsController@preview')->name('forms.preview');
        Route::resource('forms', 'FormsController');
        Route::get('/', function () {
            return view('welcome');
        });
    }
);

Route::middleware('user_type:super_admin')->group(function () {
    Route::get('/super-admin', fn() => view('super_admin'));

    // Route::get('/', function () {
    //     return view('welcome');
    // });
});

// TODO: Move back to super_admin middleware when user management system is ready
// Endpoint Configuration routes - temporarily accessible by any authenticated user
Route::middleware('auth')->prefix('admin')->name('admin.')->group(function () {
    Route::resource('endpoint-configurations', App\Http\Controllers\Admin\EndpointConfigurationController::class);

    // AJAX routes for form integration (must be defined before resource routes)
    Route::get('form-integration-settings/get-form-fields', [App\Http\Controllers\Admin\FormIntegrationSettingController::class, 'getFormFields'])
        ->name('form-integration-settings.get-form-fields');
    Route::get('form-integration-settings/get-endpoint-fields', [App\Http\Controllers\Admin\FormIntegrationSettingController::class, 'getEndpointFields'])
        ->name('form-integration-settings.get-endpoint-fields');
    Route::get('form-integration-settings/get-field-mapping-suggestions', [App\Http\Controllers\Admin\FormIntegrationSettingController::class, 'getFieldMappingSuggestions'])
        ->name('form-integration-settings.get-field-mapping-suggestions');

    // Form Integration Setting resource routes
    Route::resource('form-integration-settings', App\Http\Controllers\Admin\FormIntegrationSettingController::class);
});
