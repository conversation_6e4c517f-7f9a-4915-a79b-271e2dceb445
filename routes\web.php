<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Auth::routes();


Route::middleware('user_type:admin')->group(function () {
    Route::get('/admin', fn() => view('admin'));
});

Route::middleware('user_type:super_admin')->group(function () {
    Route::get('/super-admin', fn() => view('super_admin'));
    Route::get('/', function () {
        return view('welcome');
    });
});

// TODO: Move back to super_admin middleware when user management system is ready
// Endpoint Configuration routes - temporarily accessible by any authenticated user
Route::middleware('auth')->prefix('admin')->name('admin.')->group(function () {
    Route::resource('endpoint-configurations', App\Http\Controllers\Admin\EndpointConfigurationController::class);
});
