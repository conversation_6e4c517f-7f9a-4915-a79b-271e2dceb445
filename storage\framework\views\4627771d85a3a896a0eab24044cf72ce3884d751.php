<?php $__env->startSection('pageTitle', 'Create Endpoint Configuration'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .required:after {
        content: " *";
        color: red;
    }
    .help-block {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    .form-group {
        margin-bottom: 1.5rem;
    }
    .card-header h4 {
        margin: 0;
        color: #495057;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card card-default">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4><i class="fa fa-plus-circle"></i> <?php echo e(trans('global.create')); ?> <?php echo e(trans('cruds.endpointConfiguration.title_singular')); ?></h4>
                        </div>
                        <div class="col-auto">
                            <a href="<?php echo e(route('admin.endpoint-configurations.index')); ?>" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> <?php echo e(trans('global.back_to_list')); ?>

                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo e(route('admin.endpoint-configurations.store')); ?>" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="name"><?php echo e(trans('cruds.endpointConfiguration.fields.name')); ?></label>
                                    <input class="form-control <?php echo e($errors->has('name') ? 'is-invalid' : ''); ?>" type="text" name="name" id="name" value="<?php echo e(old('name', '')); ?>" required placeholder="Enter configuration name">
                                    <?php if($errors->has('name')): ?>
                                        <div class="invalid-feedback">
                                            <?php echo e($errors->first('name')); ?>

                                        </div>
                                    <?php endif; ?>
                                    <small class="help-block"><?php echo e(trans('cruds.endpointConfiguration.fields.name_helper')); ?></small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="erp_selection"><?php echo e(trans('cruds.endpointConfiguration.fields.erp_selection')); ?></label>
                                    <select class="form-control select2 <?php echo e($errors->has('erp_selection') ? 'is-invalid' : ''); ?>" name="erp_selection" id="erp_selection" required>
                                        <?php $__currentLoopData = $erpOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($key); ?>" <?php echo e(old('erp_selection', 'CSI') == $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php if($errors->has('erp_selection')): ?>
                                        <div class="invalid-feedback">
                                            <?php echo e($errors->first('erp_selection')); ?>

                                        </div>
                                    <?php endif; ?>
                                    <small class="help-block"><?php echo e(trans('cruds.endpointConfiguration.fields.erp_selection_helper')); ?></small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="process_selection"><?php echo e(trans('cruds.endpointConfiguration.fields.process_selection')); ?></label>
                                    <select class="form-control select2 <?php echo e($errors->has('process_selection') ? 'is-invalid' : ''); ?>" name="process_selection" id="process_selection" required>
                                        <option value=""><?php echo e(trans('global.pleaseSelect')); ?></option>
                                        <?php $__currentLoopData = $processOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($key); ?>" <?php echo e(old('process_selection', '') == $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php if($errors->has('process_selection')): ?>
                                        <div class="invalid-feedback">
                                            <?php echo e($errors->first('process_selection')); ?>

                                        </div>
                                    <?php endif; ?>
                                    <small class="help-block"><?php echo e(trans('cruds.endpointConfiguration.fields.process_selection_helper')); ?></small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="endpoint_type"><?php echo e(trans('cruds.endpointConfiguration.fields.endpoint_type')); ?></label>
                                    <select class="form-control select2 <?php echo e($errors->has('endpoint_type') ? 'is-invalid' : ''); ?>" name="endpoint_type" id="endpoint_type" required>
                                        <?php $__currentLoopData = $endpointTypeOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($key); ?>" <?php echo e(old('endpoint_type', 'API') == $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php if($errors->has('endpoint_type')): ?>
                                        <div class="invalid-feedback">
                                            <?php echo e($errors->first('endpoint_type')); ?>

                                        </div>
                                    <?php endif; ?>
                                    <small class="help-block"><?php echo e(trans('cruds.endpointConfiguration.fields.endpoint_type_helper')); ?></small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="required" for="url"><?php echo e(trans('cruds.endpointConfiguration.fields.url')); ?></label>
                            <textarea class="form-control <?php echo e($errors->has('url') ? 'is-invalid' : ''); ?>" name="url" id="url" rows="3" required placeholder="IDORequestService/ido/invoke/SLStockActItems?method=ItemMiscReceiptSp"><?php echo e(old('url', '')); ?></textarea>
                            <?php if($errors->has('url')): ?>
                                <div class="invalid-feedback">
                                    <?php echo e($errors->first('url')); ?>

                                </div>
                            <?php endif; ?>
                            <small class="help-block"><?php echo e(trans('cruds.endpointConfiguration.fields.url_helper')); ?></small>
                        </div>

                        <div class="form-group">
                            <label for="body_data_field"><?php echo e(trans('cruds.endpointConfiguration.fields.body_data_field')); ?></label>
                            <div class="card">
                                <div class="card-header">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h6 class="mb-0">Field Mapping Configuration</h6>
                                        </div>
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-sm btn-success" id="addFieldBtn">
                                                <i class="fa fa-plus"></i> Add Field
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div id="fieldMappingContainer">
                                        <!-- Dynamic fields will be added here -->
                                    </div>
                                    <div id="noFieldsMessage" class="text-center text-muted py-3">
                                        <i class="fa fa-info-circle"></i> No fields added yet. Click "Add Field" to start.
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="body_data_field" id="body_data_field_hidden" value="<?php echo e(old('body_data_field', '')); ?>">
                            <?php if($errors->has('body_data_field')): ?>
                                <div class="invalid-feedback d-block">
                                    <?php echo e($errors->first('body_data_field')); ?>

                                </div>
                            <?php endif; ?>
                            <small class="help-block"><?php echo e(trans('cruds.endpointConfiguration.fields.body_data_field_helper')); ?></small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="hidden" name="is_active" value="0">
                                <input class="custom-control-input" type="checkbox" name="is_active" id="is_active" value="1" <?php echo e(old('is_active', 1) == 1 ? 'checked' : ''); ?>>
                                <label class="custom-control-label" for="is_active">
                                    <?php echo e(trans('cruds.endpointConfiguration.fields.is_active')); ?>

                                </label>
                                <?php if($errors->has('is_active')): ?>
                                    <div class="invalid-feedback d-block">
                                        <?php echo e($errors->first('is_active')); ?>

                                    </div>
                                <?php endif; ?>
                                <small class="help-block d-block"><?php echo e(trans('cruds.endpointConfiguration.fields.is_active_helper')); ?></small>
                            </div>
                        </div>

                        <hr>

                        <div class="form-group mb-0">
                            <button class="btn btn-primary" type="submit">
                                <i class="fa fa-save"></i> <?php echo e(trans('global.save')); ?>

                            </button>
                            <a class="btn btn-secondary ml-2" href="<?php echo e(route('admin.endpoint-configurations.index')); ?>">
                                <i class="fa fa-times"></i> <?php echo e(trans('global.cancel')); ?>

                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    let fieldCounter = 0;

    // Data types options
    const dataTypes = [
        'string', 'integer', 'float', 'boolean', 'date', 'datetime', 'email', 'url', 'text', 'json'
    ];

    // Load existing data if editing
    const existingData = $('#body_data_field_hidden').val();
    console.log('Existing data:', existingData);
    if (existingData) {
        try {
            const fields = JSON.parse(existingData);
            console.log('Parsed fields:', fields);
            if (Array.isArray(fields)) {
                fields.forEach(field => addFieldRow(field));
                updateNoFieldsMessage();
            }
        } catch (e) {
            console.log('Error parsing JSON data:', e);
            console.log('Raw data:', existingData);
        }
    }

    // Add field button click
    $('#addFieldBtn').click(function() {
        addFieldRow();
    });

    // Function to add a new field row
    function addFieldRow(fieldData = {}) {
        console.log('Adding field row with data:', fieldData);
        fieldCounter++;

        const fieldRow = `
            <div class="field-row border rounded p-3 mb-3" data-field-id="${fieldCounter}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Field Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-sm field-name"
                                   placeholder="e.g., item_code" value="${fieldData.name || ''}" required>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Data Type</label>
                            <select class="form-control form-control-sm field-datatype">
                                ${dataTypes.map(type =>
                                    `<option value="${type}" ${fieldData.datatype === type ? 'selected' : ''}>${type}</option>`
                                ).join('')}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Max Length</label>
                            <input type="number" class="form-control form-control-sm field-maxlength"
                                   placeholder="255" value="${fieldData.maxlength !== null && fieldData.maxlength !== undefined ? fieldData.maxlength : ''}" min="1">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Required</label>
                            <select class="form-control form-control-sm field-required">
                                <option value="false" ${fieldData.required === false ? 'selected' : ''}>No</option>
                                <option value="true" ${fieldData.required === true ? 'selected' : ''}>Yes</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Default Value</label>
                            <input type="text" class="form-control form-control-sm field-default"
                                   placeholder="Optional" value="${fieldData.default !== null && fieldData.default !== undefined ? fieldData.default : ''}">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">&nbsp;</label>
                            <button type="button" class="btn btn-sm btn-danger btn-block remove-field">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-11">
                        <div class="form-group mb-0">
                            <label class="small font-weight-bold">Description</label>
                            <input type="text" class="form-control form-control-sm field-description"
                                   placeholder="Optional field description" value="${fieldData.description || ''}">
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('#fieldMappingContainer').append(fieldRow);
        updateNoFieldsMessage();
        updateHiddenField();
    }

    // Remove field
    $(document).on('click', '.remove-field', function() {
        $(this).closest('.field-row').remove();
        updateNoFieldsMessage();
        updateHiddenField();
    });

    // Update hidden field when any input changes
    $(document).on('input change', '.field-row input, .field-row select', function() {
        updateHiddenField();
    });

    // Function to update the hidden field with JSON data
    function updateHiddenField() {
        const fields = [];

        $('.field-row').each(function() {
            const row = $(this);
            const field = {
                name: row.find('.field-name').val().trim(),
                datatype: row.find('.field-datatype').val(),
                maxlength: row.find('.field-maxlength').val() ? parseInt(row.find('.field-maxlength').val()) : null,
                required: row.find('.field-required').val() === 'true',
                default: row.find('.field-default').val().trim() || null,
                description: row.find('.field-description').val().trim() || null
            };

            if (field.name) {
                fields.push(field);
            }
        });

        $('#body_data_field_hidden').val(JSON.stringify(fields));
    }

    // Function to show/hide no fields message
    function updateNoFieldsMessage() {
        if ($('.field-row').length === 0) {
            $('#noFieldsMessage').show();
        } else {
            $('#noFieldsMessage').hide();
        }
    }

    // Initial state
    updateNoFieldsMessage();
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Git Data Capture\application\resources\views/admin/endpoint-configurations/create.blade.php ENDPATH**/ ?>