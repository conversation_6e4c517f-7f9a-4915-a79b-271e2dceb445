<?php $__env->startSection('pageTitle', 'Create Endpoint Configuration'); ?>

<?php $__env->startSection('content'); ?>
<div class="card card-default">
    <div class="card-header">
        <h4><?php echo e(trans('global.create')); ?> <?php echo e(trans('cruds.endpointConfiguration.title_singular')); ?></h4>
    </div>
    <div class="card-body">
        <form method="POST" action="<?php echo e(route('admin.endpoint-configurations.store')); ?>" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            
            <div class="form-group">
                <label class="required" for="name"><?php echo e(trans('cruds.endpointConfiguration.fields.name')); ?></label>
                <input class="form-control <?php echo e($errors->has('name') ? 'is-invalid' : ''); ?>" type="text" name="name" id="name" value="<?php echo e(old('name', '')); ?>" required>
                <?php if($errors->has('name')): ?>
                    <div class="invalid-feedback">
                        <?php echo e($errors->first('name')); ?>

                    </div>
                <?php endif; ?>
                <span class="help-block"><?php echo e(trans('cruds.endpointConfiguration.fields.name_helper')); ?></span>
            </div>

            <div class="form-group">
                <label class="required" for="url"><?php echo e(trans('cruds.endpointConfiguration.fields.url')); ?></label>
                <textarea class="form-control <?php echo e($errors->has('url') ? 'is-invalid' : ''); ?>" name="url" id="url" rows="3" required><?php echo e(old('url', '')); ?></textarea>
                <?php if($errors->has('url')): ?>
                    <div class="invalid-feedback">
                        <?php echo e($errors->first('url')); ?>

                    </div>
                <?php endif; ?>
                <span class="help-block"><?php echo e(trans('cruds.endpointConfiguration.fields.url_helper')); ?></span>
            </div>

            <div class="form-group">
                <label for="body_data_field"><?php echo e(trans('cruds.endpointConfiguration.fields.body_data_field')); ?></label>
                <textarea class="form-control <?php echo e($errors->has('body_data_field') ? 'is-invalid' : ''); ?>" name="body_data_field" id="body_data_field" rows="5"><?php echo e(old('body_data_field', '')); ?></textarea>
                <?php if($errors->has('body_data_field')): ?>
                    <div class="invalid-feedback">
                        <?php echo e($errors->first('body_data_field')); ?>

                    </div>
                <?php endif; ?>
                <span class="help-block"><?php echo e(trans('cruds.endpointConfiguration.fields.body_data_field_helper')); ?></span>
            </div>

            <div class="form-group">
                <label class="required" for="erp_selection"><?php echo e(trans('cruds.endpointConfiguration.fields.erp_selection')); ?></label>
                <select class="form-control select2 <?php echo e($errors->has('erp_selection') ? 'is-invalid' : ''); ?>" name="erp_selection" id="erp_selection" required>
                    <?php $__currentLoopData = $erpOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($key); ?>" <?php echo e(old('erp_selection', 'CSI') == $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <?php if($errors->has('erp_selection')): ?>
                    <div class="invalid-feedback">
                        <?php echo e($errors->first('erp_selection')); ?>

                    </div>
                <?php endif; ?>
                <span class="help-block"><?php echo e(trans('cruds.endpointConfiguration.fields.erp_selection_helper')); ?></span>
            </div>

            <div class="form-group">
                <label class="required" for="process_selection"><?php echo e(trans('cruds.endpointConfiguration.fields.process_selection')); ?></label>
                <select class="form-control select2 <?php echo e($errors->has('process_selection') ? 'is-invalid' : ''); ?>" name="process_selection" id="process_selection" required>
                    <option value=""><?php echo e(trans('global.pleaseSelect')); ?></option>
                    <?php $__currentLoopData = $processOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($key); ?>" <?php echo e(old('process_selection', '') == $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <?php if($errors->has('process_selection')): ?>
                    <div class="invalid-feedback">
                        <?php echo e($errors->first('process_selection')); ?>

                    </div>
                <?php endif; ?>
                <span class="help-block"><?php echo e(trans('cruds.endpointConfiguration.fields.process_selection_helper')); ?></span>
            </div>

            <div class="form-group">
                <label class="required" for="endpoint_type"><?php echo e(trans('cruds.endpointConfiguration.fields.endpoint_type')); ?></label>
                <select class="form-control select2 <?php echo e($errors->has('endpoint_type') ? 'is-invalid' : ''); ?>" name="endpoint_type" id="endpoint_type" required>
                    <?php $__currentLoopData = $endpointTypeOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($key); ?>" <?php echo e(old('endpoint_type', 'API') == $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <?php if($errors->has('endpoint_type')): ?>
                    <div class="invalid-feedback">
                        <?php echo e($errors->first('endpoint_type')); ?>

                    </div>
                <?php endif; ?>
                <span class="help-block"><?php echo e(trans('cruds.endpointConfiguration.fields.endpoint_type_helper')); ?></span>
            </div>

            <div class="form-group">
                <div class="form-check <?php echo e($errors->has('is_active') ? 'is-invalid' : ''); ?>">
                    <input type="hidden" name="is_active" value="0">
                    <input class="form-check-input" type="checkbox" name="is_active" id="is_active" value="1" <?php echo e(old('is_active', 1) == 1 ? 'checked' : ''); ?>>
                    <label class="form-check-label" for="is_active">
                        <?php echo e(trans('cruds.endpointConfiguration.fields.is_active')); ?>

                    </label>
                </div>
                <?php if($errors->has('is_active')): ?>
                    <div class="invalid-feedback">
                        <?php echo e($errors->first('is_active')); ?>

                    </div>
                <?php endif; ?>
                <span class="help-block"><?php echo e(trans('cruds.endpointConfiguration.fields.is_active_helper')); ?></span>
            </div>

            <div class="form-group">
                <button class="btn btn-danger" type="submit">
                    <?php echo e(trans('global.save')); ?>

                </button>
                <a class="btn btn-secondary" href="<?php echo e(route('admin.endpoint-configurations.index')); ?>">
                    <?php echo e(trans('global.cancel')); ?>

                </a>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Git Data Capture\application\resources\views/admin/endpoint-configurations/create.blade.php ENDPATH**/ ?>