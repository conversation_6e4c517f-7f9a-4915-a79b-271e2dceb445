<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<!-- begin::Head -->

<head>
    <meta charset="utf-8" />
    <title><?php echo $__env->yieldContent('pageTitle'); ?> | <?php echo e(env('APP_NAME')); ?> </title>
    <meta name="description" content="Latest updates and statistic charts">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <!--begin::Fonts -->
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no" />
    <link rel="apple-touch-icon" href="/components/pages//ico/60.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/components/pages//ico/76.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/components/pages//ico/120.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/components/pages//ico/152.png">
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta content="" name="description" />
    <meta content="" name="author" />
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <link href="https://cdn.datatables.net/1.10.22/css/dataTables.bootstrap4.min.css" rel="stylesheet" type="text/css"
        media="screen" />
    <link href="https://cdn.datatables.net/responsive/2.2.6/css/responsive.dataTables.min.css" rel="stylesheet"
        type="text/css" media="screen" />
    <link href="https://cdn.datatables.net/fixedcolumns/3.3.1/css/fixedColumns.bootstrap4.min.css" rel="stylesheet"
        type="text/css" media="screen" />
    <link href="https://cdn.datatables.net/fixedheader/3.1.7/css/fixedHeader.bootstrap4.min.css" rel="stylesheet"
        type="text/css" media="screen" />
    <link href="https://cdn.datatables.net/select/1.3.1/css/select.dataTables.min.css" rel="stylesheet" type="text/css"
        media="screen" />


    <link href="/components/assets/plugins/pace/pace-theme-flash.css" rel="stylesheet" type="text/css" />
    <link href="/components/assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />

    <link href="/components/assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css" />
    <link href="/components/assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css"
        media="screen" />
    <link href="/components/assets/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"
        media="screen" />
    <link href="/components/assets/plugins/switchery/css/switchery.min.css" rel="stylesheet" type="text/css"
        media="screen" />

    <link href="/components/assets/plugins/jquery-metrojs/MetroJs.css" rel="stylesheet" type="text/css"
        media="screen" />
    <link href="/components/assets/plugins/codrops-dialogFx/dialog.css" rel="stylesheet" type="text/css"
        media="screen" />
    <link href="/components/assets/plugins/codrops-dialogFx/dialog-sandra.css" rel="stylesheet" type="text/css"
        media="screen" />
    <link href="/components/assets/plugins/owl-carousel/assets/owl.carousel.css" rel="stylesheet" type="text/css"
        media="screen" />
    <link href="/components/assets/plugins/jquery-nouislider/jquery.nouislider.css" rel="stylesheet" type="text/css"
        media="screen" />




    <link href="/components/assets/plugins/bootstrap-tag/bootstrap-tagsinput.css" rel="stylesheet" type="text/css" />
    <link href="/components/assets/plugins/dropzone/css/dropzone.css" rel="stylesheet" type="text/css" />
    <link href="/components/assets/plugins/bootstrap-datepicker/css/datepicker3.css" rel="stylesheet" type="text/css"
        media="screen">
    <!--<link href="/components/assets/plugins/summernote/css/summernote.css" rel="stylesheet" type="text/css" media="screen">-->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.15/dist/summernote.min.css" rel="stylesheet"
        type="text/css" media="screen">
    <link rel="stylesheet" href="/components/assets/plugins/gritter/jquery.gritter.css">

    <link href="/components/assets/plugins/bootstrap-daterangepicker/daterangepicker-bs3.css" rel="stylesheet"
        type="text/css" media="screen">
    <link href="/components/assets/plugins/bootstrap-timepicker/bootstrap-timepicker.min.css" rel="stylesheet"
        type="text/css" media="screen">

    <link href="/components/pages//css/pages-icons.css" rel="stylesheet" type="text/css">
    <link class="main-stylesheet" href="/components/pages//css/themes/corporate.css" rel="stylesheet"
        type="text/css" />
    <link href="/components/assets/css/style.css?ref=12" rel="stylesheet" type="text/css" />

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>

<!-- end::Head -->

<!-- begin::Body -->

<body class="fixed-header menu-pin menu-behind">
    <!-- BEGIN SIDEBPANEL-->
    <?php echo $__env->make('include.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <!-- END SIDEBAR -->
    <!-- END SIDEBPANEL-->
    <!-- START PAGE-CONTAINER -->
    <div class="page-container ">
        <!-- START HEADER -->
        <?php echo $__env->make('include.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- END HEADER -->
        <!-- START PAGE CONTENT WRAPPER -->
        <div class="page-content-wrapper ">
            <!-- START PAGE CONTENT -->
            <div class="content ">
                <!-- START JUMBOTRON -->
                <!--          <div class="jumbotron" data-pages="parallax">
                                <div class=" container-fluid   container-fixed-lg sm-p-l-0 sm-p-r-0">
                                  <div class="inner">
                                     START BREADCRUMB
                                    <ol class="breadcrumb">
                                      <li class="breadcrumb-item"><a href="#">Pages</a></li>
                                      <li class="breadcrumb-item active">Blank template</li>
                                    </ol>
                                     END BREADCRUMB
                                  </div>
                                </div>
                              </div>-->
                <!-- END JUMBOTRON -->
                <!-- START CONTAINER FLUID -->
                <div class=" container-fluid" style="z-index: 9;">
                    <!-- BEGIN PlACE PAGE CONTENT HERE -->
                    <?php echo $__env->yieldContent('content'); ?>
                    <!-- END PLACE PAGE CONTENT HERE -->
                </div>
                <!-- END CONTAINER FLUID -->
            </div>
            <!-- END PAGE CONTENT -->
            <!-- START COPYRIGHT -->
            <!-- START CONTAINER FLUID -->
            <!-- START CONTAINER FLUID -->
            

            <div class="pgn-wrapper" data-position="top-right" style="top: 59px;">
                <div class="pgn push-on-sidebar-open pgn-bar">
                    <?php echo $__env->make('partials.alerts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>

            </div>
            <!-- END COPYRIGHT -->
        </div>
        <!-- END PAGE CONTENT WRAPPER -->
    </div>
    <!-- END PAGE CONTAINER -->


    <!-- BEGIN VENDOR JS -->
    <script src="/components/assets/plugins/pace/pace.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/jquery/jquery-1.11.1.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/modernizr.custom.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/jquery-ui/jquery-ui.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/tether/js/tether.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/jquery/jquery-easy.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/jquery-ios-list/jquery.ioslist.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/jquery-actual/jquery.actual.min.js"></script>
    <script src="/components/assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js"></script>
    <script type="text/javascript" src="/components/assets/plugins/select2/js/select2.full.min.js"></script>
    <script type="text/javascript" src="/components/assets/plugins/classie/classie.js"></script>
    <script src="/components/assets/plugins/switchery/js/switchery.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/bootstrap3-wysihtml5/bootstrap3-wysihtml5.all.min.js"></script>

    <script type="text/javascript" src="/components/assets/plugins/jquery-autonumeric/autoNumeric.js"></script>
    <script type="text/javascript" src="/components/assets/plugins/dropzone/dropzone.min.js"></script>
    <script type="text/javascript" src="/components/assets/plugins/bootstrap-tag/bootstrap-tagsinput.min.js"></script>
    <script type="text/javascript" src="/components/assets/plugins/jquery-inputmask/jquery.inputmask.min.js"></script>
    <script src="/components/assets/plugins/bootstrap-form-wizard/js/jquery.bootstrap.wizard.min.js" type="text/javascript">
    </script>
    <script src="/components/assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/bootstrap-datepicker/js/bootstrap-datepicker.js" type="text/javascript">
    </script>
    <!--<script src="/components/assets/plugins/summernote/js/summernote.min.js" type="text/javascript"></script>-->
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.15/dist/summernote.min.js"></script>

    <script src="/components/assets/plugins/moment/moment.min.js"></script>

    <script src="/components/assets/plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
    <script src="/components/assets/plugins/bootstrap-timepicker/bootstrap-timepicker.min.js"></script>

    <script src="/components/assets/plugins/jquery-metrojs/MetroJs.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/imagesloaded/imagesloaded.pkgd.min.js"></script>
    <script src="/components/assets/plugins/jquery-isotope/isotope.pkgd.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/codrops-dialogFx/dialogFx.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/owl-carousel/owl.carousel.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/jquery-nouislider/jquery.nouislider.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/jquery-nouislider/jquery.liblink.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/parsley/parsley.min.js"></script>

    <script src="/components/assets/plugins/bootstrap-typehead/typeahead.bundle.min.js"></script>
    <script src="/components/assets/plugins/bootstrap-typehead/typeahead.jquery.min.js"></script>
    <script src="/components/assets/plugins/handlebars/handlebars-v4.0.5.js"></script>
    <script src="/components/assets/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="/components/assets/plugins/gritter/jquery.gritter.min.js"></script>
    <script src="/components/assets/plugins/gritter/gritter_alert.js"></script>
    <script src="/components/assets/plugins/ajaxSubmit/jquery.form.js"></script>
    <script src="/components/assets/plugins/jquery-confirmExit/jquery.confirmExit.js"></script>

    <script type="text/javascript" src="https://cdn.datatables.net/1.10.22/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.10.22/js/dataTables.bootstrap4.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/fixedcolumns/3.3.2/js/dataTables.fixedColumns.min.js">
    </script>
    <script type="text/javascript" src="https://cdn.datatables.net/fixedheader/3.1.7/js/dataTables.fixedHeader.min.js">
    </script>
    <script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js">
    </script>
    <script type="text/javascript" src="https://cdn.datatables.net/select/1.3.1/js/dataTables.select.min.js"></script>

    <!-- END VENDOR JS -->
    <!-- BEGIN CORE TEMPLATE JS -->
    <script src="/components/pages/js/pages.min.js"></script>
    <!-- END CORE TEMPLATE JS -->
    <!-- BEGIN PAGE LEVEL JS -->
    <script src="/components/assets/js/gallery.js" type="text/javascript"></script>
    <script src="/components/assets/js/form_elements.js" type="text/javascript"></script>

    <script src="/components/assets/js/scripts.js" type="text/javascript"></script>
    <script src="/components/assets/js/custom.js" type="text/javascript"></script>

    <!-- END PAGE LEVEL JS -->
    <script>
        $.fn.dataTable.ext.order['dom-input'] = function(settings, col) {
            return this.api().column(col, {
                order: 'index'
            }).nodes().map(function(td, i) {
                return $('input', td).val();
            });
        }
        Number.prototype.toCurrencyString = function(prefix, suffix) {
            //    parseFloat(~~this);
            //    console.log(this);
            if (typeof prefix === 'undefined') {
                prefix = '';
            }
            if (typeof suffix === 'undefined') {
                suffix = '';
            }
            var _localeBug = new RegExp((1).toLocaleString().replace(/^1/, '').replace(/\./, '\\.') + "$");

            return prefix + (~~this).toLocaleString().replace(_localeBug, '') + (this % 1).toFixed(3).toLocaleString()
                .replace(/^[+-]?0+/, '') + suffix;
        }

        function parseCurrencyFormate(num) {
            //    console.log("input" + num);
            if (typeof num == "string") {

                if (num.indexOf("<input") >= 0) {
                    num = $(num).val();
                    //            console.log(num);
                } else {
                    num = num.replace(/[^\d\.\-]/g, "");
                }
            }
            num = parseMyNum(num);
            //    console.log("output" + num);
            //

            return parseFloat(num);
        }
        var intVal = function(i) {
            //    console.log(i);
            var to_return = 0;
            if (typeof i === "number") {
                to_return = i;
            } else if (typeof i === 'string') {
                if (i.indexOf("<input") == 0) {
                    //            console.log($(i))
                    i = $(i).val();

                }
                //        console.log(i);
                to_return = i.replace(/[\$,]/g, '') * 1
            }
            return to_return;

        };

        function parseMyNum(num) {

            var numReturn = 0;
            if (num == "") {
                num = 0;
            }

            //    console.log(numReturn);
            return num;
        }
    </script>
    <?php echo $__env->yieldContent('scripts'); ?>
</body>
<!--end::Global App Bundle -->


<!-- end::Body -->

</html>
<?php /**PATH D:\Git Data Capture\application\resources\views/layouts/admin.blade.php ENDPATH**/ ?>