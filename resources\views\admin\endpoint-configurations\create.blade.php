@extends('layouts.admin')
@section('pageTitle', 'Create Endpoint Configuration')

@section('content')
<div class="card card-default">
    <div class="card-header">
        <h4>{{ trans('global.create') }} {{ trans('cruds.endpointConfiguration.title_singular') }}</h4>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ route('admin.endpoint-configurations.store') }}" enctype="multipart/form-data">
            @csrf
            
            <div class="form-group">
                <label class="required" for="name">{{ trans('cruds.endpointConfiguration.fields.name') }}</label>
                <input class="form-control {{ $errors->has('name') ? 'is-invalid' : '' }}" type="text" name="name" id="name" value="{{ old('name', '') }}" required>
                @if($errors->has('name'))
                    <div class="invalid-feedback">
                        {{ $errors->first('name') }}
                    </div>
                @endif
                <span class="help-block">{{ trans('cruds.endpointConfiguration.fields.name_helper') }}</span>
            </div>

            <div class="form-group">
                <label class="required" for="url">{{ trans('cruds.endpointConfiguration.fields.url') }}</label>
                <textarea class="form-control {{ $errors->has('url') ? 'is-invalid' : '' }}" name="url" id="url" rows="3" required>{{ old('url', '') }}</textarea>
                @if($errors->has('url'))
                    <div class="invalid-feedback">
                        {{ $errors->first('url') }}
                    </div>
                @endif
                <span class="help-block">{{ trans('cruds.endpointConfiguration.fields.url_helper') }}</span>
            </div>

            <div class="form-group">
                <label for="body_data_field">{{ trans('cruds.endpointConfiguration.fields.body_data_field') }}</label>
                <textarea class="form-control {{ $errors->has('body_data_field') ? 'is-invalid' : '' }}" name="body_data_field" id="body_data_field" rows="5">{{ old('body_data_field', '') }}</textarea>
                @if($errors->has('body_data_field'))
                    <div class="invalid-feedback">
                        {{ $errors->first('body_data_field') }}
                    </div>
                @endif
                <span class="help-block">{{ trans('cruds.endpointConfiguration.fields.body_data_field_helper') }}</span>
            </div>

            <div class="form-group">
                <label class="required" for="erp_selection">{{ trans('cruds.endpointConfiguration.fields.erp_selection') }}</label>
                <select class="form-control select2 {{ $errors->has('erp_selection') ? 'is-invalid' : '' }}" name="erp_selection" id="erp_selection" required>
                    @foreach($erpOptions as $key => $label)
                        <option value="{{ $key }}" {{ old('erp_selection', 'CSI') == $key ? 'selected' : '' }}>{{ $label }}</option>
                    @endforeach
                </select>
                @if($errors->has('erp_selection'))
                    <div class="invalid-feedback">
                        {{ $errors->first('erp_selection') }}
                    </div>
                @endif
                <span class="help-block">{{ trans('cruds.endpointConfiguration.fields.erp_selection_helper') }}</span>
            </div>

            <div class="form-group">
                <label class="required" for="process_selection">{{ trans('cruds.endpointConfiguration.fields.process_selection') }}</label>
                <select class="form-control select2 {{ $errors->has('process_selection') ? 'is-invalid' : '' }}" name="process_selection" id="process_selection" required>
                    <option value="">{{ trans('global.pleaseSelect') }}</option>
                    @foreach($processOptions as $key => $label)
                        <option value="{{ $key }}" {{ old('process_selection', '') == $key ? 'selected' : '' }}>{{ $label }}</option>
                    @endforeach
                </select>
                @if($errors->has('process_selection'))
                    <div class="invalid-feedback">
                        {{ $errors->first('process_selection') }}
                    </div>
                @endif
                <span class="help-block">{{ trans('cruds.endpointConfiguration.fields.process_selection_helper') }}</span>
            </div>

            <div class="form-group">
                <label class="required" for="endpoint_type">{{ trans('cruds.endpointConfiguration.fields.endpoint_type') }}</label>
                <select class="form-control select2 {{ $errors->has('endpoint_type') ? 'is-invalid' : '' }}" name="endpoint_type" id="endpoint_type" required>
                    @foreach($endpointTypeOptions as $key => $label)
                        <option value="{{ $key }}" {{ old('endpoint_type', 'API') == $key ? 'selected' : '' }}>{{ $label }}</option>
                    @endforeach
                </select>
                @if($errors->has('endpoint_type'))
                    <div class="invalid-feedback">
                        {{ $errors->first('endpoint_type') }}
                    </div>
                @endif
                <span class="help-block">{{ trans('cruds.endpointConfiguration.fields.endpoint_type_helper') }}</span>
            </div>

            <div class="form-group">
                <div class="form-check {{ $errors->has('is_active') ? 'is-invalid' : '' }}">
                    <input type="hidden" name="is_active" value="0">
                    <input class="form-check-input" type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', 1) == 1 ? 'checked' : '' }}>
                    <label class="form-check-label" for="is_active">
                        {{ trans('cruds.endpointConfiguration.fields.is_active') }}
                    </label>
                </div>
                @if($errors->has('is_active'))
                    <div class="invalid-feedback">
                        {{ $errors->first('is_active') }}
                    </div>
                @endif
                <span class="help-block">{{ trans('cruds.endpointConfiguration.fields.is_active_helper') }}</span>
            </div>

            <div class="form-group">
                <button class="btn btn-danger" type="submit">
                    {{ trans('global.save') }}
                </button>
                <a class="btn btn-secondary" href="{{ route('admin.endpoint-configurations.index') }}">
                    {{ trans('global.cancel') }}
                </a>
            </div>
        </form>
    </div>
</div>
@endsection
