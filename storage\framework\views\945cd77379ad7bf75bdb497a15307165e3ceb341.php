<?php $__env->startSection('pageTitle', 'View Endpoint Configuration'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .table th {
        width: 25%;
        background-color: #f8f9fa;
    }
    pre {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        max-height: 200px;
        overflow-y: auto;
    }
    .badge {
        font-size: 0.875rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card card-default">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4><i class="fa fa-eye"></i> <?php echo e(trans('global.show')); ?> <?php echo e(trans('cruds.endpointConfiguration.title_singular')); ?></h4>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group" role="group">
                                <a class="btn btn-info" href="<?php echo e(route('admin.endpoint-configurations.edit', $endpointConfiguration->id)); ?>">
                                    <i class="fa fa-edit"></i> <?php echo e(trans('global.edit')); ?>

                                </a>
                                <a class="btn btn-secondary" href="<?php echo e(route('admin.endpoint-configurations.index')); ?>">
                                    <i class="fa fa-arrow-left"></i> <?php echo e(trans('global.back_to_list')); ?>

                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
            <table class="table table-bordered table-striped">
                <tbody>
                    <tr>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.id')); ?>

                        </th>
                        <td>
                            <?php echo e($endpointConfiguration->id); ?>

                        </td>
                    </tr>
                    <tr>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.name')); ?>

                        </th>
                        <td>
                            <?php echo e($endpointConfiguration->name); ?>

                        </td>
                    </tr>
                    <tr>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.url')); ?>

                        </th>
                        <td>
                            <?php echo e($endpointConfiguration->url); ?>

                        </td>
                    </tr>
                    <tr>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.body_data_field')); ?>

                        </th>
                        <td>
                            <?php if($endpointConfiguration->body_data_field): ?>
                                <?php
                                    $fields = is_string($endpointConfiguration->body_data_field)
                                        ? json_decode($endpointConfiguration->body_data_field, true)
                                        : $endpointConfiguration->body_data_field;
                                ?>
                                <?php if(is_array($fields) && count($fields) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead class="thead-light">
                                                <tr>
                                                    <th>Field Name</th>
                                                    <th>Data Type</th>
                                                    <th>Max Length</th>
                                                    <th>Required</th>
                                                    <th>Default</th>
                                                    <th>Description</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $fields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><strong><?php echo e($field['name'] ?? 'N/A'); ?></strong></td>
                                                        <td><span class="badge badge-info"><?php echo e($field['datatype'] ?? 'string'); ?></span></td>
                                                        <td><?php echo e($field['maxlength'] ?? 'N/A'); ?></td>
                                                        <td>
                                                            <span class="badge badge-<?php echo e(($field['required'] ?? false) ? 'danger' : 'secondary'); ?>">
                                                                <?php echo e(($field['required'] ?? false) ? 'Yes' : 'No'); ?>

                                                            </span>
                                                        </td>
                                                        <td><?php echo e($field['default'] ?? 'N/A'); ?></td>
                                                        <td><?php echo e($field['description'] ?? 'N/A'); ?></td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">No field mapping configured</span>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="text-muted">No field mapping configured</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.erp_selection')); ?>

                        </th>
                        <td>
                            <?php echo e($endpointConfiguration->erp_selection); ?>

                        </td>
                    </tr>
                    <tr>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.process_selection')); ?>

                        </th>
                        <td>
                            <?php echo e($endpointConfiguration->process_selection); ?>

                        </td>
                    </tr>
                    <tr>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.endpoint_type')); ?>

                        </th>
                        <td>
                            <?php echo e($endpointConfiguration->endpoint_type); ?>

                        </td>
                    </tr>
                    <tr>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.is_active')); ?>

                        </th>
                        <td>
                            <span class="badge badge-<?php echo e($endpointConfiguration->is_active ? 'success' : 'danger'); ?>">
                                <?php echo e($endpointConfiguration->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.created_at')); ?>

                        </th>
                        <td>
                            <?php echo e($endpointConfiguration->created_at); ?>

                        </td>
                    </tr>
                    <tr>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.updated_at')); ?>

                        </th>
                        <td>
                            <?php echo e($endpointConfiguration->updated_at); ?>

                        </td>
                    </tr>
                </tbody>
            </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Git Data Capture\application\resources\views/admin/endpoint-configurations/show.blade.php ENDPATH**/ ?>