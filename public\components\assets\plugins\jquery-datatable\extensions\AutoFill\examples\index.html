<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
    <link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
    <link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
    <script type="text/javascript" language="javascript" src="../../../media/js/jquery.js"></script>
    <script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
    <script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>
    <title>AutoFill examples - AutoFill examples</title>
  </head>
  <body class="dt-example">
    <div class="container">
      <section>
        <h1>AutoFill example <span>AutoFill examples</span></h1>
        <div class="info">
          <p>AutoFill gives an Excel like option to a DataTable to click and drag over multiple cells, filling in information over the selected cells and incrementing numbers as needed.</p>
          <p>Thanks to <a href="http://www.phoniax.no/">Phoniax AS</a> for their sponsorship of this plug-in for DataTables.
          </p>
        </div>
      </section>
    </div>
    <section>
      <div class="footer">
        <div class="gradient"></div>
        <div class="liner">
          <div class="toc">
            <div class="toc-group">
              <h3><a href="./index.html">Examples</a></h3>
              <ul class="toc">
                <li><a href="./simple.html">Basic initialisation</a></li>
                <li><a href="./columns.html">Column options</a></li>
                <li><a href="./scrolling.html">Scrolling DataTable</a></li>
                <li><a href="./fill-both.html">Horizontal and vertical fill</a></li>
                <li><a href="./fill-horizontal.html">Horizontal fill</a></li>
                <li><a href="./complete-callback.html">Complete callback</a></li>
                <li><a href="./step-callback.html">Step callback</a></li>
              </ul>
            </div>
          </div>
          <div class="epilogue">
            <p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.
              <br> Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and
              <a href="http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of DataTables.
            </p>
            <p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2014
              <br> DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
          </div>
        </div>
      </div>
    </section>
  </body>
</html>