<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    const TYPE_USER = 3;
    const TYPE_ADMIN = 2;
    const TYPE_SUPER_ADMIN = 1;
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'user_group_id',
        'type',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];


    public function isAdmin()
    {
        return $this->type === self::TYPE_ADMIN || $this->type === self::TYPE_SUPER_ADMIN;
    }

    public function isSuperAdmin()
    {
        return $this->type === self::TYPE_SUPER_ADMIN;
    }

    public function isUser()
    {
        return $this->type === self::TYPE_USER;
    }

    public function userGroup()
    {
        return $this->belongsTo(UserGroup::class);
    }
}
