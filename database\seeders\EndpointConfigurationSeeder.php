<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\EndpointConfiguration;
use App\Models\User;

class EndpointConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get the first super admin user, or create one if none exists
        $superAdmin = User::where('type', 1)->first();

        if (!$superAdmin) {
            $superAdmin = User::create([
                'name' => 'System Administrator',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'type' => 1,
                'email_verified_at' => now(),
            ]);
        }

        // CSI Endpoint Configurations
        $csiConfigurations = [
            [
                'name' => 'CSI Misc Issue API',
                'url' => 'IDORequestService/ido/invoke/SLItemLocs?method=IaPostSp',
                'body_data_field' => [
                    [
                        'name' => 'TransQty',
                        'datatype' => 'integer',
                        'maxlength' => 19,
                        'required' => true,
                        'default' => null,
                        'description' => 'Quantity to issue'
                    ],
                    [
                        'name' => 'Whse',
                        'datatype' => 'string',
                        'maxlength' => 4,
                        'required' => true,
                        'default' => null,
                        'description' => 'Warehouse code'
                    ],
                    [
                        'name' => 'Item',
                        'datatype' => 'string',
                        'maxlength' => 30,
                        'required' => true,
                        'default' => null,
                        'description' => 'Item'
                    ],
                    [
                        'name' => 'Loc',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => true,
                        'default' => null,
                        'description' => 'Location'
                    ],
                    [
                        'name' => 'Lot',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => false,
                        'default' => null,
                        'description' => 'Lot number'
                    ],
                    [
                        'name' => 'ReasonCode',
                        'datatype' => 'string',
                        'maxlength' => 3,
                        'required' => false,
                        'default' => null,
                        'description' => 'Reason code'
                    ]
                ],
                'erp_selection' => 'CSI',
                'process_selection' => 'Misc Issue',
                'endpoint_type' => 'API',
                'is_active' => true,
            ],
            [
                'name' => 'CSI Misc Receipt API',
                'url' => 'IDORequestService/ido/invoke/SLStockActItems?method=ItemMiscReceiptSp',
                'body_data_field' => [
                    [
                        'name' => 'Item',
                        'datatype' => 'string',
                        'maxlength' => 30,
                        'required' => true,
                        'default' => null,
                        'description' => 'Item'
                    ],
                    [
                        'name' => 'Whse',
                        'datatype' => 'string',
                        'maxlength' => 4,
                        'required' => true,
                        'default' => null,
                        'description' => 'Warehouse code'
                    ],
                    [
                        'name' => 'Qty',
                        'datatype' => 'integer',
                        'maxlength' => 20,
                        'required' => true,
                        'default' => 1,
                        'description' => 'Quantity to receive'
                    ],
                    [
                        'name' => 'UM',
                        'datatype' => 'string',
                        'maxlength' => 3,
                        'required' => true,
                        'default' => null,
                        'description' => 'Unit of measure'
                    ],
                    [
                        'name' => 'Loc',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => true,
                        'default' => null,
                        'description' => 'Location'
                    ],
                    [
                        'name' => 'Lot',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => false,
                        'default' => null,
                        'description' => 'Lot number'
                    ],
                    [
                        'name' => 'Reason',
                        'datatype' => 'string',
                        'maxlength' => 3,
                        'required' => false,
                        'default' => null,
                        'description' => 'Reason code'
                    ]
                ],
                'erp_selection' => 'CSI',
                'process_selection' => 'Misc Receipt',
                'endpoint_type' => 'API',
                'is_active' => true,
            ],
            [
                'name' => 'CSI Quantity Move API',
                'url' => 'IDORequestService/ido/invoke/SLItemLocs?method=MvPostSp',
                'body_data_field' => [
                    [
                        'name' => 'Qty',
                        'datatype' => 'integer',
                        'maxlength' => 19,
                        'required' => true,
                        'default' => null,
                        'description' => 'Quantity to move'
                    ],
                    [
                        'name' => 'Item',
                        'datatype' => 'string',
                        'maxlength' => 30,
                        'required' => true,
                        'default' => null,
                        'description' => 'Item'
                    ],
                    [
                        'name' => 'FromWhse',
                        'datatype' => 'string',
                        'maxlength' => 4,
                        'required' => true,
                        'default' => null,
                        'description' => 'Warehouse code'
                    ],
                    [
                        'name' => 'FromLoc',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => true,
                        'default' => null,
                        'description' => 'Location'
                    ],
                    [
                        'name' => 'FromLot',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => false,
                        'default' => null,
                        'description' => 'Lot number'
                    ],
                    [
                        'name' => 'ToWhse',
                        'datatype' => 'string',
                        'maxlength' => 4,
                        'required' => true,
                        'default' => null,
                        'description' => 'Warehouse code'
                    ],
                    [
                        'name' => 'ToLoc',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => true,
                        'default' => null,
                        'description' => 'Location'
                    ],
                    [
                        'name' => 'ToLot',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => false,
                        'default' => null,
                        'description' => 'Lot number'
                    ],
                ],
                'erp_selection' => 'CSI',
                'process_selection' => 'Quantity Move',
                'endpoint_type' => 'API',
                'is_active' => true,
            ]
        ];

        // Combine all configurations
        $allConfigurations = array_merge($csiConfigurations);

        // Create endpoint configurations
        foreach ($allConfigurations as $config) {
            // Check if configuration already exists
            $existingConfig = EndpointConfiguration::where('name', $config['name'])->first();

            if (!$existingConfig) {
                EndpointConfiguration::create([
                    'name' => $config['name'],
                    'url' => $config['url'],
                    'body_data_field' => $config['body_data_field'],
                    'erp_selection' => $config['erp_selection'],
                    'process_selection' => $config['process_selection'],
                    'endpoint_type' => $config['endpoint_type'],
                    'is_active' => $config['is_active'],
                    'created_by' => $superAdmin->id,
                    'updated_by' => $superAdmin->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                $this->command->info("Created endpoint configuration: {$config['name']}");
            } else {
                $this->command->info("Endpoint configuration already exists: {$config['name']}");
            }
        }

        $this->command->info('Endpoint Configuration seeding completed!');
    }
}
