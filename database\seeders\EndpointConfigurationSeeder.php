<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\EndpointConfiguration;
use App\Models\User;

class EndpointConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get the first super admin user, or create one if none exists
        $superAdmin = User::where('type', 1)->first();

        if (!$superAdmin) {
            $superAdmin = User::create([
                'name' => 'System Administrator',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'type' => 1,
                'email_verified_at' => now(),
            ]);
        }

        // CSI Endpoint Configurations
        $csiConfigurations = [
            [
                'name' => 'CSI Misc Issue API',
                'url' => 'IDORequestService/ido/invoke/SLStockActItems?method=ItemMiscIssueSp',
                'body_data_field' => [
                    [
                        'name' => 'item_code',
                        'datatype' => 'string',
                        'maxlength' => 50,
                        'required' => true,
                        'default' => null,
                        'description' => 'Item code for the inventory item to issue'
                    ],
                    [
                        'name' => 'quantity',
                        'datatype' => 'integer',
                        'maxlength' => null,
                        'required' => true,
                        'default' => 1,
                        'description' => 'Quantity to issue from inventory'
                    ],
                    [
                        'name' => 'location',
                        'datatype' => 'string',
                        'maxlength' => 20,
                        'required' => true,
                        'default' => null,
                        'description' => 'Warehouse location code'
                    ],
                    [
                        'name' => 'reason_code',
                        'datatype' => 'string',
                        'maxlength' => 10,
                        'required' => false,
                        'default' => 'MISC',
                        'description' => 'Reason code for the miscellaneous issue'
                    ]
                ],
                'erp_selection' => 'CSI',
                'process_selection' => 'Misc Issue',
                'endpoint_type' => 'API',
                'is_active' => true,
            ],
            [
                'name' => 'CSI Misc Receipt API',
                'url' => 'IDORequestService/ido/invoke/SLStockActItems?method=ItemMiscReceiptSp',
                'body_data_field' => [
                    [
                        'name' => 'item_code',
                        'datatype' => 'string',
                        'maxlength' => 50,
                        'required' => true,
                        'default' => null,
                        'description' => 'Item code for the inventory item to receive'
                    ],
                    [
                        'name' => 'quantity',
                        'datatype' => 'integer',
                        'maxlength' => null,
                        'required' => true,
                        'default' => 1,
                        'description' => 'Quantity to receive into inventory'
                    ],
                    [
                        'name' => 'location',
                        'datatype' => 'string',
                        'maxlength' => 20,
                        'required' => true,
                        'default' => null,
                        'description' => 'Warehouse location code'
                    ],
                    [
                        'name' => 'unit_cost',
                        'datatype' => 'float',
                        'maxlength' => null,
                        'required' => false,
                        'default' => null,
                        'description' => 'Unit cost for the received item'
                    ]
                ],
                'erp_selection' => 'CSI',
                'process_selection' => 'Misc Receipt',
                'endpoint_type' => 'API',
                'is_active' => true,
            ],
            [
                'name' => 'CSI Quantity Move Stored Procedure',
                'url' => 'StoredProcedures/sp_inventory_move',
                'body_data_field' => [
                    [
                        'name' => 'item_code',
                        'datatype' => 'string',
                        'maxlength' => 50,
                        'required' => true,
                        'default' => null,
                        'description' => 'Item code for the inventory item to move'
                    ],
                    [
                        'name' => 'from_location',
                        'datatype' => 'string',
                        'maxlength' => 20,
                        'required' => true,
                        'default' => null,
                        'description' => 'Source warehouse location'
                    ],
                    [
                        'name' => 'to_location',
                        'datatype' => 'string',
                        'maxlength' => 20,
                        'required' => true,
                        'default' => null,
                        'description' => 'Destination warehouse location'
                    ],
                    [
                        'name' => 'quantity',
                        'datatype' => 'integer',
                        'maxlength' => null,
                        'required' => true,
                        'default' => null,
                        'description' => 'Quantity to move between locations'
                    ]
                ],
                'erp_selection' => 'CSI',
                'process_selection' => 'Quantity Move',
                'endpoint_type' => 'Stored Procedure',
                'is_active' => true,
            ]
        ];

        // Combine all configurations
        $allConfigurations = array_merge($csiConfigurations);

        // Create endpoint configurations
        foreach ($allConfigurations as $config) {
            // Check if configuration already exists
            $existingConfig = EndpointConfiguration::where('name', $config['name'])->first();

            if (!$existingConfig) {
                EndpointConfiguration::create([
                    'name' => $config['name'],
                    'url' => $config['url'],
                    'body_data_field' => $config['body_data_field'],
                    'erp_selection' => $config['erp_selection'],
                    'process_selection' => $config['process_selection'],
                    'endpoint_type' => $config['endpoint_type'],
                    'is_active' => $config['is_active'],
                    'created_by' => $superAdmin->id,
                    'updated_by' => $superAdmin->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                $this->command->info("Created endpoint configuration: {$config['name']}");
            } else {
                $this->command->info("Endpoint configuration already exists: {$config['name']}");
            }
        }

        $this->command->info('Endpoint Configuration seeding completed!');
    }
}
