/*! jQuery Dynatree Plugin - v1.2.6 - 2014-05-11
* http://dynatree.googlecode.com/
* Copyright (c) 2014 <PERSON>; Licensed MIT, GPL */function _log(e){if(_canLog){var t=Array.prototype.slice.apply(arguments,[1]),i=new Date,s=i.getHours()+":"+i.getMinutes()+":"+i.getSeconds()+"."+i.getMilliseconds();t[0]=s+" - "+t[0];try{switch(e){case"info":window.console.info.apply(window.console,t);break;case"warn":window.console.warn.apply(window.console,t);break;default:window.console.log.apply(window.console,t)}}catch(n){window.console?-**********===n.number&&window.console.log(t.join(", ")):_canLog=!1}}}function logMsg(){Array.prototype.unshift.apply(arguments,["debug"]),_log.apply(this,arguments)}var _canLog=!0,getDynaTreePersistData=null,DTNodeStatus_Error=-1,DTNodeStatus_Loading=1,DTNodeStatus_Ok=0;(function($){function getDtNodeFromElement(e){return alert("getDtNodeFromElement is deprecated"),$.ui.dynatree.getNode(e)}function noop(){}function offsetString(e){return 0===e?"":e>0?"+"+e:""+e}function _checkBrowser(){function e(e){e=e.toLowerCase();var t=/(chrome)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||0>e.indexOf("compatible")&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(e)||[];return{browser:t[1]||"",version:t[2]||"0"}}var t,i;return t=e(navigator.userAgent),i={},t.browser&&(i[t.browser]=!0,i.version=t.version),i.chrome?i.webkit=!0:i.webkit&&(i.safari=!0),i}function versionCompare(e,t){var i,s,n,a=(""+e).split("."),r=(""+t).split("."),o=Math.min(a.length,r.length);for(n=0;o>n;n++)if(i=parseInt(a[n],10),s=parseInt(r[n],10),isNaN(i)&&(i=a[n]),isNaN(s)&&(s=r[n]),i!=s)return i>s?1:s>i?-1:0/0;return a.length===r.length?0:a.length<r.length?-1:1}function _initDragAndDrop(e){var t=e.options.dnd||null;t&&(t.onDragStart||t.onDrop)&&_registerDnd(),t&&t.onDragStart&&e.$tree.draggable({addClasses:!1,appendTo:"body",containment:!1,delay:0,distance:4,revert:t.revert!==!0?!1:function(e){if(logMsg("draggable.revert(), dropped=",e),"boolean"==typeof e)return!e;var t=$.ui.ddmanager&&$.ui.ddmanager.current&&$.ui.ddmanager.current.helper,i=t&&t.hasClass("dynatree-drop-reject");return i},scroll:!0,scrollSpeed:7,scrollSensitivity:10,connectToDynatree:!0,helper:function(e){var t=$.ui.dynatree.getNode(e.target);return t?t.tree._onDragEvent("helper",t,null,e,null,null):"<div></div>"},start:function(e,t){var i=t.helper.data("dtSourceNode");return!!i}}),t&&t.onDrop&&e.$tree.droppable({addClasses:!1,tolerance:"pointer",greedy:!1})}var Class={create:function(){return function(){this.initialize.apply(this,arguments)}}},BROWSER=_checkBrowser(),jquerySupports={positionMyOfs:versionCompare($.ui.version,"1.9")>=0},DynaTreeNode=Class.create();DynaTreeNode.prototype={initialize:function(e,t,i){this.parent=e,this.tree=t,"string"==typeof i&&(i={title:i}),i.key=null==i.key?"_"+t._nodeCount++:""+i.key,this.data=$.extend({},$.ui.dynatree.nodedatadefaults,i),this.li=null,this.span=null,this.ul=null,this.childList=null,this._isLoading=!1,this.hasSubSel=!1,this.bExpanded=!1,this.bSelected=!1},toString:function(){return"DynaTreeNode<"+this.data.key+">: '"+this.data.title+"'"},toDict:function(e,t){var i,s=$.extend({},this.data);if(s.activate=this.tree.activeNode===this,s.focus=this.tree.focusNode===this,s.expand=this.bExpanded,s.select=this.bSelected,t&&t(s),e&&this.childList){s.children=[];for(var n=0,a=this.childList.length;a>n;n++)i=this.childList[n],i.isStatusNode()||s.children.push(i.toDict(!0,t))}else delete s.children;return s},fromDict:function(e){var t=e.children;return void 0===t?(this.data=$.extend(this.data,e),this.render(),void 0):(e=$.extend({},e),e.children=void 0,this.data=$.extend(this.data,e),this.removeChildren(),this.addChild(t),void 0)},_getInnerHtml:function(){var e,t=this.tree,i=t.options,s=t.cache,n=this.getLevel(),a=this.data,r="";i.minExpandLevel>n?n>1&&(r+=s.tagConnector):r+=this.hasChildren()!==!1?s.tagExpander:s.tagConnector,i.checkbox&&a.hideCheckbox!==!0&&!a.isStatusNode&&(r+=s.tagCheckbox),a.icon?(e="/"===a.icon.charAt(0)?a.icon:i.imagePath+a.icon,r+="<img src='"+e+"' alt='' />"):a.icon===!1||(r+=a.iconClass?"<span class=' "+a.iconClass+"'></span>":s.tagNodeIcon);var o="";if(i.onCustomRender&&(o=i.onCustomRender.call(t,this)||""),!o){var d=a.tooltip?' title="'+a.tooltip.replace(/\"/g,"&quot;")+'"':"",l=a.href||"#";o=i.noLink||a.noLink?'<span style="display:inline-block;" class="'+i.classNames.title+'"'+d+">"+a.title+"</span>":'<a href="'+l+'" class="'+i.classNames.title+'"'+d+">"+a.title+"</a>"}return r+=o},_fixOrder:function(){var e=this.childList;if(e&&this.ul)for(var t=this.ul.firstChild,i=0,s=e.length-1;s>i;i++){var n=e[i],a=t.dtnode;n!==a?(this.tree.logDebug("_fixOrder: mismatch at index "+i+": "+n+" != "+a),this.ul.insertBefore(n.li,a.li)):t=t.nextSibling}},render:function(e,t){var i=this.tree,s=this.parent,n=this.data,a=i.options,r=a.classNames,o=this.isLastSibling(),d=!1;if(s||this.ul){if(s){this.li||(d=!0,this.li=document.createElement("li"),this.li.dtnode=this,n.key&&a.generateIds&&(this.li.id=a.idPrefix+n.key),this.span=document.createElement("span"),this.span.className=r.title,this.li.appendChild(this.span),s.ul||(s.ul=document.createElement("ul"),s.ul.style.display="none",s.li.appendChild(s.ul)),s.ul.appendChild(this.li)),this.span.innerHTML=this._getInnerHtml();var l=[];l.push(r.node),n.isFolder&&l.push(r.folder),this.bExpanded&&l.push(r.expanded),this.hasChildren()!==!1&&l.push(r.hasChildren),n.isLazy&&null===this.childList&&l.push(r.lazy),o&&l.push(r.lastsib),this.bSelected&&l.push(r.selected),this.hasSubSel&&l.push(r.partsel),i.activeNode===this&&l.push(r.active),n.addClass&&l.push(n.addClass),l.push(r.combinedExpanderPrefix+(this.bExpanded?"e":"c")+(n.isLazy&&null===this.childList?"d":"")+(o?"l":"")),l.push(r.combinedIconPrefix+(this.bExpanded?"e":"c")+(n.isFolder?"f":"")),this.span.className=l.join(" "),this.li.className=o?r.lastsib:"",d&&a.onCreate&&a.onCreate.call(i,this,this.span),a.onRender&&a.onRender.call(i,this,this.span)}}else this.li=this.span=null,this.ul=document.createElement("ul"),this.ul.className=a.minExpandLevel>1?r.container+" "+r.noConnector:r.container;if((this.bExpanded||t===!0)&&this.childList){for(var h=0,c=this.childList.length;c>h;h++)this.childList[h].render(!1,t);this._fixOrder()}if(this.ul){var u="none"===this.ul.style.display,p=!!this.bExpanded;if(e&&a.fx&&u===p){var f=a.fx.duration||200;$(this.ul).animate(a.fx,f)}else this.ul.style.display=this.bExpanded||!s?"":"none"}},getKeyPath:function(e){var t=[],i=this.tree.options.keyPathSeparator;return this.visitParents(function(e){e.parent&&t.unshift(e.data.key)},!e),i+t.join(i)},getParent:function(){return this.parent},getChildren:function(){return void 0===this.hasChildren()?void 0:this.childList},hasChildren:function(){return this.data.isLazy?null===this.childList||void 0===this.childList?void 0:0===this.childList.length?!1:1===this.childList.length&&this.childList[0].isStatusNode()?void 0:!0:!!this.childList},isFirstSibling:function(){var e=this.parent;return!e||e.childList[0]===this},isLastSibling:function(){var e=this.parent;return!e||e.childList[e.childList.length-1]===this},isLoading:function(){return!!this._isLoading},getPrevSibling:function(){if(!this.parent)return null;for(var e=this.parent.childList,t=1,i=e.length;i>t;t++)if(e[t]===this)return e[t-1];return null},getNextSibling:function(){if(!this.parent)return null;for(var e=this.parent.childList,t=0,i=e.length-1;i>t;t++)if(e[t]===this)return e[t+1];return null},isStatusNode:function(){return this.data.isStatusNode===!0},isChildOf:function(e){return this.parent&&this.parent===e},isDescendantOf:function(e){if(!e)return!1;for(var t=this.parent;t;){if(t===e)return!0;t=t.parent}return!1},countChildren:function(){var e=this.childList;if(!e)return 0;for(var t=e.length,i=0,s=t;s>i;i++){var n=e[i];t+=n.countChildren()}return t},sortChildren:function(e,t){var i=this.childList;if(i){if(e=e||function(e,t){var i=e.data.title.toLowerCase(),s=t.data.title.toLowerCase();return i===s?0:i>s?1:-1},i.sort(e),t)for(var s=0,n=i.length;n>s;s++)i[s].childList&&i[s].sortChildren(e,"$norender$");"$norender$"!==t&&this.render()}},_setStatusNode:function(e){var t=this.childList?this.childList[0]:null;if(e)t?(e.isStatusNode=!0,e.key="_statusNode",t.data=e,t.render()):(e.isStatusNode=!0,e.key="_statusNode",t=this.addChild(e));else if(t&&t.isStatusNode()){try{this.ul&&(this.ul.removeChild(t.li),t.li=null)}catch(i){}1===this.childList.length?this.childList=[]:this.childList.shift()}},setLazyNodeStatus:function(e,t){var i=t&&t.tooltip?t.tooltip:null,s=t&&t.info?" ("+t.info+")":"";switch(e){case DTNodeStatus_Ok:this._setStatusNode(null),$(this.span).removeClass(this.tree.options.classNames.nodeLoading),this._isLoading=!1,this.tree.options.autoFocus&&(this===this.tree.tnRoot&&this.childList&&this.childList.length>0?this.childList[0].focus():this.focus());break;case DTNodeStatus_Loading:this._isLoading=!0,$(this.span).addClass(this.tree.options.classNames.nodeLoading),this.parent||this._setStatusNode({title:this.tree.options.strings.loading+s,tooltip:i,addClass:this.tree.options.classNames.nodeWait});break;case DTNodeStatus_Error:this._isLoading=!1,this._setStatusNode({title:this.tree.options.strings.loadError+s,tooltip:i,addClass:this.tree.options.classNames.nodeError});break;default:throw"Bad LazyNodeStatus: '"+e+"'."}},_parentList:function(e,t){for(var i=[],s=t?this:this.parent;s;)(e||s.parent)&&i.unshift(s),s=s.parent;return i},getLevel:function(){for(var e=0,t=this.parent;t;)e++,t=t.parent;return e},_getTypeForOuterNodeEvent:function(e){var t=this.tree.options.classNames,i=e.target;if(0>i.className.indexOf(t.node))return null;for(var s=e.pageX-i.offsetLeft,n=e.pageY-i.offsetTop,a=0,r=i.childNodes.length;r>a;a++){var o=i.childNodes[a],d=o.offsetLeft-i.offsetLeft,l=o.offsetTop-i.offsetTop,h=o.clientWidth,c=o.clientHeight;if(s>=d&&d+h>=s&&n>=l&&l+c>=n){if(o.className==t.title)return"title";if(o.className==t.expander)return"expander";if(o.className==t.checkbox||o.className==t.radio)return"checkbox";if(o.className==t.nodeIcon)return"icon"}}return"prefix"},getEventTargetType:function(e){var t=e&&e.target?e.target.className:"",i=this.tree.options.classNames;return t.indexOf(i.title)>=0?"title":t.indexOf(i.expander)>=0?"expander":t.indexOf(i.checkbox)>=0||t.indexOf(i.radio)>=0?"checkbox":t.indexOf(i.nodeIcon)>=0?"icon":t.indexOf(i.empty)>=0||t.indexOf(i.vline)>=0||t.indexOf(i.connector)>=0?"prefix":t.indexOf(i.node)>=0?this._getTypeForOuterNodeEvent(e):null},isVisible:function(){for(var e=this._parentList(!0,!1),t=0,i=e.length;i>t;t++)if(!e[t].bExpanded)return!1;return!0},makeVisible:function(){for(var e=this._parentList(!0,!1),t=0,i=e.length;i>t;t++)e[t]._expand(!0)},focus:function(){this.makeVisible();try{$(this.span).find(">a").focus()}catch(e){}},isFocused:function(){return this.tree.tnFocused===this},_activate:function(e,t){this.tree.logDebug("dtnode._activate(%o, fireEvents=%o) - %o",e,t,this);var i=this.tree.options;if(!this.data.isStatusNode)if(e){if(t&&i.onQueryActivate&&i.onQueryActivate.call(this.tree,e,this)===!1)return;if(this.tree.activeNode){if(this.tree.activeNode===this)return;this.tree.activeNode.deactivate()}i.activeVisible&&this.makeVisible(),this.tree.activeNode=this,i.persist&&$.cookie(i.cookieId+"-active",this.data.key,i.cookie),this.tree.persistence.activeKey=this.data.key,$(this.span).addClass(i.classNames.active),t&&i.onActivate&&i.onActivate.call(this.tree,this)}else if(this.tree.activeNode===this){if(i.onQueryActivate&&i.onQueryActivate.call(this.tree,!1,this)===!1)return;$(this.span).removeClass(i.classNames.active),i.persist&&$.cookie(i.cookieId+"-active","",i.cookie),this.tree.persistence.activeKey=null,this.tree.activeNode=null,t&&i.onDeactivate&&i.onDeactivate.call(this.tree,this)}},activate:function(){this._activate(!0,!0)},activateSilently:function(){this._activate(!0,!1)},deactivate:function(){this._activate(!1,!0)},isActive:function(){return this.tree.activeNode===this},_userActivate:function(){var e=!0,t=!1;if(this.data.isFolder)switch(this.tree.options.clickFolderMode){case 2:e=!1,t=!0;break;case 3:e=t=!0}null===this.parent&&(t=!1),t&&(this.toggleExpand(),this.focus()),e&&this.activate()},_setSubSel:function(e){e?(this.hasSubSel=!0,$(this.span).addClass(this.tree.options.classNames.partsel)):(this.hasSubSel=!1,$(this.span).removeClass(this.tree.options.classNames.partsel))},_updatePartSelectionState:function(){var e;if(!this.hasChildren())return e=this.bSelected&&!this.data.unselectable&&!this.data.isStatusNode,this._setSubSel(!1),e;var t,i,s=this.childList,n=!0,a=!0;for(t=0,i=s.length;i>t;t++){var r=s[t],o=r._updatePartSelectionState();o!==!1&&(a=!1),o!==!0&&(n=!1)}return e=n?!0:a?!1:void 0,this._setSubSel(void 0===e),this.bSelected=e===!0,e},_fixSelectionState:function(){var e,t,i;if(this.bSelected)for(this.visit(function(e){e.parent._setSubSel(!0),e.data.unselectable||e._select(!0,!1,!1)}),e=this.parent;e;){e._setSubSel(!0);var s=!0;for(t=0,i=e.childList.length;i>t;t++){var n=e.childList[t];if(!n.bSelected&&!n.data.isStatusNode&&!n.data.unselectable){s=!1;break}}s&&e._select(!0,!1,!1),e=e.parent}else for(this._setSubSel(!1),this.visit(function(e){e._setSubSel(!1),e._select(!1,!1,!1)}),e=this.parent;e;){e._select(!1,!1,!1);var a=!1;for(t=0,i=e.childList.length;i>t;t++)if(e.childList[t].bSelected||e.childList[t].hasSubSel){a=!0;break}e._setSubSel(a),e=e.parent}},_select:function(e,t,i){var s=this.tree.options;this.data.isStatusNode||this.bSelected!==e&&(t&&s.onQuerySelect&&s.onQuerySelect.call(this.tree,e,this)===!1||(1==s.selectMode&&e&&this.tree.visit(function(e){return e.bSelected?(e._select(!1,!1,!1),!1):void 0}),this.bSelected=e,e?(s.persist&&this.tree.persistence.addSelect(this.data.key),$(this.span).addClass(s.classNames.selected),i&&3===s.selectMode&&this._fixSelectionState(),t&&s.onSelect&&s.onSelect.call(this.tree,!0,this)):(s.persist&&this.tree.persistence.clearSelect(this.data.key),$(this.span).removeClass(s.classNames.selected),i&&3===s.selectMode&&this._fixSelectionState(),t&&s.onSelect&&s.onSelect.call(this.tree,!1,this))))},select:function(e){return this.data.unselectable?this.bSelected:this._select(e!==!1,!0,!0)},toggleSelect:function(){return this.select(!this.bSelected)},isSelected:function(){return this.bSelected},isLazy:function(){return!!this.data.isLazy},_loadContent:function(){try{var e=this.tree.options;this.tree.logDebug("_loadContent: start - %o",this),this.setLazyNodeStatus(DTNodeStatus_Loading),!0===e.onLazyRead.call(this.tree,this)&&(this.setLazyNodeStatus(DTNodeStatus_Ok),this.tree.logDebug("_loadContent: succeeded - %o",this))}catch(t){this.tree.logWarning("_loadContent: failed - %o",t),this.setLazyNodeStatus(DTNodeStatus_Error,{tooltip:""+t})}},_expand:function(e,t){if(this.bExpanded===e)return this.tree.logDebug("dtnode._expand(%o) IGNORED - %o",e,this),void 0;this.tree.logDebug("dtnode._expand(%o) - %o",e,this);var i=this.tree.options;if(!e&&this.getLevel()<i.minExpandLevel)return this.tree.logDebug("dtnode._expand(%o) prevented collapse - %o",e,this),void 0;if(!i.onQueryExpand||i.onQueryExpand.call(this.tree,e,this)!==!1){this.bExpanded=e,i.persist&&(e?this.tree.persistence.addExpand(this.data.key):this.tree.persistence.clearExpand(this.data.key));var s=!(this.data.isLazy&&null===this.childList||this._isLoading||t);if(this.render(s),this.bExpanded&&this.parent&&i.autoCollapse)for(var n=this._parentList(!1,!0),a=0,r=n.length;r>a;a++)n[a].collapseSiblings();return i.activeVisible&&this.tree.activeNode&&!this.tree.activeNode.isVisible()&&this.tree.activeNode.deactivate(),e&&this.data.isLazy&&null===this.childList&&!this._isLoading?(this._loadContent(),void 0):(i.onExpand&&i.onExpand.call(this.tree,e,this),void 0)}},isExpanded:function(){return this.bExpanded},expand:function(e){e=e!==!1,(this.childList||this.data.isLazy||!e)&&(null!==this.parent||e)&&this._expand(e)},scheduleAction:function(e,t){this.tree.timer&&(clearTimeout(this.tree.timer),this.tree.logDebug("clearTimeout(%o)",this.tree.timer));var i=this;switch(e){case"cancel":break;case"expand":this.tree.timer=setTimeout(function(){i.tree.logDebug("setTimeout: trigger expand"),i.expand(!0)},t);break;case"activate":this.tree.timer=setTimeout(function(){i.tree.logDebug("setTimeout: trigger activate"),i.activate()},t);break;default:throw"Invalid mode "+e}this.tree.logDebug("setTimeout(%s, %s): %s",e,t,this.tree.timer)},toggleExpand:function(){this.expand(!this.bExpanded)},collapseSiblings:function(){if(null!==this.parent)for(var e=this.parent.childList,t=0,i=e.length;i>t;t++)e[t]!==this&&e[t].bExpanded&&e[t]._expand(!1)},_onClick:function(e){var t=this.getEventTargetType(e);if("expander"===t)this.toggleExpand(),this.focus();else if("checkbox"===t)this.toggleSelect(),this.focus();else{this._userActivate();var i=this.span.getElementsByTagName("a");if(!i[0])return!0;BROWSER.msie&&9>parseInt(BROWSER.version,10)||i[0].focus()}e.preventDefault()},_onDblClick:function(){},_onKeydown:function(e){var t,i=!0;switch(e.which){case 107:case 187:this.bExpanded||this.toggleExpand();break;case 109:case 189:this.bExpanded&&this.toggleExpand();break;case 32:this._userActivate();break;case 8:this.parent&&this.parent.focus();break;case 37:this.bExpanded?(this.toggleExpand(),this.focus()):this.parent&&this.parent.parent&&this.parent.focus();break;case 39:this.bExpanded||!this.childList&&!this.data.isLazy?this.childList&&this.childList[0].focus():(this.toggleExpand(),this.focus());break;case 38:for(t=this.getPrevSibling();t&&t.bExpanded&&t.childList;)t=t.childList[t.childList.length-1];!t&&this.parent&&this.parent.parent&&(t=this.parent),t&&t.focus();break;case 40:if(this.bExpanded&&this.childList)t=this.childList[0];else for(var s=this._parentList(!1,!0),n=s.length-1;n>=0&&!(t=s[n].getNextSibling());n--);t&&t.focus();break;default:i=!1}i&&e.preventDefault()},_onKeypress:function(){},_onFocus:function(e){var t=this.tree.options;"blur"==e.type||"focusout"==e.type?(t.onBlur&&t.onBlur.call(this.tree,this),this.tree.tnFocused&&$(this.tree.tnFocused.span).removeClass(t.classNames.focused),this.tree.tnFocused=null,t.persist&&$.cookie(t.cookieId+"-focus","",t.cookie)):("focus"==e.type||"focusin"==e.type)&&(this.tree.tnFocused&&this.tree.tnFocused!==this&&(this.tree.logDebug("dtnode.onFocus: out of sync: curFocus: %o",this.tree.tnFocused),$(this.tree.tnFocused.span).removeClass(t.classNames.focused)),this.tree.tnFocused=this,t.onFocus&&t.onFocus.call(this.tree,this),$(this.tree.tnFocused.span).addClass(t.classNames.focused),t.persist&&$.cookie(t.cookieId+"-focus",this.data.key,t.cookie))},visit:function(e,t){var i=!0;if(t===!0&&(i=e(this),i===!1||"skip"===i))return i;if(this.childList)for(var s=0,n=this.childList.length;n>s&&(i=this.childList[s].visit(e,!0),i!==!1);s++);return i},visitParents:function(e,t){if(t&&e(this)===!1)return!1;for(var i=this.parent;i;){if(e(i)===!1)return!1;i=i.parent}return!0},remove:function(){if(this===this.tree.root)throw"Cannot remove system root";return this.parent.removeChild(this)},removeChild:function(e){var t=this.childList;if(1===t.length){if(e!==t[0])throw"removeChild: invalid child";return this.removeChildren()}e===this.tree.activeNode&&e.deactivate(),this.tree.options.persist&&(e.bSelected&&this.tree.persistence.clearSelect(e.data.key),e.bExpanded&&this.tree.persistence.clearExpand(e.data.key)),e.removeChildren(!0),this.ul&&e.li&&this.ul.removeChild(e.li);for(var i=0,s=t.length;s>i;i++)if(t[i]===e){this.childList.splice(i,1);break}},removeChildren:function(e,t){this.tree.logDebug("%s.removeChildren(%o)",this,e);var i=this.tree,s=this.childList;if(s){for(var n=0,a=s.length;a>n;n++){var r=s[n];r!==i.activeNode||t||r.deactivate(),this.tree.options.persist&&!t&&(r.bSelected&&this.tree.persistence.clearSelect(r.data.key),r.bExpanded&&this.tree.persistence.clearExpand(r.data.key)),r.removeChildren(!0,t),this.ul&&r.li&&$("li",$(this.ul)).remove()}this.childList=null}e||(this._isLoading=!1,this.render())},setTitle:function(e){this.fromDict({title:e})},reload:function(){throw"Use reloadChildren() instead"},reloadChildren:function(e){if(null===this.parent)throw"Use tree.reload() instead";if(!this.data.isLazy)throw"node.reloadChildren() requires lazy nodes.";if(e){var t=this,i="nodeLoaded.dynatree."+this.tree.$tree.attr("id")+"."+this.data.key;this.tree.$tree.bind(i,function(s,n,a){if(t.tree.$tree.unbind(i),t.tree.logDebug("loaded %o, %o, %o",s,n,a),n!==t)throw"got invalid load event";e.call(t.tree,n,a)})}this.removeChildren(),this._loadContent()},_loadKeyPath:function(e,t){var i=this.tree;if(i.logDebug("%s._loadKeyPath(%s)",this,e),""===e)throw"Key path must not be empty";var s=e.split(i.options.keyPathSeparator);if(""===s[0])throw"Key path must be relative (don't start with '/')";var n=s.shift();if(this.childList)for(var a=0,r=this.childList.length;r>a;a++){var o=this.childList[a];if(o.data.key===n){if(0===s.length)t.call(i,o,"ok");else if(!o.data.isLazy||null!==o.childList&&void 0!==o.childList)t.call(i,o,"loaded"),o._loadKeyPath(s.join(i.options.keyPathSeparator),t);else{i.logDebug("%s._loadKeyPath(%s) -> reloading %s...",this,e,o);var d=this;o.reloadChildren(function(n,a){a?(i.logDebug("%s._loadKeyPath(%s) -> reloaded %s.",n,e,n),t.call(i,o,"loaded"),n._loadKeyPath(s.join(i.options.keyPathSeparator),t)):(i.logWarning("%s._loadKeyPath(%s) -> reloadChildren() failed.",d,e),t.call(i,o,"error"))})}return}}t.call(i,void 0,"notfound",n,0===s.length),i.logWarning("Node not found: "+n)},resetLazy:function(){if(null===this.parent)throw"Use tree.reload() instead";if(!this.data.isLazy)throw"node.resetLazy() requires lazy nodes.";this.expand(!1),this.removeChildren()},_addChildNode:function(e,t){var i=this.tree,s=i.options,n=i.persistence;if(e.parent=this,null===this.childList?this.childList=[]:t||this.childList.length>0&&$(this.childList[this.childList.length-1].span).removeClass(s.classNames.lastsib),t){var a=$.inArray(t,this.childList);if(0>a)throw"<beforeNode> must be a child of <this>";this.childList.splice(a,0,e)}else this.childList.push(e);var r=i.isInitializing();if(s.persist&&n.cookiesFound&&r?(n.activeKey===e.data.key&&(i.activeNode=e),n.focusedKey===e.data.key&&(i.focusNode=e),e.bExpanded=$.inArray(e.data.key,n.expandedKeyList)>=0,e.bSelected=$.inArray(e.data.key,n.selectedKeyList)>=0):(e.data.activate&&(i.activeNode=e,s.persist&&(n.activeKey=e.data.key)),e.data.focus&&(i.focusNode=e,s.persist&&(n.focusedKey=e.data.key)),e.bExpanded=e.data.expand===!0,e.bExpanded&&s.persist&&n.addExpand(e.data.key),e.bSelected=e.data.select===!0,e.bSelected&&s.persist&&n.addSelect(e.data.key)),s.minExpandLevel>=e.getLevel()&&(this.bExpanded=!0),e.bSelected&&3==s.selectMode)for(var o=this;o;)o.hasSubSel||o._setSubSel(!0),o=o.parent;return i.bEnableUpdate&&this.render(),e},addChild:function(e,t){if("string"==typeof e)throw"Invalid data type for "+e;if(e&&0!==e.length){if(e instanceof DynaTreeNode)return this._addChildNode(e,t);e.length||(e=[e]);for(var i=this.tree.enableUpdate(!1),s=null,n=0,a=e.length;a>n;n++){var r=e[n],o=this._addChildNode(new DynaTreeNode(this,this.tree,r),t);s||(s=o),r.children&&o.addChild(r.children,null)}return this.tree.enableUpdate(i),s}},append:function(e){return this.tree.logWarning("node.append() is deprecated (use node.addChild() instead)."),this.addChild(e,null)},appendAjax:function(e){var t=this;if(this.removeChildren(!1,!0),this.setLazyNodeStatus(DTNodeStatus_Loading),e.debugLazyDelay){var i=e.debugLazyDelay;return e.debugLazyDelay=0,this.tree.logInfo("appendAjax: waiting for debugLazyDelay "+i),setTimeout(function(){t.appendAjax(e)},i),void 0}var s=e.success,n=e.error,a="nodeLoaded.dynatree."+this.tree.$tree.attr("id")+"."+this.data.key,r=$.extend({},this.tree.options.ajaxDefaults,e,{success:function(e,i){var n=t.tree.phase,r=t.tree.options;t.tree.phase="init",r.postProcess?e=r.postProcess.call(this,e,this.dataType):e&&e.hasOwnProperty("d")&&(e="string"==typeof e.d?$.parseJSON(e.d):e.d),$.isArray(e)&&0===e.length||t.addChild(e,null),t.tree.phase="postInit",s&&s.call(r,t,e,i),t.tree.logDebug("trigger "+a),t.tree.$tree.trigger(a,[t,!0]),t.tree.phase=n,t.setLazyNodeStatus(DTNodeStatus_Ok),$.isArray(e)&&0===e.length&&(t.childList=[],t.render())},error:function(e,i,s){t.tree.logWarning("appendAjax failed:",i,":\n",e,"\n",s),n&&n.call(r,t,e,i,s),t.tree.$tree.trigger(a,[t,!1]),t.setLazyNodeStatus(DTNodeStatus_Error,{info:i,tooltip:""+s})}});$.ajax(r)},move:function(e,t){var i;if(this!==e){if(!this.parent)throw"Cannot move system root";(void 0===t||"over"==t)&&(t="child");var s=this.parent,n="child"===t?e:e.parent;if(n.isDescendantOf(this))throw"Cannot move a node to it's own descendant";if(1==this.parent.childList.length)this.parent.childList=this.parent.data.isLazy?[]:null,this.parent.bExpanded=!1;else{if(i=$.inArray(this,this.parent.childList),0>i)throw"Internal error";this.parent.childList.splice(i,1)}if(this.parent.ul&&this.li&&this.parent.ul.removeChild(this.li),this.parent=n,n.hasChildren())switch(t){case"child":n.childList.push(this);break;case"before":if(i=$.inArray(e,n.childList),0>i)throw"Internal error";n.childList.splice(i,0,this);break;case"after":if(i=$.inArray(e,n.childList),0>i)throw"Internal error";n.childList.splice(i+1,0,this);break;default:throw"Invalid mode "+t}else n.childList=[this];if(n.ul||(n.ul=document.createElement("ul"),n.ul.style.display="none",n.li&&n.li.appendChild(n.ul)),this.li&&n.ul.appendChild(this.li),this.tree!==e.tree)throw this.visit(function(t){t.tree=e.tree},null,!0),"Not yet implemented.";s.isDescendantOf(n)||s.render(),n.isDescendantOf(s)||n.render()}},lastentry:void 0};var DynaTreeStatus=Class.create();DynaTreeStatus._getTreePersistData=function(e,t){var i=new DynaTreeStatus(e,t);return i.read(),i.toDict()},getDynaTreePersistData=DynaTreeStatus._getTreePersistData,DynaTreeStatus.prototype={initialize:function(e,t){void 0===e&&(e=$.ui.dynatree.prototype.options.cookieId),t=$.extend({},$.ui.dynatree.prototype.options.cookie,t),this.cookieId=e,this.cookieOpts=t,this.cookiesFound=void 0,this.activeKey=null,this.focusedKey=null,this.expandedKeyList=null,this.selectedKeyList=null},_log:function(){Array.prototype.unshift.apply(arguments,["debug"]),_log.apply(this,arguments)},read:function(){this.cookiesFound=!1;var e=$.cookie(this.cookieId+"-active");this.activeKey=e||"",e&&(this.cookiesFound=!0),e=$.cookie(this.cookieId+"-focus"),this.focusedKey=e||"",e&&(this.cookiesFound=!0),e=$.cookie(this.cookieId+"-expand"),this.expandedKeyList=e?e.split(","):[],e&&(this.cookiesFound=!0),e=$.cookie(this.cookieId+"-select"),this.selectedKeyList=e?e.split(","):[],e&&(this.cookiesFound=!0)},write:function(){$.cookie(this.cookieId+"-active",null===this.activeKey?"":this.activeKey,this.cookieOpts),$.cookie(this.cookieId+"-focus",null===this.focusedKey?"":this.focusedKey,this.cookieOpts),$.cookie(this.cookieId+"-expand",null===this.expandedKeyList?"":this.expandedKeyList.join(","),this.cookieOpts),$.cookie(this.cookieId+"-select",null===this.selectedKeyList?"":this.selectedKeyList.join(","),this.cookieOpts)},addExpand:function(e){0>$.inArray(e,this.expandedKeyList)&&(this.expandedKeyList.push(e),$.cookie(this.cookieId+"-expand",this.expandedKeyList.join(","),this.cookieOpts))},clearExpand:function(e){var t=$.inArray(e,this.expandedKeyList);t>=0&&(this.expandedKeyList.splice(t,1),$.cookie(this.cookieId+"-expand",this.expandedKeyList.join(","),this.cookieOpts))},addSelect:function(e){0>$.inArray(e,this.selectedKeyList)&&(this.selectedKeyList.push(e),$.cookie(this.cookieId+"-select",this.selectedKeyList.join(","),this.cookieOpts))},clearSelect:function(e){var t=$.inArray(e,this.selectedKeyList);t>=0&&(this.selectedKeyList.splice(t,1),$.cookie(this.cookieId+"-select",this.selectedKeyList.join(","),this.cookieOpts))},isReloading:function(){return this.cookiesFound===!0},toDict:function(){return{cookiesFound:this.cookiesFound,activeKey:this.activeKey,focusedKey:this.activeKey,expandedKeyList:this.expandedKeyList,selectedKeyList:this.selectedKeyList}},lastentry:void 0};var DynaTree=Class.create();DynaTree.version="@@Version",DynaTree.prototype={initialize:function(e){this.phase="init",this.$widget=e,this.options=e.options,this.$tree=e.element,this.timer=null,this.divTree=this.$tree.get(0),_initDragAndDrop(this)},_load:function(e){this.$widget;var t=this.options,i=this;this.bEnableUpdate=!0,this._nodeCount=1,this.activeNode=null,this.focusNode=null,void 0!==t.rootVisible&&this.logWarning("Option 'rootVisible' is no longer supported."),1>t.minExpandLevel&&(this.logWarning("Option 'minExpandLevel' must be >= 1."),t.minExpandLevel=1),t.classNames!==$.ui.dynatree.prototype.options.classNames&&(t.classNames=$.extend({},$.ui.dynatree.prototype.options.classNames,t.classNames)),t.ajaxDefaults!==$.ui.dynatree.prototype.options.ajaxDefaults&&(t.ajaxDefaults=$.extend({},$.ui.dynatree.prototype.options.ajaxDefaults,t.ajaxDefaults)),t.dnd!==$.ui.dynatree.prototype.options.dnd&&(t.dnd=$.extend({},$.ui.dynatree.prototype.options.dnd,t.dnd)),t.imagePath||$("script").each(function(){var e=/.*dynatree[^\/]*\.js$/i;return this.src.search(e)>=0?(t.imagePath=this.src.indexOf("/")>=0?this.src.slice(0,this.src.lastIndexOf("/"))+"/skin/":"skin/",i.logDebug("Guessing imagePath from '%s': '%s'",this.src,t.imagePath),!1):void 0}),this.persistence=new DynaTreeStatus(t.cookieId,t.cookie),t.persist&&($.cookie||_log("warn","Please include jquery.cookie.js to use persistence."),this.persistence.read()),this.logDebug("DynaTree.persistence: %o",this.persistence.toDict()),this.cache={tagEmpty:"<span class='"+t.classNames.empty+"'></span>",tagVline:"<span class='"+t.classNames.vline+"'></span>",tagExpander:"<span class='"+t.classNames.expander+"'></span>",tagConnector:"<span class='"+t.classNames.connector+"'></span>",tagNodeIcon:"<span class='"+t.classNames.nodeIcon+"'></span>",tagCheckbox:"<span class='"+t.classNames.checkbox+"'></span>",lastentry:void 0},(t.children||t.initAjax&&t.initAjax.url||t.initId)&&$(this.divTree).empty();var s=this.$tree.find(">ul:first").hide();this.tnRoot=new DynaTreeNode(null,this,{}),this.tnRoot.bExpanded=!0,this.tnRoot.render(),this.divTree.appendChild(this.tnRoot.ul);var n=this.tnRoot,a=t.persist&&this.persistence.isReloading(),r=!1,o=this.enableUpdate(!1);this.logDebug("Dynatree._load(): read tree structure..."),t.children?n.addChild(t.children):t.initAjax&&t.initAjax.url?(r=!0,n.data.isLazy=!0,this._reloadAjax(e)):t.initId?this._createFromTag(n,$("#"+t.initId)):(this._createFromTag(n,s),s.remove()),this._checkConsistency(),r||3!=t.selectMode||n._updatePartSelectionState(),this.logDebug("Dynatree._load(): render nodes..."),this.enableUpdate(o),this.logDebug("Dynatree._load(): bind events..."),this.$widget.bind(),this.logDebug("Dynatree._load(): postInit..."),this.phase="postInit",t.persist&&this.persistence.write(),this.focusNode&&this.focusNode.isVisible()&&(this.logDebug("Focus on init: %o",this.focusNode),this.focusNode.focus()),r||(t.onPostInit&&t.onPostInit.call(this,a,!1),e&&e.call(this,"ok")),this.phase="idle"},_reloadAjax:function(e){var t=this.options;if(!t.initAjax||!t.initAjax.url)throw"tree.reload() requires 'initAjax' mode.";var i=this.persistence,s=$.extend({},t.initAjax);s.addActiveKey&&(s.data.activeKey=i.activeKey),s.addFocusedKey&&(s.data.focusedKey=i.focusedKey),s.addExpandedKeyList&&(s.data.expandedKeyList=i.expandedKeyList.join(",")),s.addSelectedKeyList&&(s.data.selectedKeyList=i.selectedKeyList.join(",")),s.success&&this.logWarning("initAjax: success callback is ignored; use onPostInit instead."),s.error&&this.logWarning("initAjax: error callback is ignored; use onPostInit instead.");var n=i.isReloading();s.success=function(i){3==t.selectMode&&i.tree.tnRoot._updatePartSelectionState(),t.onPostInit&&t.onPostInit.call(i.tree,n,!1),e&&e.call(i.tree,"ok")},s.error=function(i,s,a,r){t.onPostInit&&t.onPostInit.call(i.tree,n,!0,s,a,r),e&&e.call(i.tree,"error",s,a,r)},this.logDebug("Dynatree._init(): send Ajax request..."),this.tnRoot.appendAjax(s)},toString:function(){return"Dynatree '"+this.$tree.attr("id")+"'"},toDict:function(e){var t=this.tnRoot.toDict(!0);return e?t:t.children
},serializeArray:function(e){for(var t=this.getSelectedNodes(e),i=this.$tree.attr("name")||this.$tree.attr("id"),s=[],n=0,a=t.length;a>n;n++)s.push({name:i,value:t[n].data.key});return s},getPersistData:function(){return this.persistence.toDict()},logDebug:function(){this.options.debugLevel>=2&&(Array.prototype.unshift.apply(arguments,["debug"]),_log.apply(this,arguments))},logInfo:function(){this.options.debugLevel>=1&&(Array.prototype.unshift.apply(arguments,["info"]),_log.apply(this,arguments))},logWarning:function(){Array.prototype.unshift.apply(arguments,["warn"]),_log.apply(this,arguments)},isInitializing:function(){return"init"==this.phase||"postInit"==this.phase},isReloading:function(){return("init"==this.phase||"postInit"==this.phase)&&this.options.persist&&this.persistence.cookiesFound},isUserEvent:function(){return"userEvent"==this.phase},redraw:function(){this.tnRoot.render(!1,!1)},renderInvisibleNodes:function(){this.tnRoot.render(!1,!0)},reload:function(e){this._load(e)},getRoot:function(){return this.tnRoot},enable:function(){this.$widget.enable()},disable:function(){this.$widget.disable()},getNodeByKey:function(e){var t=document.getElementById(this.options.idPrefix+e);if(t)return t.dtnode?t.dtnode:null;var i=null;return this.visit(function(t){return t.data.key===e?(i=t,!1):void 0},!0),i},getActiveNode:function(){return this.activeNode},reactivate:function(e){var t=this.activeNode;t&&(this.activeNode=null,t.activate(),e&&t.focus())},getSelectedNodes:function(e){var t=[];return this.tnRoot.visit(function(i){return i.bSelected&&(t.push(i),e===!0)?"skip":void 0}),t},activateKey:function(e){var t=null===e?null:this.getNodeByKey(e);return t?(t.focus(),t.activate(),t):(this.activeNode&&this.activeNode.deactivate(),this.activeNode=null,null)},loadKeyPath:function(e,t){var i=e.split(this.options.keyPathSeparator);return""===i[0]&&i.shift(),i[0]==this.tnRoot.data.key&&(this.logDebug("Removed leading root key."),i.shift()),e=i.join(this.options.keyPathSeparator),this.tnRoot._loadKeyPath(e,t)},selectKey:function(e,t){var i=this.getNodeByKey(e);return i?(i.select(t),i):null},enableUpdate:function(e){return this.bEnableUpdate==e?e:(this.bEnableUpdate=e,e&&this.redraw(),!e)},count:function(){return this.tnRoot.countChildren()},visit:function(e,t){return this.tnRoot.visit(e,t)},_createFromTag:function(parentTreeNode,$ulParent){var self=this;$ulParent.find(">li").each(function(){var $li=$(this),$liSpan=$li.find(">span:first"),$liA=$li.find(">a:first"),title,href=null,target=null,tooltip;if($liSpan.length)title=$liSpan.html();else if($liA.length)title=$liA.html(),href=$liA.attr("href"),target=$liA.attr("target"),tooltip=$liA.attr("title");else{title=$li.html();var iPos=title.search(/<ul/i);title=iPos>=0?$.trim(title.substring(0,iPos)):$.trim(title)}var data={title:title,tooltip:tooltip,isFolder:$li.hasClass("folder"),isLazy:$li.hasClass("lazy"),expand:$li.hasClass("expanded"),select:$li.hasClass("selected"),activate:$li.hasClass("active"),focus:$li.hasClass("focused"),noLink:$li.hasClass("noLink")};if(href&&(data.href=href,data.target=target),$li.attr("title")&&(data.tooltip=$li.attr("title")),$li.attr("id")&&(data.key=""+$li.attr("id")),$li.attr("data")){var dataAttr=$.trim($li.attr("data"));if(dataAttr){"{"!=dataAttr.charAt(0)&&(dataAttr="{"+dataAttr+"}");try{$.extend(data,eval("("+dataAttr+")"))}catch(e){throw"Error parsing node data: "+e+"\ndata:\n'"+dataAttr+"'"}}}var childNode=parentTreeNode.addChild(data),$ul=$li.find(">ul:first");$ul.length&&self._createFromTag(childNode,$ul)})},_checkConsistency:function(){},_setDndStatus:function(e,t,i,s,n){var a,r=e?$(e.span):null,o=$(t.span),d=0,l="center";if(this.$dndMarker||(this.$dndMarker=$("<div id='dynatree-drop-marker'></div>").hide().css({"z-index":1e3}).prependTo($(this.divTree).parent())),"after"===s||"before"===s||"over"===s){switch(s){case"before":this.$dndMarker.removeClass("dynatree-drop-after dynatree-drop-over"),this.$dndMarker.addClass("dynatree-drop-before"),l="top";break;case"after":this.$dndMarker.removeClass("dynatree-drop-before dynatree-drop-over"),this.$dndMarker.addClass("dynatree-drop-after"),l="bottom";break;default:this.$dndMarker.removeClass("dynatree-drop-after dynatree-drop-before"),this.$dndMarker.addClass("dynatree-drop-over"),o.addClass("dynatree-drop-target"),d=8}a=jquerySupports.positionMyOfs?{my:"left"+offsetString(d)+" center",at:"left "+l,of:o}:{my:"left center",at:"left "+l,of:o,offset:""+d+" 0"},this.$dndMarker.show().position(a)}else o.removeClass("dynatree-drop-target"),this.$dndMarker.hide();"after"===s?o.addClass("dynatree-drop-after"):o.removeClass("dynatree-drop-after"),"before"===s?o.addClass("dynatree-drop-before"):o.removeClass("dynatree-drop-before"),n===!0?(r&&r.addClass("dynatree-drop-accept"),o.addClass("dynatree-drop-accept"),i.addClass("dynatree-drop-accept")):(r&&r.removeClass("dynatree-drop-accept"),o.removeClass("dynatree-drop-accept"),i.removeClass("dynatree-drop-accept")),n===!1?(r&&r.addClass("dynatree-drop-reject"),o.addClass("dynatree-drop-reject"),i.addClass("dynatree-drop-reject")):(r&&r.removeClass("dynatree-drop-reject"),o.removeClass("dynatree-drop-reject"),i.removeClass("dynatree-drop-reject"))},_onDragEvent:function(e,t,i,s,n,a){var r,o,d,l=this.options.dnd,h=null,c=$(t.span);switch(e){case"helper":var u=$("<div class='dynatree-drag-helper'><span class='dynatree-drag-helper-img' /></div>").append(c.find(".dynatree-title").clone());$("ul.dynatree-container",t.tree.divTree).append(u),u.data("dtSourceNode",t),h=u;break;case"start":t.isStatusNode()?h=!1:l.onDragStart&&(h=l.onDragStart(t)),h===!1?(this.logDebug("tree.onDragStart() cancelled"),n.helper.trigger("mouseup"),n.helper.hide()):c.addClass("dynatree-drag-source");break;case"enter":d=l.onDragEnter?l.onDragEnter(t,i,n,a):null,h=d?$.isArray(d)?{over:$.inArray("over",d)>=0,before:$.inArray("before",d)>=0,after:$.inArray("after",d)>=0}:{over:d===!0||"over"===d,before:d===!0||"before"===d,after:d===!0||"after"===d}:!1,n.helper.data("enterResponse",h);break;case"over":if(o=n.helper.data("enterResponse"),r=null,o===!1);else if("string"==typeof o)r=o;else{var p=c.offset(),f={x:s.pageX-p.left,y:s.pageY-p.top},g={x:f.x/c.width(),y:f.y/c.height()};o.after&&g.y>.75?r="after":!o.over&&o.after&&g.y>.5?r="after":o.before&&.25>=g.y?r="before":!o.over&&o.before&&.5>=g.y?r="before":o.over&&(r="over"),l.preventVoidMoves&&(t===i?r=null:"before"===r&&i&&t===i.getNextSibling()?r=null:"after"===r&&i&&t===i.getPrevSibling()?r=null:"over"===r&&i&&i.parent===t&&i.isLastSibling()&&(r=null)),n.helper.data("hitMode",r)}"over"===r&&l.autoExpandMS&&t.hasChildren()!==!1&&!t.bExpanded&&t.scheduleAction("expand",l.autoExpandMS),r&&l.onDragOver&&(h=l.onDragOver(t,i,r,n,a),("over"===h||"before"===h||"after"===h)&&(r=h)),this._setDndStatus(i,t,n.helper,r,h!==!1&&null!==r);break;case"drop":var y=n.helper.hasClass("dynatree-drop-reject");r=n.helper.data("hitMode"),r&&l.onDrop&&!y&&l.onDrop(t,i,r,n,a);break;case"leave":t.scheduleAction("cancel"),n.helper.data("enterResponse",null),n.helper.data("hitMode",null),this._setDndStatus(i,t,n.helper,"out",void 0),l.onDragLeave&&l.onDragLeave(t,i,n,a);break;case"stop":c.removeClass("dynatree-drag-source"),l.onDragStop&&l.onDragStop(t);break;default:throw"Unsupported drag event: "+e}return h},cancelDrag:function(){var e=$.ui.ddmanager.current;e&&e.cancel()},lastentry:void 0},$.widget("ui.dynatree",{_init:function(){return 0>versionCompare($.ui.version,"1.8")?(this.options.debugLevel>=0&&_log("warn","ui.dynatree._init() was called; you should upgrade to jquery.ui.core.js v1.8 or higher."),this._create()):(this.options.debugLevel>=2&&_log("debug","ui.dynatree._init() was called; no current default functionality."),void 0)},_create:function(){var e=this.options;e.debugLevel>=1&&logMsg("Dynatree._create(): version='%s', debugLevel=%o.",$.ui.dynatree.version,this.options.debugLevel),this.options.event+=".dynatree",this.element.get(0),this.tree=new DynaTree(this),this.tree._load(),this.tree.logDebug("Dynatree._init(): done.")},bind:function(){function e(e){e=$.event.fix(e||window.event);var t=$.ui.dynatree.getNode(e.target);return t?t._onFocus(e):!1}this.unbind();var t="click.dynatree dblclick.dynatree";this.options.keyboard&&(t+=" keypress.dynatree keydown.dynatree"),this.element.bind(t,function(e){var t=$.ui.dynatree.getNode(e.target);if(!t)return!0;var i=t.tree,s=i.options;i.logDebug("event(%s): dtnode: %s",e.type,t);var n=i.phase;i.phase="userEvent";try{switch(e.type){case"click":return s.onClick&&s.onClick.call(i,t,e)===!1?!1:t._onClick(e);case"dblclick":return s.onDblClick&&s.onDblClick.call(i,t,e)===!1?!1:t._onDblClick(e);case"keydown":return s.onKeydown&&s.onKeydown.call(i,t,e)===!1?!1:t._onKeydown(e);case"keypress":return s.onKeypress&&s.onKeypress.call(i,t,e)===!1?!1:t._onKeypress(e)}}catch(a){i.logWarning("bind(%o): dtnode: %o, error: %o",e,t,a)}finally{i.phase=n}});var i=this.tree.divTree;i.addEventListener?(i.addEventListener("focus",e,!0),i.addEventListener("blur",e,!0)):i.onfocusin=i.onfocusout=e},unbind:function(){this.element.unbind(".dynatree")},enable:function(){this.bind(),$.Widget.prototype.enable.apply(this,arguments)},disable:function(){this.unbind(),$.Widget.prototype.disable.apply(this,arguments)},getTree:function(){return this.tree},getRoot:function(){return this.tree.getRoot()},getActiveNode:function(){return this.tree.getActiveNode()},getSelectedNodes:function(){return this.tree.getSelectedNodes()},lastentry:void 0}),0>versionCompare($.ui.version,"1.8")&&($.ui.dynatree.getter="getTree getRoot getActiveNode getSelectedNodes"),$.extend($.ui.dynatree,{version:"1.2.6",buildType:"release",_DynaTreeClass:DynaTree,_DynaTreeNodeClass:DynaTreeNode,getNode:function(e){if(e instanceof DynaTreeNode)return e;for(void 0!==e.selector&&(e=e[0]);e;){if(e.dtnode)return e.dtnode;e=e.parentNode}return null},getPersistData:DynaTreeStatus._getTreePersistData}),$.ui.dynatree.prototype.options={title:"Dynatree",minExpandLevel:1,imagePath:null,children:null,initId:null,initAjax:null,autoFocus:!0,keyboard:!0,persist:!1,autoCollapse:!1,clickFolderMode:3,activeVisible:!0,checkbox:!1,selectMode:2,fx:null,noLink:!1,onClick:null,onDblClick:null,onKeydown:null,onKeypress:null,onFocus:null,onBlur:null,onQueryActivate:null,onQuerySelect:null,onQueryExpand:null,onPostInit:null,onActivate:null,onDeactivate:null,onSelect:null,onExpand:null,onLazyRead:null,onCustomRender:null,onCreate:null,onRender:null,postProcess:null,dnd:{onDragStart:null,onDragStop:null,revert:!1,autoExpandMS:1e3,preventVoidMoves:!0,onDragEnter:null,onDragOver:null,onDrop:null,onDragLeave:null},ajaxDefaults:{cache:!1,timeout:0,dataType:"json"},strings:{loading:"Loading&#8230;",loadError:"Load error!"},generateIds:!1,idPrefix:"dynatree-id-",keyPathSeparator:"/",cookieId:"dynatree",cookie:{expires:null},classNames:{container:"dynatree-container",node:"dynatree-node",folder:"dynatree-folder",empty:"dynatree-empty",vline:"dynatree-vline",expander:"dynatree-expander",connector:"dynatree-connector",checkbox:"dynatree-checkbox",radio:"dynatree-radio",nodeIcon:"dynatree-icon",title:"dynatree-title",noConnector:"dynatree-no-connector",nodeError:"dynatree-statusnode-error",nodeWait:"dynatree-statusnode-wait",hidden:"dynatree-hidden",combinedExpanderPrefix:"dynatree-exp-",combinedIconPrefix:"dynatree-ico-",nodeLoading:"dynatree-loading",hasChildren:"dynatree-has-children",active:"dynatree-active",selected:"dynatree-selected",expanded:"dynatree-expanded",lazy:"dynatree-lazy",focused:"dynatree-focused",partsel:"dynatree-partsel",lastsib:"dynatree-lastsib"},debugLevel:1,lastentry:void 0},0>versionCompare($.ui.version,"1.8")&&($.ui.dynatree.defaults=$.ui.dynatree.prototype.options),$.ui.dynatree.nodedatadefaults={title:null,key:null,isFolder:!1,isLazy:!1,tooltip:null,href:null,icon:null,addClass:null,noLink:!1,activate:!1,focus:!1,expand:!1,select:!1,hideCheckbox:!1,unselectable:!1,children:null,lastentry:void 0};var didRegisterDnd=!1,_registerDnd=function(){didRegisterDnd||($.ui.plugin.add("draggable","connectToDynatree",{start:function(e,t){var i=$(this).data("ui-draggable")||$(this).data("draggable"),s=t.helper.data("dtSourceNode")||null;return s?(i.offset.click.top=-2,i.offset.click.left=16,s.tree._onDragEvent("start",s,null,e,t,i)):void 0},drag:function(e,t){var i=$(this).data("ui-draggable")||$(this).data("draggable"),s=t.helper.data("dtSourceNode")||null,n=t.helper.data("dtTargetNode")||null,a=$.ui.dynatree.getNode(e.target);if(e.target&&!a){var r=$(e.target).closest("div.dynatree-drag-helper,#dynatree-drop-marker").length>0;if(r)return}t.helper.data("dtTargetNode",a),n&&n!==a&&n.tree._onDragEvent("leave",n,s,e,t,i),a&&a.tree.options.dnd.onDrop&&(a===n?a.tree._onDragEvent("over",a,s,e,t,i):a.tree._onDragEvent("enter",a,s,e,t,i))},stop:function(e,t){var i=$(this).data("ui-draggable")||$(this).data("draggable"),s=t.helper.data("dtSourceNode")||null,n=t.helper.data("dtTargetNode")||null,a=e.type,r="mouseup"==a&&1==e.which;logMsg("draggable-connectToDynatree.stop: targetNode(from event): %s, dtTargetNode: %s",n,t.helper.data("dtTargetNode")),r||logMsg("Drag was cancelled"),n&&(r&&n.tree._onDragEvent("drop",n,s,e,t,i),n.tree._onDragEvent("leave",n,s,e,t,i)),s&&s.tree._onDragEvent("stop",s,null,e,t,i)}}),didRegisterDnd=!0)}})(jQuery);