@extends('layouts.admin')
@section('pageTitle', 'Edit Endpoint Configuration')

@push('styles')
<style>
    .required:after {
        content: " *";
        color: red;
    }
    .help-block {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    .form-group {
        margin-bottom: 1.5rem;
    }
    .card-header h4 {
        margin: 0;
        color: #495057;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card card-default">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4><i class="fa fa-edit"></i> {{ trans('global.edit') }} {{ trans('cruds.endpointConfiguration.title_singular') }}</h4>
                        </div>
                        <div class="col-auto">
                            <a href="{{ route('admin.endpoint-configurations.index') }}" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> {{ trans('global.back_to_list') }}
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.endpoint-configurations.update', [$endpointConfiguration->id]) }}" enctype="multipart/form-data">
                        @method('PUT')
                        @csrf

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="name">{{ trans('cruds.endpointConfiguration.fields.name') }}</label>
                                    <input class="form-control {{ $errors->has('name') ? 'is-invalid' : '' }}" type="text" name="name" id="name" value="{{ old('name', $endpointConfiguration->name) }}" required placeholder="Enter configuration name">
                                    @if($errors->has('name'))
                                        <div class="invalid-feedback">
                                            {{ $errors->first('name') }}
                                        </div>
                                    @endif
                                    <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.name_helper') }}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="erp_selection">{{ trans('cruds.endpointConfiguration.fields.erp_selection') }}</label>
                                    <select class="form-control select2 {{ $errors->has('erp_selection') ? 'is-invalid' : '' }}" name="erp_selection" id="erp_selection" required>
                                        @foreach($erpOptions as $key => $label)
                                            <option value="{{ $key }}" {{ old('erp_selection', $endpointConfiguration->erp_selection) == $key ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('erp_selection'))
                                        <div class="invalid-feedback">
                                            {{ $errors->first('erp_selection') }}
                                        </div>
                                    @endif
                                    <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.erp_selection_helper') }}</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="process_selection">{{ trans('cruds.endpointConfiguration.fields.process_selection') }}</label>
                                    <select class="form-control select2 {{ $errors->has('process_selection') ? 'is-invalid' : '' }}" name="process_selection" id="process_selection" required>
                                        <option value="">{{ trans('global.pleaseSelect') }}</option>
                                        @foreach($processOptions as $key => $label)
                                            <option value="{{ $key }}" {{ old('process_selection', $endpointConfiguration->process_selection) == $key ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('process_selection'))
                                        <div class="invalid-feedback">
                                            {{ $errors->first('process_selection') }}
                                        </div>
                                    @endif
                                    <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.process_selection_helper') }}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="endpoint_type">{{ trans('cruds.endpointConfiguration.fields.endpoint_type') }}</label>
                                    <select class="form-control select2 {{ $errors->has('endpoint_type') ? 'is-invalid' : '' }}" name="endpoint_type" id="endpoint_type" required>
                                        @foreach($endpointTypeOptions as $key => $label)
                                            <option value="{{ $key }}" {{ old('endpoint_type', $endpointConfiguration->endpoint_type) == $key ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('endpoint_type'))
                                        <div class="invalid-feedback">
                                            {{ $errors->first('endpoint_type') }}
                                        </div>
                                    @endif
                                    <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.endpoint_type_helper') }}</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="required" for="url">{{ trans('cruds.endpointConfiguration.fields.url') }}</label>
                            <textarea class="form-control {{ $errors->has('url') ? 'is-invalid' : '' }}" name="url" id="url" rows="3" required placeholder="https://api.example.com/endpoint">{{ old('url', $endpointConfiguration->url) }}</textarea>
                            @if($errors->has('url'))
                                <div class="invalid-feedback">
                                    {{ $errors->first('url') }}
                                </div>
                            @endif
                            <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.url_helper') }}</small>
                        </div>

                        <div class="form-group">
                            <label for="body_data_field">{{ trans('cruds.endpointConfiguration.fields.body_data_field') }}</label>
                            <div class="card">
                                <div class="card-header">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h6 class="mb-0">Field Mapping Configuration</h6>
                                        </div>
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-sm btn-success" id="addFieldBtn">
                                                <i class="fa fa-plus"></i> Add Field
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div id="fieldMappingContainer">
                                        <!-- Dynamic fields will be added here -->
                                    </div>
                                    <div id="noFieldsMessage" class="text-center text-muted py-3">
                                        <i class="fa fa-info-circle"></i> No fields added yet. Click "Add Field" to start.
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="body_data_field" id="body_data_field_hidden" value="{{ old('body_data_field', $endpointConfiguration->body_data_field_json) }}">
                            @if($errors->has('body_data_field'))
                                <div class="invalid-feedback d-block">
                                    {{ $errors->first('body_data_field') }}
                                </div>
                            @endif
                            <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.body_data_field_helper') }}</small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="hidden" name="is_active" value="0">
                                <input class="custom-control-input" type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', $endpointConfiguration->is_active) == 1 ? 'checked' : '' }}>
                                <label class="custom-control-label" for="is_active">
                                    {{ trans('cruds.endpointConfiguration.fields.is_active') }}
                                </label>
                                @if($errors->has('is_active'))
                                    <div class="invalid-feedback d-block">
                                        {{ $errors->first('is_active') }}
                                    </div>
                                @endif
                                <small class="help-block d-block">{{ trans('cruds.endpointConfiguration.fields.is_active_helper') }}</small>
                            </div>
                        </div>

                        <hr>

                        <div class="form-group mb-0">
                            <button class="btn btn-primary" type="submit">
                                <i class="fa fa-save"></i> {{ trans('global.update') }}
                            </button>
                            <a class="btn btn-secondary ml-2" href="{{ route('admin.endpoint-configurations.index') }}">
                                <i class="fa fa-times"></i> {{ trans('global.cancel') }}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    let fieldCounter = 0;

    // Data types options
    const dataTypes = [
        'string', 'integer', 'float', 'boolean', 'date', 'datetime', 'email', 'url', 'text', 'json'
    ];

    // Load existing data if editing
    const existingData = $('#body_data_field_hidden').val();
    if (existingData) {
        try {
            const fields = JSON.parse(existingData);
            if (Array.isArray(fields)) {
                fields.forEach(field => addFieldRow(field));
            }
        } catch (e) {
            console.log('Error parsing JSON data:', e);
        }
    }

    // Add field button click
    $('#addFieldBtn').click(function() {
        addFieldRow();
    });

    // Function to add a new field row
    function addFieldRow(fieldData = {}) {
        fieldCounter++;

        const fieldRow = `
            <div class="field-row border rounded p-3 mb-3" data-field-id="${fieldCounter}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Field Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-sm field-name"
                                   placeholder="e.g., item_code" value="${fieldData.name || ''}" required>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Data Type</label>
                            <select class="form-control form-control-sm field-datatype">
                                ${dataTypes.map(type =>
                                    `<option value="${type}" ${fieldData.datatype === type ? 'selected' : ''}>${type}</option>`
                                ).join('')}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Max Length</label>
                            <input type="number" class="form-control form-control-sm field-maxlength"
                                   placeholder="255" value="${fieldData.maxlength || ''}" min="1">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Required</label>
                            <select class="form-control form-control-sm field-required">
                                <option value="false" ${fieldData.required === false ? 'selected' : ''}>No</option>
                                <option value="true" ${fieldData.required === true ? 'selected' : ''}>Yes</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Default Value</label>
                            <input type="text" class="form-control form-control-sm field-default"
                                   placeholder="Optional" value="${fieldData.default || ''}">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">&nbsp;</label>
                            <button type="button" class="btn btn-sm btn-danger btn-block remove-field">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-11">
                        <div class="form-group mb-0">
                            <label class="small font-weight-bold">Description</label>
                            <input type="text" class="form-control form-control-sm field-description"
                                   placeholder="Optional field description" value="${fieldData.description || ''}">
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('#fieldMappingContainer').append(fieldRow);
        updateNoFieldsMessage();
        updateHiddenField();
    }

    // Remove field
    $(document).on('click', '.remove-field', function() {
        $(this).closest('.field-row').remove();
        updateNoFieldsMessage();
        updateHiddenField();
    });

    // Update hidden field when any input changes
    $(document).on('input change', '.field-row input, .field-row select', function() {
        updateHiddenField();
    });

    // Function to update the hidden field with JSON data
    function updateHiddenField() {
        const fields = [];

        $('.field-row').each(function() {
            const row = $(this);
            const field = {
                name: row.find('.field-name').val().trim(),
                datatype: row.find('.field-datatype').val(),
                maxlength: row.find('.field-maxlength').val() ? parseInt(row.find('.field-maxlength').val()) : null,
                required: row.find('.field-required').val() === 'true',
                default: row.find('.field-default').val().trim() || null,
                description: row.find('.field-description').val().trim() || null
            };

            if (field.name) {
                fields.push(field);
            }
        });

        $('#body_data_field_hidden').val(JSON.stringify(fields));
    }

    // Function to show/hide no fields message
    function updateNoFieldsMessage() {
        if ($('.field-row').length === 0) {
            $('#noFieldsMessage').show();
        } else {
            $('#noFieldsMessage').hide();
        }
    }

    // Initial state
    updateNoFieldsMessage();
});
</script>
@endpush
