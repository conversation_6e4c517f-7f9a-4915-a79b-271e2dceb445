<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\FormIntegrationSetting;
use App\Models\Form;
use App\Models\EndpointConfiguration;
use App\Models\User;
use App\Models\UserGroup;

class FormIntegrationSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('Starting Form Integration Setting seeding...');

        // Get or create a super admin user
        $superAdmin = User::where('type', 1)->first();
        if (!$superAdmin) {
            $superAdmin = User::create([
                'name' => 'System Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'type' => 1,
                'email_verified_at' => now(),
            ]);
            $this->command->info('Created system admin user for seeding');
        }

        // Get or create a user group
        $userGroup = UserGroup::first();
        if (!$userGroup) {
            $userGroup = UserGroup::create([
                'name' => 'Default Group',
                'description' => 'Default user group for testing',
                'is_active' => true,
            ]);
            $this->command->info('Created default user group for seeding');
        }

        // Create hardcoded forms for testing
        $forms = $this->createTestForms($superAdmin, $userGroup);
        
        // Get existing endpoint configurations
        $endpointConfigurations = EndpointConfiguration::all();
        
        if ($endpointConfigurations->isEmpty()) {
            $this->command->error('No endpoint configurations found. Please run EndpointConfigurationSeeder first.');
            return;
        }

        // Create form integration settings
        $integrationSettings = $this->getIntegrationSettingsData($forms, $endpointConfigurations, $superAdmin);

        foreach ($integrationSettings as $settingData) {
            // Check if integration setting already exists
            $existingSetting = FormIntegrationSetting::where('name', $settingData['name'])->first();

            if (!$existingSetting) {
                FormIntegrationSetting::create($settingData);
                $this->command->info("Created form integration setting: {$settingData['name']}");
            } else {
                $this->command->info("Form integration setting already exists: {$settingData['name']}");
            }
        }

        $this->command->info('Form Integration Setting seeding completed!');
    }

    /**
     * Create test forms with hardcoded data
     */
    private function createTestForms($superAdmin, $userGroup)
    {
        $forms = [];

        // Inventory Issue Form
        $inventoryIssueForm = [
            'title' => 'Inventory Issue Form',
            'content' => [
                'components' => [
                    [
                        'key' => 'item_code',
                        'type' => 'textfield',
                        'label' => 'Item Code',
                        'validate' => ['required' => true],
                        'description' => 'Enter the item code to issue'
                    ],
                    [
                        'key' => 'quantity',
                        'type' => 'number',
                        'label' => 'Quantity',
                        'validate' => ['required' => true, 'min' => 1],
                        'description' => 'Enter the quantity to issue'
                    ],
                    [
                        'key' => 'location_code',
                        'type' => 'textfield',
                        'label' => 'Location Code',
                        'validate' => ['required' => true],
                        'description' => 'Enter the location code'
                    ],
                    [
                        'key' => 'cost_center',
                        'type' => 'textfield',
                        'label' => 'Cost Center',
                        'validate' => ['required' => false],
                        'description' => 'Enter the cost center'
                    ],
                    [
                        'key' => 'reason',
                        'type' => 'textarea',
                        'label' => 'Reason',
                        'validate' => ['required' => false],
                        'description' => 'Enter the reason for issue'
                    ]
                ]
            ],
            'user_group_id' => $userGroup->id,
            'created_by' => $superAdmin->id,
            'is_active' => true,
        ];

        // Inventory Receipt Form
        $inventoryReceiptForm = [
            'title' => 'Inventory Receipt Form',
            'content' => [
                'components' => [
                    [
                        'key' => 'material_number',
                        'type' => 'textfield',
                        'label' => 'Material Number',
                        'validate' => ['required' => true],
                        'description' => 'Enter the material number'
                    ],
                    [
                        'key' => 'quantity',
                        'type' => 'number',
                        'label' => 'Quantity',
                        'validate' => ['required' => true, 'min' => 1],
                        'description' => 'Enter the quantity received'
                    ],
                    [
                        'key' => 'plant_code',
                        'type' => 'textfield',
                        'label' => 'Plant Code',
                        'validate' => ['required' => true],
                        'description' => 'Enter the plant code'
                    ],
                    [
                        'key' => 'storage_location',
                        'type' => 'textfield',
                        'label' => 'Storage Location',
                        'validate' => ['required' => true],
                        'description' => 'Enter the storage location'
                    ],
                    [
                        'key' => 'unit_cost',
                        'type' => 'number',
                        'label' => 'Unit Cost',
                        'validate' => ['required' => false],
                        'description' => 'Enter the unit cost'
                    ]
                ]
            ],
            'user_group_id' => $userGroup->id,
            'created_by' => $superAdmin->id,
            'is_active' => true,
        ];

        // Stock Transfer Form
        $stockTransferForm = [
            'title' => 'Stock Transfer Form',
            'content' => [
                'components' => [
                    [
                        'key' => 'item_code',
                        'type' => 'textfield',
                        'label' => 'Item Code',
                        'validate' => ['required' => true],
                        'description' => 'Enter the item code to transfer'
                    ],
                    [
                        'key' => 'quantity',
                        'type' => 'number',
                        'label' => 'Quantity',
                        'validate' => ['required' => true, 'min' => 1],
                        'description' => 'Enter the quantity to transfer'
                    ],
                    [
                        'key' => 'from_location',
                        'type' => 'textfield',
                        'label' => 'From Location',
                        'validate' => ['required' => true],
                        'description' => 'Enter the source location'
                    ],
                    [
                        'key' => 'to_location',
                        'type' => 'textfield',
                        'label' => 'To Location',
                        'validate' => ['required' => true],
                        'description' => 'Enter the destination location'
                    ],
                    [
                        'key' => 'transfer_reason',
                        'type' => 'select',
                        'label' => 'Transfer Reason',
                        'data' => [
                            'values' => [
                                ['label' => 'Rebalancing', 'value' => 'REBALANCE'],
                                ['label' => 'Maintenance', 'value' => 'MAINTENANCE'],
                                ['label' => 'Quality Issue', 'value' => 'QUALITY'],
                                ['label' => 'Other', 'value' => 'OTHER']
                            ]
                        ],
                        'validate' => ['required' => true],
                        'description' => 'Select the reason for transfer'
                    ]
                ]
            ],
            'user_group_id' => $userGroup->id,
            'created_by' => $superAdmin->id,
            'is_active' => true,
        ];

        // Create the forms
        $formData = [$inventoryIssueForm, $inventoryReceiptForm, $stockTransferForm];
        
        foreach ($formData as $data) {
            $existingForm = Form::where('title', $data['title'])->first();
            if (!$existingForm) {
                $form = Form::create($data);
                $forms[] = $form;
                $this->command->info("Created test form: {$data['title']}");
            } else {
                $forms[] = $existingForm;
                $this->command->info("Test form already exists: {$data['title']}");
            }
        }

        return collect($forms);
    }

    /**
     * Get integration settings data
     */
    private function getIntegrationSettingsData($forms, $endpointConfigurations, $superAdmin)
    {
        $settings = [];

        // Find specific forms and endpoints for mapping
        $issueForm = $forms->firstWhere('title', 'Inventory Issue Form');
        $receiptForm = $forms->firstWhere('title', 'Inventory Receipt Form');
        $transferForm = $forms->firstWhere('title', 'Stock Transfer Form');

        // CSI Misc Issue Integration
        $csiIssueEndpoint = $endpointConfigurations->where('erp_selection', 'CSI')
            ->where('process_selection', 'Misc Issue')->first();
        
        if ($issueForm && $csiIssueEndpoint) {
            $settings[] = [
                'name' => 'CSI Inventory Issue Integration',
                'form_id' => $issueForm->id,
                'endpoint_configuration_id' => $csiIssueEndpoint->id,
                'field_mappings' => [
                    'item_code' => 'item_code',
                    'quantity' => 'quantity',
                    'location_code' => 'location_code',
                    'cost_center' => 'cost_center',
                    'reason' => 'reason'
                ],
                'description' => 'Integration between Inventory Issue Form and CSI Misc Issue endpoint',
                'is_active' => true,
                'created_by' => $superAdmin->id,
                'updated_by' => $superAdmin->id,
            ];
        }

        // SAP Misc Receipt Integration
        $sapReceiptEndpoint = $endpointConfigurations->where('erp_selection', 'SAP')
            ->where('process_selection', 'Misc Receipt')->first();
        
        if ($receiptForm && $sapReceiptEndpoint) {
            $settings[] = [
                'name' => 'SAP Inventory Receipt Integration',
                'form_id' => $receiptForm->id,
                'endpoint_configuration_id' => $sapReceiptEndpoint->id,
                'field_mappings' => [
                    'material_number' => 'material_number',
                    'quantity' => 'quantity',
                    'plant_code' => 'plant_code',
                    'storage_location' => 'storage_location',
                    'unit_cost' => 'unit_cost'
                ],
                'description' => 'Integration between Inventory Receipt Form and SAP Misc Receipt endpoint',
                'is_active' => true,
                'created_by' => $superAdmin->id,
                'updated_by' => $superAdmin->id,
            ];
        }

        // CSI Quantity Move Integration
        $csiMoveEndpoint = $endpointConfigurations->where('erp_selection', 'CSI')
            ->where('process_selection', 'Quantity Move')->first();
        
        if ($transferForm && $csiMoveEndpoint) {
            $settings[] = [
                'name' => 'CSI Stock Transfer Integration',
                'form_id' => $transferForm->id,
                'endpoint_configuration_id' => $csiMoveEndpoint->id,
                'field_mappings' => [
                    'item_code' => 'item_code',
                    'quantity' => 'quantity',
                    'from_location' => 'from_location',
                    'to_location' => 'to_location',
                    'transfer_reason' => 'reason'
                ],
                'description' => 'Integration between Stock Transfer Form and CSI Quantity Move endpoint',
                'is_active' => true,
                'created_by' => $superAdmin->id,
                'updated_by' => $superAdmin->id,
            ];
        }

        return $settings;
    }
}
