/*
  IR_Black style (c) <PERSON><PERSON> <<EMAIL>>
*/

.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  background: #000;
  color: #f8f8f8;
  -webkit-text-size-adjust: none;
}

.hljs-shebang,
.hljs-comment,
.hljs-template_comment,
.hljs-javadoc {
  color: #7c7c7c;
}

.hljs-keyword,
.hljs-tag,
.tex .hljs-command,
.hljs-request,
.hljs-status,
.clojure .hljs-attribute {
  color: #96cbfe;
}

.hljs-sub .hljs-keyword,
.method,
.hljs-list .hljs-title,
.nginx .hljs-title {
  color: #ffffb6;
}

.hljs-string,
.hljs-tag .hljs-value,
.hljs-cdata,
.hljs-filter .hljs-argument,
.hljs-attr_selector,
.apache .hljs-cbracket,
.hljs-date,
.coffeescript .hljs-attribute {
  color: #a8ff60;
}

.hljs-subst {
  color: #daefa3;
}

.hljs-regexp {
  color: #e9c062;
}

.hljs-title,
.hljs-sub .hljs-identifier,
.hljs-pi,
.hljs-decorator,
.tex .hljs-special,
.hljs-type,
.hljs-constant,
.smalltalk .hljs-class,
.hljs-javadoctag,
.hljs-yardoctag,
.hljs-phpdoc,
.hljs-dartdoc,
.nginx .hljs-built_in {
  color: #ffffb6;
}

.hljs-symbol,
.ruby .hljs-symbol .hljs-string,
.hljs-number,
.hljs-variable,
.vbscript,
.hljs-literal {
  color: #c6c5fe;
}

.css .hljs-tag {
  color: #96cbfe;
}

.css .hljs-rules .hljs-property,
.css .hljs-id {
  color: #ffffb6;
}

.css .hljs-class {
  color: #fff;
}

.hljs-hexcolor {
  color: #c6c5fe;
}

.hljs-number {
  color:#ff73fd;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.7;
}
