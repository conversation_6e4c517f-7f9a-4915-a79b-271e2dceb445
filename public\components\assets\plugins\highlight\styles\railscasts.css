/*

Railscasts-like style (c) Visoft, Inc. (<PERSON>)

*/

.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  background: #232323;
  color: #e6e1dc;
  -webkit-text-size-adjust: none;
}

.hljs-comment,
.hljs-template_comment,
.hljs-javadoc,
.hljs-shebang {
  color: #bc9458;
  font-style: italic;
}

.hljs-keyword,
.ruby .hljs-function .hljs-keyword,
.hljs-request,
.hljs-status,
.nginx .hljs-title,
.method,
.hljs-list .hljs-title {
  color: #c26230;
}

.hljs-string,
.hljs-number,
.hljs-regexp,
.hljs-tag .hljs-value,
.hljs-cdata,
.hljs-filter .hljs-argument,
.hljs-attr_selector,
.apache .hljs-cbracket,
.hljs-date,
.tex .hljs-command,
.markdown .hljs-link_label {
  color: #a5c261;
}

.hljs-subst {
  color: #519f50;
}

.hljs-tag,
.hljs-tag .hljs-keyword,
.hljs-tag .hljs-title,
.hljs-doctype,
.hljs-sub .hljs-identifier,
.hljs-pi,
.input_number {
  color: #e8bf6a;
}

.hljs-identifier {
  color: #d0d0ff;
}

.hljs-class .hljs-title,
.hljs-type,
.smalltalk .hljs-class,
.hljs-javadoctag,
.hljs-yardoctag,
.hljs-phpdoc,
.hljs-dartdoc {
  text-decoration: none;
}

.hljs-constant {
  color: #da4939;
}


.hljs-symbol,
.hljs-built_in,
.ruby .hljs-symbol .hljs-string,
.ruby .hljs-symbol .hljs-identifier,
.markdown .hljs-link_url,
.hljs-attribute {
  color: #6d9cbe;
}

.markdown .hljs-link_url {
  text-decoration: underline;
}



.hljs-params,
.hljs-variable,
.clojure .hljs-attribute {
  color: #d0d0ff;
}

.css .hljs-tag,
.hljs-rules .hljs-property,
.hljs-pseudo,
.tex .hljs-special {
  color: #cda869;
}

.css .hljs-class {
  color: #9b703f;
}

.hljs-rules .hljs-keyword {
  color: #c5af75;
}

.hljs-rules .hljs-value {
  color: #cf6a4c;
}

.css .hljs-id {
  color: #8b98ab;
}

.hljs-annotation,
.apache .hljs-sqbracket,
.nginx .hljs-built_in {
  color: #9b859d;
}

.hljs-preprocessor,
.hljs-preprocessor *,
.hljs-pragma {
  color: #8996a8 !important;
}

.hljs-hexcolor,
.css .hljs-value .hljs-number {
  color: #a5c261;
}

.hljs-title,
.hljs-decorator,
.css .hljs-function {
  color: #ffc66d;
}

.diff .hljs-header,
.hljs-chunk {
  background-color: #2f33ab;
  color: #e6e1dc;
  display: inline-block;
  width: 100%;
}

.diff .hljs-change {
  background-color: #4a410d;
  color: #f8f8f8;
  display: inline-block;
  width: 100%;
}

.hljs-addition {
  background-color: #144212;
  color: #e6e1dc;
  display: inline-block;
  width: 100%;
}

.hljs-deletion {
  background-color: #600;
  color: #e6e1dc;
  display: inline-block;
  width: 100%;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.7;
}
