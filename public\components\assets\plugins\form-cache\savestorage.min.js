/**
 * jQuery saveStorage - 23.06.2019
 * Version: 2.0.0
 * Website: https://github.com/sarkhanraj<PERSON>ov/saveStorage
 * Author: <PERSON><PERSON><PERSON>
 **/
!function(e){e.fn.saveStorage=function(t){"use strict";if("undefined"!=typeof Storage){var n=e(this),a=e(this).attr("id")+"_saveStorage",r=e.extend({},{exclude:[]},t),i=function(){var t="";return e.each(r.exclude,function(e,n){t+="input[type="+n+"],"}),t};n.find(":input").bind("change keyup",function(){var e=n.serializeArray();localStorage.setItem(a,JSON.stringify(e))});n.submit(function(){localStorage.removeItem(a)}),function(){if(null!==localStorage.getItem(a))for(var e=JSON.parse(localStorage.getItem(a)),t=n.find("input[type=radio]"),r=n.find("input[type=checkbox]"),o=0;o<e.length;o++){n.find(":input[name="+e[o].name+"]").not(i()+"input[type=radio], input[type=checkbox]").val(e[o].value);for(var u=0;u<t.length;u++)t[u].getAttribute("name")===e[o].name&&t[u].getAttribute("value")===e[o].value&&(t[u].checked=!0);for(var c=0;c<r.length;c++)r[c].getAttribute("name")===e[o].name&&r[c].getAttribute("value")===e[o].value&&(r[c].checked=!0)}}()}else console.error("Sorry! No web storage support.")}}(jQuery);
