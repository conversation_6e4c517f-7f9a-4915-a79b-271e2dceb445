<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Form extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'content',
        'user_group_id',
        'created_by',
        'is_active'
    ];

    protected $casts = [
        'content' => 'json',
        'is_active' => 'boolean',
    ];

    public function userGroup()
    {
        return $this->belongsTo(UserGroup::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForUserGroup($query, $userGroupId)
    {
        return $query->where('user_group_id', $userGroupId);
    }

    public function scopeAccessibleByUser($query, User $user)
    {
        if ($user->isAdmin() || $user->isSuperAdmin()) {
            return $query;
        }
        
        return $query->where('user_group_id', $user->user_group_id);
    }
}
