@extends('layouts.admin')
@section('pageTitle', 'User Group Details')

@section('content')
<div class="card card-default">
    <div class="card-header separator">
        <div class="card-title mainheading">
            <h4>User Group Details</h4>
        </div>
        <div class="card-controls">
            <ul>
                <li>
                    <a class="btn btn-secondary" href="{{ route('admin.usergroups.index') }}">
                        Back to List
                    </a>
                </li>
                <li>
                    <a class="btn btn-info" href="{{ route('admin.usergroups.edit', $usergroup->id) }}">
                        Edit
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <div class="card-block">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">Name</th>
                        <td>{{ $usergroup->name }}</td>
                    </tr>
                    <tr>
                        <th>Description</th>
                        <td>{{ $usergroup->description ?? 'N/A' }}</td>
                    </tr>
                    <tr>
                        <th>Status</th>
                        <td>
                            @if($usergroup->is_active)
                                <span class="badge badge-success">Active</span>
                            @else
                                <span class="badge badge-danger">Inactive</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>Users Count</th>
                        <td>{{ $usergroup->users->count() }}</td>
                    </tr>
                    <tr>
                        <th>Created At</th>
                        <td>{{ $usergroup->created_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                    <tr>
                        <th>Updated At</th>
                        <td>{{ $usergroup->updated_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                </table>
            </div>
        </div>

        @if($usergroup->users->count() > 0)
        <div class="mt-4">
            <h5>Users in this Group</h5>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Created At</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($usergroup->users as $user)
                        <tr>
                            <td>{{ $user->name }}</td>
                            <td>{{ $user->email }}</td>
                            <td>{{ $user->created_at->format('Y-m-d H:i:s') }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
