<?php $__env->startSection('pageTitle', 'Endpoint Configurations'); ?>

<?php $__env->startSection('content'); ?>
<div class="card card-default">
    <div class="card-header">
        <div class="row">
            <div class="col-md-6">
                <h4><?php echo e(trans('cruds.endpointConfiguration.title')); ?></h4>
            </div>
            <div class="col-md-6 text-right">
                <a class="btn btn-success" href="<?php echo e(route('admin.endpoint-configurations.create')); ?>">
                    <i class="fa fa-plus"></i> <?php echo e(trans('global.add')); ?> <?php echo e(trans('cruds.endpointConfiguration.title_singular')); ?>

                </a>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-striped table-hover datatable datatable-EndpointConfiguration">
                <thead>
                    <tr>
                        <th width="10">
                            
                        </th>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.name')); ?>

                        </th>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.url')); ?>

                        </th>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.erp_selection')); ?>

                        </th>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.process_selection')); ?>

                        </th>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.endpoint_type')); ?>

                        </th>
                        <th>
                            <?php echo e(trans('cruds.endpointConfiguration.fields.is_active')); ?>

                        </th>
                        <th>
                            <?php echo e(trans('global.actions')); ?>

                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $endpointConfigurations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $endpointConfiguration): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr data-entry-id="<?php echo e($endpointConfiguration->id); ?>">
                            <td>
                                
                            </td>
                            <td>
                                <?php echo e($endpointConfiguration->name ?? ''); ?>

                            </td>
                            <td>
                                <span title="<?php echo e($endpointConfiguration->url); ?>">
                                    <?php echo e(Str::limit($endpointConfiguration->url, 50)); ?>

                                </span>
                            </td>
                            <td>
                                <?php echo e($endpointConfiguration->erp_selection ?? ''); ?>

                            </td>
                            <td>
                                <?php echo e($endpointConfiguration->process_selection ?? ''); ?>

                            </td>
                            <td>
                                <?php echo e($endpointConfiguration->endpoint_type ?? ''); ?>

                            </td>
                            <td>
                                <span class="badge badge-<?php echo e($endpointConfiguration->is_active ? 'success' : 'danger'); ?>">
                                    <?php echo e($endpointConfiguration->is_active ? 'Active' : 'Inactive'); ?>

                                </span>
                            </td>
                            <td>
                                <?php echo $__env->make('partials.datatablesActions', [
                                    'crudRoutePart' => 'endpoint-configurations',
                                    'editGate' => 'endpoint_configuration_edit',
                                    'deleteGate' => 'endpoint_configuration_delete'
                                ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<?php echo \Illuminate\View\Factory::parentPlaceholder('scripts'); ?>
<script>
    $(function () {
        let dtButtons = $.extend(true, [], $.fn.dataTable.defaults.buttons)

        $.extend(true, $.fn.dataTable.defaults, {
            orderCellsTop: true,
            order: [[ 1, 'desc' ]],
            pageLength: 25,
        });
        let table = $('.datatable-EndpointConfiguration:not(.ajaxTable)').DataTable({ buttons: dtButtons })
        $('a[data-toggle="tab"]').on('shown.bs.tab click', function(e){
            $($.fn.dataTable.tables(true)).DataTable()
                .columns.adjust();
        });
        
    })
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Git Data Capture\application\resources\views/admin/endpoint-configurations/index.blade.php ENDPATH**/ ?>