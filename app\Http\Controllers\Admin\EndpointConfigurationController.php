<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreEndpointConfigurationRequest;
use App\Http\Requests\UpdateEndpointConfigurationRequest;
use App\Models\EndpointConfiguration;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EndpointConfigurationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $endpointConfigurations = EndpointConfiguration::with(['createdBy', 'updatedBy'])
            ->orderBy('created_at', 'desc')
            ->get();

        return view('admin.endpoint-configurations.index', compact('endpointConfigurations'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $erpOptions = EndpointConfiguration::getErpOptions();
        $processOptions = EndpointConfiguration::getProcessOptions();
        $endpointTypeOptions = EndpointConfiguration::getEndpointTypeOptions();

        return view('admin.endpoint-configurations.create', compact('erpOptions', 'processOptions', 'endpointTypeOptions'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreEndpointConfigurationRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreEndpointConfigurationRequest $request)
    {
        $data = $request->validated();

        // Handle JSON data
        if (isset($data['body_data_field']) && is_string($data['body_data_field'])) {
            try {
                $data['body_data_field'] = json_decode($data['body_data_field'], true);
            } catch (\Exception $e) {
                $data['body_data_field'] = null;
            }
        }

        // Set audit fields
        $data['created_by'] = auth()->id();
        $data['updated_by'] = auth()->id();

        $endpointConfiguration = EndpointConfiguration::create($data);

        return redirect()->route('admin.endpoint-configurations.index')
            ->with('message', 'Endpoint Configuration created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\EndpointConfiguration  $endpointConfiguration
     * @return \Illuminate\Http\Response
     */
    public function show(EndpointConfiguration $endpointConfiguration)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $endpointConfiguration->load(['createdBy', 'updatedBy']);

        return view('admin.endpoint-configurations.show', compact('endpointConfiguration'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\EndpointConfiguration  $endpointConfiguration
     * @return \Illuminate\Http\Response
     */
    public function edit(EndpointConfiguration $endpointConfiguration)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $erpOptions = EndpointConfiguration::getErpOptions();
        $processOptions = EndpointConfiguration::getProcessOptions();
        $endpointTypeOptions = EndpointConfiguration::getEndpointTypeOptions();

        return view('admin.endpoint-configurations.edit', compact('endpointConfiguration', 'erpOptions', 'processOptions', 'endpointTypeOptions'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateEndpointConfigurationRequest  $request
     * @param  \App\Models\EndpointConfiguration  $endpointConfiguration
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateEndpointConfigurationRequest $request, EndpointConfiguration $endpointConfiguration)
    {
        $data = $request->validated();

        // Handle JSON data
        if (isset($data['body_data_field']) && is_string($data['body_data_field'])) {
            try {
                $data['body_data_field'] = json_decode($data['body_data_field'], true);
            } catch (\Exception $e) {
                $data['body_data_field'] = null;
            }
        }

        // Set audit field for update
        $data['updated_by'] = auth()->id();

        $endpointConfiguration->update($data);

        return redirect()->route('admin.endpoint-configurations.index')
            ->with('message', 'Endpoint Configuration updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EndpointConfiguration  $endpointConfiguration
     * @return \Illuminate\Http\Response
     */
    public function destroy(EndpointConfiguration $endpointConfiguration)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $endpointConfiguration->delete();

        return redirect()->route('admin.endpoint-configurations.index')
            ->with('message', 'Endpoint Configuration deleted successfully.');
    }
}
