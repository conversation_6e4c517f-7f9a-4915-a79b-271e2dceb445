<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreEndpointConfigurationRequest;
use App\Http\Requests\UpdateEndpointConfigurationRequest;
use App\Models\EndpointConfiguration;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EndpointConfigurationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $endpointConfigurations = EndpointConfiguration::orderBy('created_at', 'desc')->get();

        return view('admin.endpoint-configurations.index', compact('endpointConfigurations'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $erpOptions = EndpointConfiguration::getErpOptions();
        $processOptions = EndpointConfiguration::getProcessOptions();
        $endpointTypeOptions = EndpointConfiguration::getEndpointTypeOptions();

        return view('admin.endpoint-configurations.create', compact('erpOptions', 'processOptions', 'endpointTypeOptions'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreEndpointConfigurationRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreEndpointConfigurationRequest $request)
    {
        $endpointConfiguration = EndpointConfiguration::create($request->validated());

        return redirect()->route('admin.endpoint-configurations.index')
            ->with('message', 'Endpoint Configuration created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\EndpointConfiguration  $endpointConfiguration
     * @return \Illuminate\Http\Response
     */
    public function show(EndpointConfiguration $endpointConfiguration)
    {
        abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.endpoint-configurations.show', compact('endpointConfiguration'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\EndpointConfiguration  $endpointConfiguration
     * @return \Illuminate\Http\Response
     */
    public function edit(EndpointConfiguration $endpointConfiguration)
    {
        abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $erpOptions = EndpointConfiguration::getErpOptions();
        $processOptions = EndpointConfiguration::getProcessOptions();
        $endpointTypeOptions = EndpointConfiguration::getEndpointTypeOptions();

        return view('admin.endpoint-configurations.edit', compact('endpointConfiguration', 'erpOptions', 'processOptions', 'endpointTypeOptions'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateEndpointConfigurationRequest  $request
     * @param  \App\Models\EndpointConfiguration  $endpointConfiguration
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateEndpointConfigurationRequest $request, EndpointConfiguration $endpointConfiguration)
    {
        $endpointConfiguration->update($request->validated());

        return redirect()->route('admin.endpoint-configurations.index')
            ->with('message', 'Endpoint Configuration updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EndpointConfiguration  $endpointConfiguration
     * @return \Illuminate\Http\Response
     */
    public function destroy(EndpointConfiguration $endpointConfiguration)
    {
        abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $endpointConfiguration->delete();

        return redirect()->route('admin.endpoint-configurations.index')
            ->with('message', 'Endpoint Configuration deleted successfully.');
    }
}
