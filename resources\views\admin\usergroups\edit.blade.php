@extends('layouts.admin')
@section('pageTitle', 'Edit User Group')

@section('content')
<div class="card card-default">
    <div class="card-header separator">
        <div class="card-title mainheading">
            <h4>Edit User Group</h4>
        </div>
        <div class="card-controls">
            <ul>
                <li>
                    <a class="btn btn-secondary" href="{{ route('admin.usergroups.index') }}">
                        Back to List
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <div class="card-block">
        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form method="POST" action="{{ route('admin.usergroups.update', $usergroup->id) }}">
            @csrf
            @method('PUT')
            
            <div class="form-group row">
                <label for="name" class="col-md-3 col-form-label">Name <span class="text-danger">*</span></label>
                <div class="col-md-9">
                    <input type="text" class="form-control" id="name" name="name" value="{{ old('name', $usergroup->name) }}" required>
                </div>
            </div>

            <div class="form-group row">
                <label for="description" class="col-md-3 col-form-label">Description</label>
                <div class="col-md-9">
                    <textarea class="form-control" id="description" name="description" rows="3">{{ old('description', $usergroup->description) }}</textarea>
                </div>
            </div>

            <div class="form-group row">
                <label for="is_active" class="col-md-3 col-form-label">Status</label>
                <div class="col-md-9">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" {{ old('is_active', $usergroup->is_active) ? 'checked' : '' }}>
                        <label class="form-check-label" for="is_active">
                            Active
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <div class="col-md-9 offset-md-3">
                    <button type="submit" class="btn btn-primary">Update User Group</button>
                    <a href="{{ route('admin.usergroups.index') }}" class="btn btn-secondary">Cancel</a>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection
