<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
    <title>Scroller example - Client-side data source (50,000 rows)</title>
    <link rel="stylesheet" type="text/css" href="../../../media/css/jquery.dataTables.css">
    <link rel="stylesheet" type="text/css" href="../css/dataTables.scroller.css">
    <link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
    <link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
    <style type="text/css" class="init">
    </style>
    <script type="text/javascript" language="javascript" src="../../../media/js/jquery.js"></script>
    <script type="text/javascript" language="javascript" src="../../../media/js/jquery.dataTables.js"></script>
    <script type="text/javascript" language="javascript" src="../js/dataTables.scroller.js"></script>
    <script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
    <script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>
    <script type="text/javascript" language="javascript" class="init">
    $(document).ready(function()
    {
      var data = [];
      for (var i = 0; i < 50000; i++)
      {
        data.push([i, i, i, i, i]);
      }
      var oTable = $('#example').dataTable(
      {
        data: data,
        deferRender: true,
        dom: "frtiS",
        scrollY: 200,
        scrollCollapse: true
      });
    });
    </script>
  </head>
  <body class="dt-example">
    <div class="container">
      <section>
        <h1>Scroller example <span>Client-side data source (50,000 rows)</span></h1>
        <div class="info">
          <p>This example is completely artificial in that the data generated is created on the client-side by just looping around a Javascript array and then passing that to DataTables. However, it does show quite nicely that DataTables and Scroller can cope with large amounts of data on the client-side quite nicely. Typically data such as this would be Ajax sourced and server-side processing should be considered.
          </p>
          <p>Please be aware that the performance of this page will depend on your browser as the array of data is generated - for example IE6 will crawl!</p>
        </div>
        <table id="example" class="display" cellspacing="0" width="100%">
          <thead>
            <tr>
              <th>ID</th>
              <th>First name</th>
              <th>Last name</th>
              <th>ZIP / Post code</th>
              <th>Country</th>
            </tr>
          </thead>
        </table>
        <ul class="tabs">
          <li class="active">Javascript</li>
          <li>HTML</li>
          <li>CSS</li>
          <li>Ajax</li>
          <li>Server-side script</li>
        </ul>
        <div class="tabs">
          <div class="js">
            <p>The Javascript shown below is used to initialise the table shown in this example:
            </p><code class="multiline brush: js;">$(document).ready(function() {
		var data = [];
		for ( var i=0 ; i&lt;50000 ; i++ ) {
			data.push( [ i, i, i, i, i ] );
		}
		
		var oTable = $('#example').dataTable( {
			data:           data,
			deferRender:    true,
			dom:            &quot;frtiS&quot;,
			scrollY:        200,
			scrollCollapse: true
		} );
	} );</code>
            <p>In addition to the above code, the following Javascript library files are loaded for use in this example:
            </p>
            <ul>
              <li><a href="../../../media/js/jquery.js">../../../media/js/jquery.js</a></li>
              <li><a href="../../../media/js/jquery.dataTables.js">../../../media/js/jquery.dataTables.js</a></li>
              <li><a href="../js/dataTables.scroller.js">../js/dataTables.scroller.js</a></li>
            </ul>
          </div>
          <div class="table">
            <p>The HTML shown below is the raw HTML table element, before it has been enhanced by DataTables:
            </p>
          </div>
          <div class="css">
            <div>
              <p>This example uses a little bit of additional CSS beyond what is loaded from the library files (below), in order to correctly display the table. The additional CSS used is shown below:
              </p><code class="multiline brush: js;"></code>
            </div>
            <p>The following CSS library files are loaded for use in this example to provide the styling of the table:
            </p>
            <ul>
              <li><a href="../../../media/css/jquery.dataTables.css">../../../media/css/jquery.dataTables.css</a></li>
              <li><a href="../css/dataTables.scroller.css">../css/dataTables.scroller.css</a></li>
            </ul>
          </div>
          <div class="ajax">
            <p>This table loads data by Ajax. The latest data that has been loaded is shown below. This data will update automatically as any additional data is loaded.</p>
          </div>
          <div class="php">
            <p>The script used to perform the server-side processing for this table is shown below. Please note that this is just an example script using PHP. Server-side processing scripts can be written in any language, using <a href="//datatables.net/manual/server-side">the protocol described in the
					DataTables documentation</a>.</p>
          </div>
        </div>
      </section>
    </div>
    <section>
      <div class="footer">
        <div class="gradient"></div>
        <div class="liner">
          <h2>Other examples</h2>
          <div class="toc">
            <div class="toc-group">
              <h3><a href="./index.html">Examples</a></h3>
              <ul class="toc active">
                <li><a href="./simple.html">Basic initialisation</a></li>
                <li><a href="./state_saving.html">State saving</a></li>
                <li class="active"><a href="./large_js_source.html">Client-side data source (50,000
							rows)</a></li>
                <li><a href="./server-side_processing.html">Server-side processing (5,000,000
							rows)</a></li>
                <li><a href="./api_scrolling.html">API</a></li>
              </ul>
            </div>
          </div>
          <div class="epilogue">
            <p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.
              <br> Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and
              <a href="http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of DataTables.
            </p>
            <p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2014
              <br> DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
          </div>
        </div>
      </div>
    </section>
  </body>
</html>