@extends('layouts.admin')
@section('pageTitle', 'Dashboard')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap/dist/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdn.form.io/js/formio.full.min.css">
<link rel="stylesheet" href="https://cdn.form.io/js/formio.form.min.css">
<style>
    .builder {
        width: auto !important;
        right: 0px !important;
    }
</style>
@section('styles')
    @parent

    <style>
        .builder {
            width: auto;
            right: 0px;
        }
    </style>
@endsection
@section('content')
    <!--<div class="content">-->

    <div class="card card-default">
        <div class="card-header">
            Dashboard
        </div>
        <div class="card-body">
            <div id="builder"></div>
            <div id="formio"></div>

        </div>

    </div>

    <!--</div>-->
@endsection
@section('scripts')
    @parent
    <script src="https://cdn.form.io/js/formio.form.min.js"></script>
    <script src="https://cdn.form.io/js/formio.full.min.js"></script>


    <script>
        var schema = {
            builder: {
                basic: false,
                advanced: false,
                premium: false,
                data: false,
                customBasic: {
                    title: 'Basic Components',
                    default: true,
                    weight: 0,
                    components: {
                        textfield: true,
                        textarea: true,
                        email: true,
                        phoneNumber: true
                    }
                },
                // custom: {
                //     title: 'User Fields',
                //     weight: 10,
                //     components: {
                //         firstName: {
                //             title: 'First Name',
                //             key: 'firstName',
                //             icon: 'terminal',
                //             schema: {
                //                 label: 'First Name',
                //                 type: 'textfield',
                //                 key: 'firstName',
                //                 input: true
                //             }
                //         },
                //         lastName: {
                //             title: 'Last Name',
                //             key: 'lastName',
                //             icon: 'terminal',
                //             schema: {
                //                 label: 'Last Name',
                //                 type: 'textfield',
                //                 key: 'lastName',
                //                 input: true
                //             }
                //         },
                //         email: {
                //             title: 'Email',
                //             key: 'email',
                //             icon: 'at',
                //             schema: {
                //                 label: 'Email',
                //                 type: 'email',
                //                 key: 'email',
                //                 input: true
                //             }
                //         },
                //         phoneNumber: {
                //             title: 'Mobile Phone',
                //             key: 'mobilePhone',
                //             icon: 'phone-square',
                //             schema: {
                //                 label: 'Mobile Phone',
                //                 type: 'phoneNumber',
                //                 key: 'mobilePhone',
                //                 input: true
                //             }
                //         }
                //     }
                // },
                layout: {
                    components: {
                        table: false
                    }
                }
            },
            editForm: {
                textfield: [{
                    key: 'api',
                    ignore: true
                }]
            }
        };

        var formB = Formio.builder(document.getElementById('builder'), {}, schema).then(function(builder) {

            builder.on('saveComponent', function() {
                // console.log(builder.schema);
            });
            builder.on("change", function(e) {
                console.log(builder.schema);
            });
        });

        Formio.createForm(document.getElementById('formio'), {
            components: [{
                    type: 'textfield',
                    key: 'firstName',
                    label: 'First Name',
                    case: "uppercase",
                    truncateMultipleSpaces: true,
                },
                {
                    type: 'textfield',
                    key: 'lastName',
                    label: 'Last Name'
                },
                {
                    type: 'email',
                    key: 'email',
                    label: 'Email'
                },
                {
                    type: 'button',
                    key: 'submit',
                    label: 'Submit'
                }
            ]
        }).then(function(form) {
            // form.on('change', function(submission) {
            //     console.log(submission);
            // });
            form.on('submit', function(submission) {
                console.log(submission.data);
            });
        });
    </script>
    {{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.5.0/Chart.min.js"></script> --}}
@endsection
