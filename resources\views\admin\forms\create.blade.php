@extends('layouts.admin')
@section('pageTitle', 'Create Form')
{{-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap/dist/css/bootstrap.min.css"> --}}
{{-- <link rel="stylesheet" href="https://cdn.form.io/js/formio.full.min.css"> --}}
<link rel="stylesheet" href="https://cdn.form.io/js/formio.form.min.css">
@section('styles')
    @parent

    <style>
        .builder {
            width: auto !important;
            right: 0px !important;
            min-height: 500px;
        }

        .form-builder-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
@endsection

@section('content')

    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>Create Form</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <a class="btn btn-secondary" href="{{ route('admin.forms.index') }}">
                            Back to List
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="card-block">
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('admin.forms.store') }}" id="formData">
                @csrf

                <div class="form-group row">
                    <label for="title" class="col-md-3 col-form-label">Form Title <span
                            class="text-danger">*</span></label>
                    <div class="col-md-9">
                        <input type="text" class="form-control" id="title" name="title" value="{{ old('title') }}"
                            required>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="user_group_id" class="col-md-3 col-form-label">User Group</label>
                    <div class="col-md-9">
                        <select class="form-control" id="user_group_id" name="user_group_id">
                            <option value="">All Groups</option>
                            @foreach ($userGroups as $id => $name)
                                <option value="{{ $id }}" {{ old('user_group_id') == $id ? 'selected' : '' }}>
                                    {{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="is_active" class="col-md-3 col-form-label">Status</label>
                    <div class="col-md-9">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1"
                                {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                Active
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    {{-- <label class="col-md-3 col-form-label">Form Builder</label> --}}
                    <div class="col-md-12">
                        <div class="form-builder-container">
                            <div id="builder"></div>
                        </div>
                    </div>
                </div>

                <input type="hidden" name="content" id="content">

                <div class="form-group row">
                    <div class="col-md-9 offset-md-3">
                        <button type="submit" class="btn btn-primary">Create Form</button>
                        <a href="{{ route('admin.forms.index') }}" class="btn btn-secondary">Cancel</a>
                    </div>
                </div>
            </form>

        </div>
    </div>
@endsection

@section('scripts')
    @parent
    <script src="https://cdn.form.io/js/formio.form.min.js"></script>
    {{-- <script src="https://cdn.form.io/js/formio.full.min.js"></script> --}}

    <script>
        var schema = {
            builder: {
                basic: false,
                advanced: false,
                premium: false,
                data: false,
                customBasic: {
                    title: 'Form Elements',
                    default: true,
                    weight: 0,
                    components: {
                        textfield: true,
                        textarea: true,
                        email: false,
                        phoneNumber: false,
                        number: true,
                        checkbox: true,
                        selectboxes: false,
                        select: true,
                        radio: true,
                        button: false
                    }
                },
                layout: {
                    // title: 'Form s',
                    default: true,
                    weight: 1,
                    components: {
                        htmlelement: false,
                        well: false,
                        fieldset: false,
                        tabs: false,
                        panel: false,
                        content: false,
                        table: true,
                        columns: true,
                    }
                }
            },
            editForm: {
                textfield: [{
                    key: 'api',
                    ignore: true
                }]
            }
        };

        var builder;
        Formio.builder(document.getElementById('builder'), {}, schema).then(function(formBuilder) {
            builder = formBuilder;

            builder.on('change', function() {
                document.getElementById('content').value = JSON.stringify(builder.schema);
            });
        });

        // Form submission
        document.getElementById('formData').addEventListener('submit', function(e) {
            if (builder) {
                document.getElementById('content').value = JSON.stringify(builder.schema);
            }

            if (!document.getElementById('content').value || document.getElementById('content').value === '{}') {
                e.preventDefault();
                alert('Please design your form using the form builder.');
            }
        });
    </script>
@endsection
