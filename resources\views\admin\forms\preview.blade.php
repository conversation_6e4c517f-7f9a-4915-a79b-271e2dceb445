@extends('layouts.admin')
@section('pageTitle', 'Form Preview')

@section('styles')
@parent
<link rel="stylesheet" href="https://cdn.form.io/js/formio.full.min.css">
<link rel="stylesheet" href="https://cdn.form.io/js/formio.form.min.css">
<style>
    .form-preview-container {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 20px;
        margin-bottom: 20px;
        background-color: #fff;
    }
</style>
@endsection

@section('content')
<div class="card card-default">
    <div class="card-header separator">
        <div class="card-title mainheading">
            <h4>Form Preview: {{ $form->title }}</h4>
        </div>
        <div class="card-controls">
            <ul>
                <li>
                    <a class="btn btn-secondary" href="{{ route('admin.forms.show', $form->id) }}">
                        Back to Details
                    </a>
                </li>
                @if(auth()->user()->isAdmin() || auth()->user()->isSuperAdmin())
                <li>
                    <a class="btn btn-info" href="{{ route('admin.forms.edit', $form->id) }}">
                        Edit Form
                    </a>
                </li>
                @endif
            </ul>
        </div>
    </div>

    <div class="card-block">
        <div class="alert alert-info">
            <i class="fa fa-info-circle"></i> This is a preview of the form. You can interact with it but submissions will not be saved.
        </div>
        
        <div class="form-preview-container">
            <div id="formPreview"></div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
@parent
<script src="https://cdn.form.io/js/formio.form.min.js"></script>
<script src="https://cdn.form.io/js/formio.full.min.js"></script>

<script>
var formDefinition = {!! json_encode($form->content) !!};

Formio.createForm(document.getElementById('formPreview'), formDefinition)
    .then(function(form) {
        // Handle form submission for preview
        form.on('submit', function(submission) {
            console.log('Form submission (preview mode):', submission.data);
            
            // Show submission data in an alert for preview
            var submissionData = JSON.stringify(submission.data, null, 2);
            alert('Form Submitted (Preview Mode)\n\nSubmission Data:\n' + submissionData);
            
            // Reset the form
            form.submission = { data: {} };
        });
        
        // Handle form change events
        form.on('change', function(changed) {
            console.log('Form changed:', changed);
        });
    })
    .catch(function(error) {
        console.error('Error rendering form:', error);
        document.getElementById('formPreview').innerHTML = 
            '<div class="alert alert-danger">Error loading form preview. Please check the form definition.</div>';
    });
</script>
@endsection
