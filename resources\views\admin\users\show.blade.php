@extends('layouts.admin')
@section('pageTitle', 'User Details')

@section('content')
<div class="card card-default">
    <div class="card-header separator">
        <div class="card-title mainheading">
            <h4>User Details</h4>
        </div>
        <div class="card-controls">
            <ul>
                <li>
                    <a class="btn btn-secondary" href="{{ route('admin.users.index') }}">
                        Back to List
                    </a>
                </li>
                <li>
                    <a class="btn btn-info" href="{{ route('admin.users.edit', $user->id) }}">
                        Edit
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <div class="card-block">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">Name</th>
                        <td>{{ $user->name }}</td>
                    </tr>
                    <tr>
                        <th>Email</th>
                        <td>{{ $user->email }}</td>
                    </tr>
                    <tr>
                        <th>Role</th>
                        <td>
                            @switch($user->type)
                                @case(1)
                                    <span class="badge badge-danger">Super Admin</span>
                                    @break
                                @case(2)
                                    <span class="badge badge-warning">Admin</span>
                                    @break
                                @case(3)
                                    <span class="badge badge-info">User</span>
                                    @break
                                @default
                                    <span class="badge badge-secondary">Unknown</span>
                            @endswitch
                        </td>
                    </tr>
                    <tr>
                        <th>User Group</th>
                        <td>
                            @if($user->userGroup)
                                <span class="badge badge-primary">{{ $user->userGroup->name }}</span>
                            @else
                                <span class="text-muted">No Group Assigned</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>Email Verified</th>
                        <td>
                            @if($user->email_verified_at)
                                <span class="badge badge-success">Verified</span>
                                <br><small>{{ $user->email_verified_at->format('Y-m-d H:i:s') }}</small>
                            @else
                                <span class="badge badge-warning">Not Verified</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>Created At</th>
                        <td>{{ $user->created_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                    <tr>
                        <th>Updated At</th>
                        <td>{{ $user->updated_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                @if($user->userGroup)
                <div class="card">
                    <div class="card-header">
                        <h5>User Group Information</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <th>Group Name:</th>
                                <td>{{ $user->userGroup->name }}</td>
                            </tr>
                            <tr>
                                <th>Description:</th>
                                <td>{{ $user->userGroup->description ?? 'N/A' }}</td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    @if($user->userGroup->is_active)
                                        <span class="badge badge-success">Active</span>
                                    @else
                                        <span class="badge badge-danger">Inactive</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>Total Members:</th>
                                <td>{{ $user->userGroup->users->count() }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
