
<!--
<?php if(isset($viewGate)): ?>
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check($viewGate)): ?>
<a class="btn btn-xs btn-block btn-primary" href="<?php echo e(route('admin.' . $crudRoutePart . '.show', $row->id)); ?>">
    <?php echo e(trans('global.view')); ?>

</a>
<?php endif; ?>
<?php endif; ?>-->
<?php if(isset($editGate)): ?>
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check($editGate)): ?>
<a class="btn btn-sm btn-info inline"  title="Edit" href="<?php echo e(route('admin.' . $crudRoutePart . '.edit', $row->id)); ?>">
    <!--    <?php echo e(trans('global.edit')); ?>-->
    <i class="fa fa-edit"></i>
</a>
<?php endif; ?>
<?php endif; ?>
<?php if(isset($deleteGate)): ?>
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check($deleteGate)): ?>
<form class="ml-2 inline deletetItemForm" action="<?php echo e(route('admin.' . $crudRoutePart . '.destroy', $row->id)); ?>" method="POST"  >
    <input type="hidden" name="_method" value="DELETE">
    <input type="hidden" name="_token" value="<?php echo e(csrf_token()); ?>">
    <input type="hidden" class="delete_confirm_custome_message" value="<?php echo e(isset($customeMessage) && !empty($customeMessage)?$customeMessage:''); ?>">
    <button type="button" title="Delete" class="btn btn-sm btn-danger deleteConfirmForm"><i class="fa fa-trash"></i></button>
    <!--<input type="submit" class="btn btn-block btn-xs btn-danger" value="<?php echo e(trans('global.delete')); ?>">-->
</form>
<?php endif; ?>
<?php endif; ?>
<?php if(isset($manualDeleteGate)): ?>
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check($manualDeleteGate)): ?>
<form class="ml-2 inline deletetItemForm" action="<?php echo e(route('admin.' . $crudRoutePart . '.destroy', $row->id)); ?>" method="POST"  >
    <input type="hidden" name="_method" value="DELETE">
    <input type="hidden" name="_token" value="<?php echo e(csrf_token()); ?>">
    <input type="hidden" class="delete_confirm_custome_message" value="<?php echo e(isset($customeMessage) && !empty($customeMessage)?$customeMessage:''); ?>">
    <button type="button" title="Delete" class="btn btn-sm btn-danger <?php echo e($btnClass); ?>"><i class="fa fa-trash"></i></button>
    <!--<input type="submit" class="btn btn-block btn-xs btn-danger" value="<?php echo e(trans('global.delete')); ?>">-->
</form>
<?php endif; ?>
<?php endif; ?>
<!--<div class="dropdown dropdown-default">
    <button aria-label="" class="btn dropdown-toggle" type="button" data-toggle="dropdown">
        Actions
    </button>
    <div class="dropdown-menu " >
<?php if(isset($editGate)): ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check($editGate)): ?>

        <a class="dropdown-item" href="<?php echo e(route('admin.' . $crudRoutePart . '.edit', $row->id)); ?>" ><i class="fa fa-edit"></i> <span class="bold">  <?php echo e(trans('global.edit')); ?></span>
        </a>
        <?php endif; ?>
<?php endif; ?>
<?php if(isset($deleteGate)): ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check($deleteGate)): ?>
        <a class="dropdown-item deleteItemAjax text-danger" href="javascript:void(0);"><i class="fa fa-trash"></i> <span class="bold"><?php echo e(trans('global.delete')); ?></span>
        </a>

        <form method="POST" class="deleteItemForm" action="<?php echo e(route('admin.' . $crudRoutePart . '.destroy', $row->id)); ?>"> 
            <?php echo csrf_field(); ?>
            <?php echo method_field('DELETE'); ?>
            <button class="btn btn-danger btn-cons" onclick="return confirm('Are you sure you want to delete this item?');" type="submit">Delete</button>
        </form>
        <?php endif; ?>
<?php endif; ?>

    </div>
</div>--><?php /**PATH D:\Git Data Capture\application\resources\views/partials/datatablesActions.blade.php ENDPATH**/ ?>