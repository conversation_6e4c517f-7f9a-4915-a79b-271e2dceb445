/*

vim-hybrid theme by w0ng (https://github.com/w0ng/vim-hybrid)

*/

/*background color*/
.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  background: #1d1f21;
  -webkit-text-size-adjust: none;
}

/*selection color*/
.hljs::selection,
.hljs span::selection {
	background: #373b41;
}
.hljs::-moz-selection,
.hljs span::-moz-selection {
	background: #373b41;
}

/*foreground color*/
.hljs,
.hljs-setting .hljs-value,
.hljs-expression .hljs-variable,
.hljs-expression .hljs-begin-block,
.hljs-expression .hljs-end-block,
.hljs-class .hljs-params,
.hljs-function .hljs-params,
.hljs-at_rule .hljs-preprocessor {
  color: #c5c8c6;
}

/*color: fg_yellow*/
.hljs-title,
.hljs-function .hljs-title,
.hljs-keyword .hljs-common,
.hljs-class .hljs-title,
.hljs-decorator,
.hljs-tag .hljs-title,
.hljs-header,
.hljs-sub,
.hljs-function {
  color: #f0c674;
}

/*color: fg_comment*/
.hljs-comment,
.hljs-javadoc,
.hljs-output .hljs-value,
.hljs-pi,
.hljs-shebang,
.hljs-template_comment,
.hljs-doctype {
  color: #707880;
}

/*color: fg_red*/
.hljs-number,
.hljs-symbol,
.hljs-literal,
.hljs-deletion,
.hljs-link_url,
.hljs-symbol .hljs-string,
.hljs-argument,
.hljs-hexcolor,
.hljs-input .hljs-prompt,
.hljs-char {
 color: #cc6666
}

/*color: fg_green*/
.hljs-string,
.hljs-special,
.hljs-javadoctag,
.hljs-addition,
.hljs-important,
.hljs-tag .hljs-value,
.hljs-at.rule .hljs-keyword,
.hljs-regexp,
.hljs-attr_selector {
  color: #b5bd68;
}

/*color: fg_purple*/
.hljs-variable,
.hljs-property,
.hljs-envar,
.hljs-code,
.hljs-expression,
.hljs-localvars,
.hljs-id,
.hljs-variable .hljs-filter,
.hljs-variable .hljs-filter .hljs-keyword,
.hljs-template_tag .hljs-filter .hljs-keyword {
 color: #b294bb;
}

/*color: fg_blue*/
.hljs-statement,
.hljs-label,
.hljs-keyword,
.hljs-xmlDocTag,
.hljs-function .hljs-keyword,
.hljs-chunk,
.hljs-cdata,
.hljs-link_label,
.hljs-bullet,
.hljs-class .hljs-keyword,
.hljs-smartquote,
.hljs-method,
.hljs-list .hljs-title,
.hljs-tag {
 color: #81a2be;
}

/*color: fg_aqua*/
.hljs-pseudo,
.hljs-exception,
.hljs-annotation,
.hljs-subst,
.hljs-change,
.hljs-cbracket,
.hljs-operator,
.hljs-horizontal_rule,
.hljs-preprocessor .hljs-keyword,
.hljs-typedef,
.hljs-template_tag,
.hljs-variable,
.hljs-variable .hljs-filter .hljs-argument,
.hljs-at_rule,
.hljs-at_rule .hljs-string,
.hljs-at_rule .hljs-keyword {
  color: #8abeb7;
}


/*color: fg_orange*/
.hljs-type,
.hljs-typename,
.hljs-inheritance .hljs-parent,
.hljs-constant,
.hljs-built_in,
.hljs-setting,
.hljs-structure,
.hljs-link_reference,
.hljs-attribute,
.hljs-blockquote,
.hljs-quoted,
.hljs-class,
.hljs-header {
  color: #de935f;
}

.hljs-emphasis
{
  font-style: italic;
}

.hljs-strong
{
  font-weight: bold;
}




