<nav class="page-sidebar <?php echo e(1 == 1 ? 'sp_admin' : ''); ?>" data-pages="sidebar">
    <!-- BEGIN SIDEBAR MENU TOP TRAY CONTENT-->

    <!-- END SIDEBAR MENU TOP TRAY CONTENT-->
    <!-- BEGIN SIDEBAR MENU HEADER-->
    <div class="sidebar-header">
        <a href="/">
            <h4 style="color:#fff;display: inline;font-weight: 600;"><?php echo e(env('APP_NAME')); ?></h4>
        </a>
        <!--<img src="/logo.png" alt="logo" class="brand" data-src="/logo.png" data-src-retina="/logo.png" width="78" height="22">-->
        <div class="sidebar-header-controls">
            <!--            <button type="button" class="btn btn-xs sidebar-slide-toggle btn-link m-l-20 hidden-md-down" data-pages-toggle="#appMenu"><i class="fa fa fa-angle-down fs-16"></i>
            </button>-->
            <button type="button" class="btn btn-link hidden-md-down" data-toggle-pin="sidebar"><i
                    class="fa fs-12"></i>
            </button>
        </div>
    </div>
    <!-- END SIDEBAR MENU HEADER-->
    <!-- START SIDEBAR MENU -->
    <div class="sidebar-menu">
        <!-- BEGIN SIDEBAR MENU ITEMS-->
        <ul class="menu-items">


            <li class="m-t-30 <?php echo e(request()->segment(1) == 'admin' && request()->segment(2) == '' ? 'active' : ''); ?>">
                <a href="/admin" class="">
                    <span class="title">Dashboard</span>
                    <!--<span class="details">12 New Updates</span>-->
                </a>
                <span class="icon-thumbnail">
                    <i class="fa fa-dashboard"></i>
                </span>
            </li>

            <hr style="margin: 5px;border-color: #cecece;">
            </hr>


            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('user_management_access')): ?>
                <?php
                $manageRequests = ['admin/permissions*', 'admin/roles*', 'admin/users*'];
                $managementClass = '';
                foreach ($manageRequests as $req) {
                    if (request()->is($req)) {
                        $managementClass = 'open active';
                    }
                }
                ?>
                <li class="sidebar-menu-bg1 <?php echo e($managementClass); ?>">

                    <a href="javascript:;"><span class="title"> <?php echo e(trans('cruds.userManagement.title')); ?></span>
                        <span class=" arrow open active"></span></a>
                    <span class="icon-thumbnail"><i class="fa fa-users"></i></span>

                    <ul class="sub-menu">
                        
                            
                        
                        
                            
                        
                        
                            <li class="<?php echo e(request()->is('admin/users') || request()->is('admin/users/*') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('admin.users.index')); ?>"><?php echo e(trans('cruds.user.title')); ?></a>
                                <span class="icon-thumbnail"><i class="fa fa-user-plus"></i></span>
                            </li>
                        
                         <li class="<?php echo e(request()->is('admin/usergroups') || request()->is('admin/usergroups/*') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('admin.usergroups.index')); ?>"><?php echo e(trans('cruds.usergroups.title')); ?></a>
                                <span class="icon-thumbnail"><i class="fa fa-users"></i></span>
                            </li>
                            <li class="<?php echo e(request()->is('admin/forms') || request()->is('admin/forms/*') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('admin.forms.index')); ?>">Forms</a>
                                <span class="icon-thumbnail"><i class="fa fa-wpforms"></i></span>
                            </li>


                    </ul>
                </li>
            <?php endif; ?>






            
            <?php if(auth()->check()): ?>
                <li class="m-t-30 <?php echo e(request()->is('admin/endpoint-configurations*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('admin.endpoint-configurations.index')); ?>" class="">
                        <span class="title"><?php echo e(trans('cruds.endpointConfiguration.title')); ?></span>
                    </a>
                    <span class="icon-thumbnail">
                        <i class="fa fa-cogs"></i>
                    </span>
                </li>
            <?php endif; ?>

            
            <?php if(auth()->check()): ?>
                <li class="m-t-30 <?php echo e(request()->is('admin/form-integration-settings*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('admin.form-integration-settings.index')); ?>" class="">
                        <span class="title">Form Integration Settings</span>
                    </a>
                    <span class="icon-thumbnail">
                        <i class="fa fa-link"></i>
                    </span>
                </li>
            <?php endif; ?>

        </ul>
        <div class="clearfix"></div>
    </div>

    <!-- END SIDEBAR MENU -->
</nav>
<?php /**PATH D:\Git Data Capture\application\resources\views/include/sidebar.blade.php ENDPATH**/ ?>