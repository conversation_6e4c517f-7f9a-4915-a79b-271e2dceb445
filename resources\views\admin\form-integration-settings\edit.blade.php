@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title">Edit Form Integration Setting</h3>
                        <a href="{{ route('admin.form-integration-settings.index') }}" class="btn btn-secondary">
                            <i class="fa fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>

                <form action="{{ route('admin.form-integration-settings.update', $formIntegrationSetting->id) }}" method="POST" id="integration-form">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="required">Integration Name</label>
                                    <input type="text" 
                                           class="form-control @error('name') is-invalid @enderror" 
                                           id="name" 
                                           name="name" 
                                           value="{{ old('name', $formIntegrationSetting->name) }}" 
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="is_active">Status</label>
                                    <select class="form-control @error('is_active') is-invalid @enderror" 
                                            id="is_active" 
                                            name="is_active">
                                        <option value="1" {{ old('is_active', $formIntegrationSetting->is_active) == '1' ? 'selected' : '' }}>Active</option>
                                        <option value="0" {{ old('is_active', $formIntegrationSetting->is_active) == '0' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                    @error('is_active')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" 
                                      name="description" 
                                      rows="3">{{ old('description', $formIntegrationSetting->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Form Selection -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="form_id" class="required">Select Form</label>
                                    <select class="form-control @error('form_id') is-invalid @enderror" 
                                            id="form_id" 
                                            name="form_id" 
                                            required>
                                        <option value="">-- Select Form --</option>
                                        @foreach($forms as $form)
                                            <option value="{{ $form->id }}" {{ old('form_id', $formIntegrationSetting->form_id) == $form->id ? 'selected' : '' }}>
                                                {{ $form->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('form_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Endpoint Configuration Filters -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5>Endpoint Configuration Selection</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="erp_filter">Filter by ERP System</label>
                                            <select class="form-control" id="erp_filter">
                                                <option value="">All ERP Systems</option>
                                                @foreach($erpOptions as $key => $value)
                                                    <option value="{{ $key }}">{{ $value }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="process_filter">Filter by Process Type</label>
                                            <select class="form-control" id="process_filter">
                                                <option value="">All Process Types</option>
                                                @foreach($processOptions as $key => $value)
                                                    <option value="{{ $key }}">{{ $value }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="endpoint_type_filter">Filter by Endpoint Type</label>
                                            <select class="form-control" id="endpoint_type_filter">
                                                <option value="">All Endpoint Types</option>
                                                @foreach($endpointTypeOptions as $key => $value)
                                                    <option value="{{ $key }}">{{ $value }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="endpoint_configuration_id" class="required">Select Endpoint Configuration</label>
                                    <select class="form-control @error('endpoint_configuration_id') is-invalid @enderror" 
                                            id="endpoint_configuration_id" 
                                            name="endpoint_configuration_id" 
                                            required>
                                        <option value="">-- Select Endpoint Configuration --</option>
                                        @foreach($endpointConfigurations as $endpoint)
                                            <option value="{{ $endpoint->id }}" 
                                                    data-erp="{{ $endpoint->erp_selection }}"
                                                    data-process="{{ $endpoint->process_selection }}"
                                                    data-type="{{ $endpoint->endpoint_type }}"
                                                    {{ old('endpoint_configuration_id', $formIntegrationSetting->endpoint_configuration_id) == $endpoint->id ? 'selected' : '' }}>
                                                {{ $endpoint->name }} ({{ $endpoint->erp_selection }} - {{ $endpoint->process_selection }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('endpoint_configuration_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Field Mapping Section -->
                        <div class="card mt-4" id="field-mapping-section" style="display: none;">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5>Field Mapping</h5>
                                    <button type="button" class="btn btn-sm btn-info" id="suggest-mappings-btn">
                                        <i class="fa fa-magic"></i> Suggest Mappings
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Form Fields</h6>
                                        <div id="form-fields-list" class="border p-3" style="min-height: 200px; max-height: 400px; overflow-y: auto;">
                                            <p class="text-muted">Select a form to see its fields</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Endpoint Fields</h6>
                                        <div id="endpoint-fields-list" class="border p-3" style="min-height: 200px; max-height: 400px; overflow-y: auto;">
                                            <p class="text-muted">Select an endpoint configuration to see its fields</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <h6>Field Mappings</h6>
                                    <div id="field-mappings-container">
                                        <p class="text-muted">Field mappings will appear here when you select both form and endpoint</p>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-success" id="add-mapping-btn" style="display: none;">
                                        <i class="fa fa-plus"></i> Add Mapping
                                    </button>
                                </div>

                                @error('field_mappings')
                                    <div class="alert alert-danger mt-3">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-save"></i> Update Integration Setting
                        </button>
                        <a href="{{ route('admin.form-integration-settings.index') }}" class="btn btn-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    let formFields = [];
    let endpointFields = [];
    let fieldMappings = @json(old('field_mappings', $formIntegrationSetting->field_mappings ?? []));

    // Initialize with existing data
    if ($('#form_id').val()) {
        loadFormFields($('#form_id').val());
    }
    if ($('#endpoint_configuration_id').val()) {
        loadEndpointFields($('#endpoint_configuration_id').val());
    }

    // Form selection change
    $('#form_id').change(function() {
        const formId = $(this).val();
        if (formId) {
            loadFormFields(formId);
        } else {
            clearFormFields();
        }
        updateFieldMappingSection();
    });

    // Endpoint configuration selection change
    $('#endpoint_configuration_id').change(function() {
        const endpointId = $(this).val();
        if (endpointId) {
            loadEndpointFields(endpointId);
        } else {
            clearEndpointFields();
        }
        updateFieldMappingSection();
    });

    // Filter functionality
    $('#erp_filter, #process_filter, #endpoint_type_filter').change(function() {
        filterEndpointOptions();
    });

    // Suggest mappings button
    $('#suggest-mappings-btn').click(function() {
        suggestFieldMappings();
    });

    // Add mapping button
    $('#add-mapping-btn').click(function() {
        addEmptyMapping();
    });

    // Load form fields via AJAX
    function loadFormFields(formId) {
        $.ajax({
            url: '{{ route("admin.form-integration-settings.get-form-fields") }}',
            method: 'GET',
            data: { form_id: formId },
            success: function(response) {
                formFields = response.fields || [];
                displayFormFields();
                updateFieldMappingSection();
            },
            error: function() {
                showError('Failed to load form fields');
                clearFormFields();
            }
        });
    }

    // Load endpoint fields via AJAX
    function loadEndpointFields(endpointId) {
        $.ajax({
            url: '{{ route("admin.form-integration-settings.get-endpoint-fields") }}',
            method: 'GET',
            data: { endpoint_id: endpointId },
            success: function(response) {
                endpointFields = response.fields || [];
                displayEndpointFields();
                updateFieldMappingSection();
            },
            error: function() {
                showError('Failed to load endpoint fields');
                clearEndpointFields();
            }
        });
    }

    // Display form fields
    function displayFormFields() {
        const container = $('#form-fields-list');
        if (formFields.length === 0) {
            container.html('<p class="text-muted">No fields found in this form</p>');
            return;
        }

        let html = '';
        formFields.forEach(function(field) {
            const requiredBadge = field.required ? '<span class="badge badge-danger">Required</span>' : '';
            html += `
                <div class="form-field-item mb-2 p-2 border rounded" data-field-key="${field.key}">
                    <strong>${field.label || field.key}</strong> ${requiredBadge}
                    <br><small class="text-muted">Key: ${field.key} | Type: ${field.type}</small>
                </div>
            `;
        });
        container.html(html);
    }

    // Display endpoint fields
    function displayEndpointFields() {
        const container = $('#endpoint-fields-list');
        if (endpointFields.length === 0) {
            container.html('<p class="text-muted">No fields found in this endpoint configuration</p>');
            return;
        }

        let html = '';
        endpointFields.forEach(function(field) {
            const requiredBadge = field.required ? '<span class="badge badge-danger">Required</span>' : '';
            const dataType = field.datatype ? `<span class="badge badge-info">${field.datatype}</span>` : '';
            html += `
                <div class="endpoint-field-item mb-2 p-2 border rounded" data-field-name="${field.name}">
                    <strong>${field.name}</strong> ${requiredBadge} ${dataType}
                    <br><small class="text-muted">${field.description || 'No description'}</small>
                </div>
            `;
        });
        container.html(html);
    }

    // Update field mapping section visibility and content
    function updateFieldMappingSection() {
        if (formFields.length > 0 && endpointFields.length > 0) {
            $('#field-mapping-section').show();
            $('#add-mapping-btn').show();
            displayFieldMappings();
        } else {
            $('#field-mapping-section').hide();
        }
    }

    // Display field mappings
    function displayFieldMappings() {
        const container = $('#field-mappings-container');
        
        if (Object.keys(fieldMappings).length === 0) {
            container.html('<p class="text-muted">No field mappings configured. Click "Add Mapping" or "Suggest Mappings" to get started.</p>');
            return;
        }

        let html = '<div class="field-mappings-list">';
        Object.keys(fieldMappings).forEach(function(formFieldKey, index) {
            const endpointFieldName = fieldMappings[formFieldKey];
            html += createMappingRow(formFieldKey, endpointFieldName, index);
        });
        html += '</div>';
        
        container.html(html);
    }

    // Create a mapping row
    function createMappingRow(formFieldKey, endpointFieldName, index) {
        const formFieldOptions = formFields.map(field => 
            `<option value="${field.key}" ${field.key === formFieldKey ? 'selected' : ''}>${field.label || field.key}</option>`
        ).join('');

        const endpointFieldOptions = endpointFields.map(field => 
            `<option value="${field.name}" ${field.name === endpointFieldName ? 'selected' : ''}>${field.name}</option>`
        ).join('');

        return `
            <div class="mapping-row mb-3 p-3 border rounded" data-index="${index}">
                <div class="row">
                    <div class="col-md-5">
                        <label>Form Field</label>
                        <select class="form-control form-field-select" name="field_mappings_form[]">
                            <option value="">-- Select Form Field --</option>
                            ${formFieldOptions}
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label>Endpoint Field</label>
                        <select class="form-control endpoint-field-select" name="field_mappings_endpoint[]">
                            <option value="">-- Select Endpoint Field --</option>
                            ${endpointFieldOptions}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label>&nbsp;</label>
                        <button type="button" class="btn btn-danger btn-block remove-mapping-btn">
                            <i class="fa fa-trash"></i>
                        </button>
                    </div>
                </div>
                <input type="hidden" name="field_mappings[${formFieldKey}]" value="${endpointFieldName}">
            </div>
        `;
    }

    // Add empty mapping
    function addEmptyMapping() {
        const newIndex = Object.keys(fieldMappings).length;
        const html = createMappingRow('', '', newIndex);
        $('#field-mappings-container .field-mappings-list').append(html);
    }

    // Handle mapping changes
    $(document).on('change', '.form-field-select, .endpoint-field-select', function() {
        const row = $(this).closest('.mapping-row');
        const formField = row.find('.form-field-select').val();
        const endpointField = row.find('.endpoint-field-select').val();
        
        if (formField && endpointField) {
            fieldMappings[formField] = endpointField;
            row.find('input[type="hidden"]').attr('name', `field_mappings[${formField}]`).val(endpointField);
        }
    });

    // Handle mapping removal
    $(document).on('click', '.remove-mapping-btn', function() {
        const row = $(this).closest('.mapping-row');
        const formField = row.find('.form-field-select').val();
        if (formField && fieldMappings[formField]) {
            delete fieldMappings[formField];
        }
        row.remove();
        
        if (Object.keys(fieldMappings).length === 0) {
            displayFieldMappings();
        }
    });

    // Suggest field mappings
    function suggestFieldMappings() {
        const formId = $('#form_id').val();
        const endpointId = $('#endpoint_configuration_id').val();
        
        if (!formId || !endpointId) {
            showError('Please select both form and endpoint configuration first');
            return;
        }

        $.ajax({
            url: '{{ route("admin.form-integration-settings.get-field-mapping-suggestions") }}',
            method: 'GET',
            data: { 
                form_id: formId,
                endpoint_id: endpointId
            },
            success: function(response) {
                fieldMappings = response.suggestions || {};
                displayFieldMappings();
                showSuccess('Field mapping suggestions applied');
            },
            error: function() {
                showError('Failed to get field mapping suggestions');
            }
        });
    }

    // Filter endpoint options
    function filterEndpointOptions() {
        const erpFilter = $('#erp_filter').val();
        const processFilter = $('#process_filter').val();
        const typeFilter = $('#endpoint_type_filter').val();
        
        $('#endpoint_configuration_id option').each(function() {
            if ($(this).val() === '') return; // Skip the default option
            
            const erp = $(this).data('erp');
            const process = $(this).data('process');
            const type = $(this).data('type');
            
            let show = true;
            
            if (erpFilter && erp !== erpFilter) show = false;
            if (processFilter && process !== processFilter) show = false;
            if (typeFilter && type !== typeFilter) show = false;
            
            $(this).toggle(show);
        });
    }

    // Clear form fields
    function clearFormFields() {
        formFields = [];
        $('#form-fields-list').html('<p class="text-muted">Select a form to see its fields</p>');
    }

    // Clear endpoint fields
    function clearEndpointFields() {
        endpointFields = [];
        $('#endpoint-fields-list').html('<p class="text-muted">Select an endpoint configuration to see its fields</p>');
    }

    // Utility functions
    function showError(message) {
        alert('Error: ' + message);
    }

    function showSuccess(message) {
        console.log('Success: ' + message);
    }
});
</script>
@endsection

@section('styles')
<style>
.required::after {
    content: " *";
    color: red;
}

.form-field-item, .endpoint-field-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.form-field-item:hover, .endpoint-field-item:hover {
    background-color: #f8f9fa;
}

.mapping-row {
    background-color: #f8f9fa;
}

.badge {
    font-size: 0.7em;
}

#field-mapping-section .card-body {
    background-color: #fff;
}

.field-mappings-list {
    max-height: 500px;
    overflow-y: auto;
}
</style>
@endsection
