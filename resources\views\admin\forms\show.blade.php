@extends('layouts.admin')
@section('pageTitle', 'Form Details')

@section('content')
<div class="card card-default">
    <div class="card-header separator">
        <div class="card-title mainheading">
            <h4>Form Details</h4>
        </div>
        <div class="card-controls">
            <ul>
                <li>
                    <a class="btn btn-secondary" href="{{ route('admin.forms.index') }}">
                        Back to List
                    </a>
                </li>
                @if(auth()->user()->isAdmin() || auth()->user()->isSuperAdmin())
                <li>
                    <a class="btn btn-info" href="{{ route('admin.forms.edit', $form->id) }}">
                        Edit
                    </a>
                </li>
                @endif
                <li>
                    <a class="btn btn-success" href="{{ route('admin.forms.preview', $form->id) }}" target="_blank">
                        Preview Form
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <div class="card-block">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">Title</th>
                        <td>{{ $form->title }}</td>
                    </tr>
                    <tr>
                        <th>User Group</th>
                        <td>
                            @if($form->userGroup)
                                <span class="badge badge-primary">{{ $form->userGroup->name }}</span>
                            @else
                                <span class="text-muted">All Groups</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>Created By</th>
                        <td>{{ $form->creator ? $form->creator->name : 'Unknown' }}</td>
                    </tr>
                    <tr>
                        <th>Status</th>
                        <td>
                            @if($form->is_active)
                                <span class="badge badge-success">Active</span>
                            @else
                                <span class="badge badge-danger">Inactive</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>Created At</th>
                        <td>{{ $form->created_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                    <tr>
                        <th>Updated At</th>
                        <td>{{ $form->updated_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                @if($form->userGroup)
                <div class="card">
                    <div class="card-header">
                        <h5>User Group Information</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <th>Group Name:</th>
                                <td>{{ $form->userGroup->name }}</td>
                            </tr>
                            <tr>
                                <th>Description:</th>
                                <td>{{ $form->userGroup->description ?? 'N/A' }}</td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    @if($form->userGroup->is_active)
                                        <span class="badge badge-success">Active</span>
                                    @else
                                        <span class="badge badge-danger">Inactive</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>Total Members:</th>
                                <td>{{ $form->userGroup->users->count() }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <div class="mt-4">
            <h5>Form Structure</h5>
            <div class="card">
                <div class="card-body">
                    <pre class="bg-light p-3" style="max-height: 400px; overflow-y: auto;">{{ json_encode($form->content, JSON_PRETTY_PRINT) }}</pre>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
