/*

* Override Pages default styles or create your own styles here

*/
.pgn-wrapper{
    min-width: 300px;
}
.summernote-wrapper{
    margin: -20px;
}
.sticky_card_primary > .card-header{
    position: sticky;
    top: 60px;

    /*border: 1px solid #ccc;*/
    /*border-bottom: 0px;*/
    z-index: 99;
    /*background: #fff;*/

}
.card .card-header{
    /* background: #323237; */
    /* color: #fff; */
}
.card-header h3 {
    color: #fff;
}
.sticky_card_primary > .card-header h3{

}
.sticky_card_primary > .card-header .card-controls .fa{
    margin-right: 5px;
}
th {
    color: black !important;
    background-color: #f0f0f0;
}
.card .card-header + .card-block {
    padding-top: 20px;
}
.page-sidebar .sidebar-menu .menu-items li > a{
    width: 78%;
}
.sub_section{
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-top: 15px;
    padding: 15px;
}
.sub_section h5{
    text-align: center;
    border-bottom: 1px solid #ccc;
    line-height: 2;
    margin-top: -15px;

}
.item_row{
    border: 1px solid #cecece;
    border-radius: 5px;
    margin-top: 15px;
    padding: 15px;
}
.form-horizontal .form-group .control-label {
    font-weight: bold !important;
    color: #000;
    font-size: 12px !important;
    opacity: 1 !important;
    text-transform: capitalize !important;
}
.windows h5,.windows h6 {

    font-weight: 500;
}
.item_row > h6{

    background: #323237;
    padding: 10px;
    color: #fff;
    margin: -15px;
    margin-bottom: 15px;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;

}
.select2{
    max-width: 100%;
}
thead th {
    color: black !important;
    background-color: #949494;

}
.container-fluid {
    padding-left: 15px;
    padding-right: 15px;
}
table.dataTable>thead>tr>th:not(.sorting_disabled), table.dataTable>thead>tr>td:not(.sorting_disabled){
    padding-right: 5px !important;
    /*padding-left: 5px !important;*/
}
.table-bordered td, .table-bordered th {
    border: 1px solid #bfc0c1;
}
table.dataTable td, table.dataTable th {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
.table-striped tbody tr:nth-of-type(odd) {
    background-color: #e6e6e6;
}
.form-horizontal .form-group .control-label{
    opacity: 0.9;
}
td,th{
    padding: 0px 10px;
    text-align: center;
}
.v-align-middled.inputs{
    /*min-width: 100px;*/
}
label{
    font-weight: bold;
}
.danger{
    background: #ce4242;
    color: #ffffff;
}
.warning{
    background: #daca9e;
    color: #fff;
}
.success{
    background: #0eaf91;
    color: #fff;
}
.table.dataTable.no-footer {
    border: auto;
}
th{
    padding-top: 5px;
    padding-bottom: 5px;

}
td{
    padding-top: 5px;
    padding-bottom: 5px;

}
.w_30{
    max-width: 30px !important;
    width: 30px !important;
    /*padding-right: 5px !important;*/
}
.w_50{
    width:50px;
    max-width: 50px;
}
.w_70{
    width:70px;
    max-width: 70px;
}
input:disabled {
    border: 0px;
    color: #000;
    cursor: default;
    background-color: #fff0;
    text-align: center;
}
table.dataTable{
    width: 100%;
}
.card .card-header {

    padding: 5px 20px 5px 20px;

    min-height: 38px;
}
.windows .card-title h3 {

    font-size: 18px;
    line-height: 1;
    margin: 5px 0px 1px 0px;
    font-weight: 600;
}
.page-container .page-content-wrapper .footer{
    /*position: relative;*/


}
table.dataTable>tbody>tr.child td{
    text-align: left;

}
table.dataTable>tbody>tr.child,table.dataTable>tbody>tr.child:hover {
    background: #f3ebeb !important;
}
table.dataTable>tbody>tr.child .dtr-details{
    width: 100%;
}
table.dataTable>thead .sorting:before, table.dataTable>thead .sorting_asc:before, table.dataTable>thead .sorting_desc:before, table.dataTable>thead .sorting_asc_disabled:before, table.dataTable>thead .sorting_desc_disabled:before{
    display: none;
}
table.dataTable>thead .sorting:after, table.dataTable>thead .sorting_asc:after, table.dataTable>thead .sorting_desc:after, table.dataTable>thead .sorting_asc_disabled:after, table.dataTable>thead .sorting_desc_disabled:after {
    display: none;
}
.page-item.active .page-link {
    z-index: 2;
    color: #fff;
    background-color: #0275d8;
    border-color: #0275d8;
}
.form-control {

    border: 1px solid rgb(0 0 0 / 32%);
}
tr.validation_error{
    background: #f19f9f !important;
}
.datatable tr .parsley-errors-list.filled{
    display: inline;
    list-style: none;
}
.datepicker table tr td.today, .datepicker table tr td.today:hover, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover {
    color: #000000;
    background-color: #ffdb99;
    border-color: #ffb733;
}
