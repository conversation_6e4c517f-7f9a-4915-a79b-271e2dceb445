# Endpoint Configuration Seeder Documentation

## Overview
The `EndpointConfigurationSeeder` provides comprehensive sample data for the Endpoint Configuration module, including configurations for both CSI and SAP ERP systems across all supported process types.

## 🗄️ Seeder Structure

### **File Location**
```
database/seeders/EndpointConfigurationSeeder.php
```

### **Dependencies**
- `App\Models\EndpointConfiguration`
- `App\Models\User`
- Requires at least one user with `type = 1` (Super Admin)

## 📊 Sample Data Included

### **CSI Configurations (3 endpoints)**

#### 1. **CSI Misc Issue API**
```php
'name' => 'CSI Misc Issue API'
'url' => 'IDORequestService/ido/invoke/SLStockActItems?method=ItemMiscIssueSp'
'erp_selection' => 'CSI'
'process_selection' => 'Misc Issue'
'endpoint_type' => 'API'
```

**Body Data Fields:**
- `item_code` (string, 50 chars, required) - Item code for inventory item
- `quantity` (integer, required, default: 1) - Quantity to issue
- `location` (string, 20 chars, required) - Warehouse location code
- `reason_code` (string, 10 chars, optional, default: 'MISC') - Issue reason

#### 2. **CSI Misc Receipt API**
```php
'name' => 'CSI Misc Receipt API'
'url' => 'IDORequestService/ido/invoke/SLStockActItems?method=ItemMiscReceiptSp'
'erp_selection' => 'CSI'
'process_selection' => 'Misc Receipt'
'endpoint_type' => 'API'
```

**Body Data Fields:**
- `item_code` (string, 50 chars, required) - Item code for inventory item
- `quantity` (integer, required, default: 1) - Quantity to receive
- `location` (string, 20 chars, required) - Warehouse location code
- `unit_cost` (float, optional) - Unit cost for received item

#### 3. **CSI Quantity Move Stored Procedure**
```php
'name' => 'CSI Quantity Move Stored Procedure'
'url' => 'StoredProcedures/sp_inventory_move'
'erp_selection' => 'CSI'
'process_selection' => 'Quantity Move'
'endpoint_type' => 'Stored Procedure'
```

**Body Data Fields:**
- `item_code` (string, 50 chars, required) - Item code for inventory item
- `from_location` (string, 20 chars, required) - Source warehouse location
- `to_location` (string, 20 chars, required) - Destination warehouse location
- `quantity` (integer, required) - Quantity to move

### **SAP Configurations (3 endpoints)**

#### 1. **SAP Misc Issue API**
```php
'name' => 'SAP Misc Issue API'
'url' => 'SAPServices/api/v1/inventory/misc-issue'
'erp_selection' => 'SAP'
'process_selection' => 'Misc Issue'
'endpoint_type' => 'API'
```

**Body Data Fields:**
- `material_number` (string, 18 chars, required) - SAP Material Number
- `plant` (string, 4 chars, required) - SAP Plant Code
- `storage_location` (string, 4 chars, required) - SAP Storage Location
- `quantity` (float, required) - Quantity to issue
- `unit_of_measure` (string, 3 chars, required, default: 'EA') - Unit of measure
- `movement_type` (string, 3 chars, required, default: '201') - SAP Movement Type

#### 2. **SAP Misc Receipt API**
```php
'name' => 'SAP Misc Receipt API'
'url' => 'SAPServices/api/v1/inventory/misc-receipt'
'erp_selection' => 'SAP'
'process_selection' => 'Misc Receipt'
'endpoint_type' => 'API'
```

**Body Data Fields:**
- `material_number` (string, 18 chars, required) - SAP Material Number
- `plant` (string, 4 chars, required) - SAP Plant Code
- `storage_location` (string, 4 chars, required) - SAP Storage Location
- `quantity` (float, required) - Quantity to receive
- `unit_of_measure` (string, 3 chars, required, default: 'EA') - Unit of measure
- `movement_type` (string, 3 chars, required, default: '501') - SAP Movement Type

#### 3. **SAP Stock Transfer API**
```php
'name' => 'SAP Stock Transfer API'
'url' => 'SAPServices/api/v1/inventory/stock-transfer'
'erp_selection' => 'SAP'
'process_selection' => 'Quantity Move'
'endpoint_type' => 'API'
```

**Body Data Fields:**
- `material_number` (string, 18 chars, required) - SAP Material Number
- `plant` (string, 4 chars, required) - SAP Plant Code
- `from_storage_location` (string, 4 chars, required) - Source SAP Storage Location
- `to_storage_location` (string, 4 chars, required) - Destination SAP Storage Location
- `quantity` (float, required) - Quantity to transfer
- `unit_of_measure` (string, 3 chars, required, default: 'EA') - Unit of measure

## 🚀 Usage Instructions

### **Run Individual Seeder**
```bash
php artisan db:seed --class=EndpointConfigurationSeeder
```

### **Run All Seeders**
```bash
php artisan db:seed
```

### **Fresh Migration with Seeding**
```bash
php artisan migrate:fresh --seed
```

## 🔧 Seeder Features

### **Duplicate Prevention**
- Checks for existing configurations by name
- Only creates new configurations if they don't exist
- Provides informative output about creation/skipping

### **Audit Trail Support**
- Automatically sets `created_by` and `updated_by` fields
- Uses first available Super Admin user
- Creates system admin user if none exists

### **Comprehensive Data**
- Covers all ERP systems (CSI, SAP)
- Covers all process types (Misc Issue, Misc Receipt, Quantity Move)
- Covers all endpoint types (API, Stored Procedure)
- Realistic field mappings and data types

### **Production Ready**
- Safe to run multiple times
- Informative console output
- Proper error handling
- Follows Laravel seeding best practices

## 📋 Field Data Types Reference

### **Common Data Types Used**
- **string** - Text fields with specified max length
- **integer** - Whole numbers (quantities, counts)
- **float** - Decimal numbers (costs, weights)

### **Field Properties**
- **name** - Field identifier
- **datatype** - Data type (string, integer, float)
- **maxlength** - Maximum character length (null for numeric types)
- **required** - Whether field is mandatory
- **default** - Default value if not provided
- **description** - Human-readable field description

## 🎯 Benefits

### **Development**
- Quick setup of realistic test data
- Comprehensive coverage of all scenarios
- Consistent data structure across environments

### **Testing**
- Reliable test data for automated tests
- Edge cases covered (optional fields, defaults)
- Both API and Stored Procedure examples

### **Documentation**
- Living examples of proper configuration structure
- Reference for field naming conventions
- Template for creating new configurations

This seeder provides a solid foundation for development, testing, and demonstration of the Endpoint Configuration module with realistic, comprehensive sample data.
