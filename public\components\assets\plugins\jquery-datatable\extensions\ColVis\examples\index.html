<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
    <link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
    <link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
    <script type="text/javascript" language="javascript" src="../../../media/js/jquery.js"></script>
    <script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
    <script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>
    <title>ColVis examples - ColVis examples</title>
  </head>
  <body class="dt-example">
    <div class="container">
      <section>
        <h1>ColVis example <span>ColVis examples</span></h1>
        <div class="info">
          <p>ColVis adds a button to the toolbars around DataTables which gives the end user of the table the ability to dynamically change the visibility of the columns in the table:</p>
          <ul class="markdown">
            <li>Dynamically show and hide columns in a table</li>
            <li>Very easy integration with DataTables</li>
            <li>Ability to exclude columns from being either hidden or shown</li>
            <li>Save saving integration with DataTables</li>
          </ul>
        </div>
      </section>
    </div>
    <section>
      <div class="footer">
        <div class="gradient"></div>
        <div class="liner">
          <div class="toc">
            <div class="toc-group">
              <h3><a href="./index.html">Examples</a></h3>
              <ul class="toc">
                <li><a href="./simple.html">Basic initialisation</a></li>
                <li><a href="./new_init.html">`new` initialisation</a></li>
                <li><a href="./text.html">Custom button text</a></li>
                <li><a href="./exclude_columns.html">Exclude columns from list</a></li>
                <li><a href="./title_callback.html">Column button callback</a></li>
                <li><a href="./button_order.html">Button ordering</a></li>
                <li><a href="./mouseover.html">Mouseover activation</a></li>
                <li><a href="./group_columns.html">Group columns</a></li>
                <li><a href="./two_tables.html">Two tables with individual controls</a></li>
                <li><a href="./two_tables_identical.html">Two tables with shared controls</a></li>
                <li><a href="./restore.html">Restore / show all</a></li>
                <li><a href="./jqueryui.html">jQuery UI styling</a></li>
              </ul>
            </div>
          </div>
          <div class="epilogue">
            <p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.
              <br> Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and <a href="http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of DataTables.</p>
            <p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015
              <br> DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
          </div>
        </div>
      </div>
    </section>
  </body>
</html>