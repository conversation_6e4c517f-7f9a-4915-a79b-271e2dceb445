@extends('layouts.admin')
@section('pageTitle', 'Users')

@section('content')
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>Users List</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <a class="btn btn-success" href="{{ route('admin.users.create') }}">
                            Add User
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="card-block">
            <div class="table-responsived">
                <table class="table table-bordered table-striped table-hover datatable" id="users-table">
                    <thead>
                        <tr>
                            <th width="10">#</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>User Group</th>
                            <th>Role</th>
                            <th>Created At</th>
                            <th width="150">Action</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    @parent
    <script>
        $(function() {
            $('#users-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: "{{ route('admin.users.index') }}",
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {
                        data: 'email',
                        name: 'email'
                    },
                    {
                        data: 'user_group',
                        name: 'userGroup.name'
                    },
                    {
                        data: 'role_type',
                        name: 'type'
                    },
                    {
                        data: 'created_at',
                        name: 'created_at'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                order: [
                    [5, 'desc']
                ],
                pageLength: 25,
                responsive: true,
                dom: 'Bfrtip',
                buttons: buttonsDefault,
            });
        });
    </script>
@endsection
