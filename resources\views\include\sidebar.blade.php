<nav class="page-sidebar {{1 == 1 ? 'sp_admin' : '' }}" data-pages="sidebar">
    <!-- BEGIN SIDEBAR MENU TOP TRAY CONTENT-->

    <!-- <PERSON>ND SIDEBAR MENU TOP TRAY CONTENT-->
    <!-- BEGIN SIDEBAR MENU HEADER-->
    <div class="sidebar-header">
        <a href="/">
            <h4 style="color:#fff;display: inline;font-weight: 600;">{{ env('APP_NAME') }}</h4>
        </a>
        <!--<img src="/logo.png" alt="logo" class="brand" data-src="/logo.png" data-src-retina="/logo.png" width="78" height="22">-->
        <div class="sidebar-header-controls">
            <!--            <button type="button" class="btn btn-xs sidebar-slide-toggle btn-link m-l-20 hidden-md-down" data-pages-toggle="#appMenu"><i class="fa fa fa-angle-down fs-16"></i>
            </button>-->
            <button type="button" class="btn btn-link hidden-md-down" data-toggle-pin="sidebar"><i
                    class="fa fs-12"></i>
            </button>
        </div>
    </div>
    <!-- END SIDEBAR MENU HEADER-->
    <!-- START SIDEBAR MENU -->
    <div class="sidebar-menu">
        <!-- BEGIN SIDEBAR MENU ITEMS-->
        <ul class="menu-items">


            <li class="m-t-30 {{ request()->segment(1) == 'admin' && request()->segment(2) == '' ? 'active' : '' }}">
                <a href="/admin" class="">
                    <span class="title">Dashboard</span>
                    <!--<span class="details">12 New Updates</span>-->
                </a>
                <span class="icon-thumbnail">
                    <i class="fa fa-dashboard"></i>
                </span>
            </li>

            <hr style="margin: 5px;border-color: #cecece;">
            </hr>


            @can('user_management_access')
                <?php
                $manageRequests = ['admin/permissions*', 'admin/roles*', 'admin/users*'];
                $managementClass = '';
                foreach ($manageRequests as $req) {
                    if (request()->is($req)) {
                        $managementClass = 'open active';
                    }
                }
                ?>
                <li class="sidebar-menu-bg1 {{ $managementClass }}">

                    <a href="javascript:;"><span class="title"> {{ trans('cruds.userManagement.title') }}</span>
                        <span class=" arrow open active"></span></a>
                    <span class="icon-thumbnail"><i class="fa fa-users"></i></span>

                    <ul class="sub-menu">
                        {{-- @can('permission_access') --}}
                            {{-- <li
                                class="{{ request()->is('admin/permissions') || request()->is('admin/permissions/*') ? 'active' : '' }}">
                                <a href="{{ route('admin.permissions.index') }}">{{ trans('cruds.permission.title') }}</a>
                                <span class="icon-thumbnail">PM</span>

                            </li> --}}
                        {{-- @endcan --}}
                        {{-- @can('role_access') --}}
                            {{-- <li class="{{ request()->is('admin/roles') || request()->is('admin/roles/*') ? 'active' : '' }}">
                                <a href="{{ route('admin.roles.index') }}">{{ trans('cruds.role.title') }}</a>
                                <span class="icon-thumbnail">RL</span>
                            </li> --}}
                        {{-- @endcan --}}
                        {{-- @can('user_access') --}}
                            <li class="{{ request()->is('admin/users') || request()->is('admin/users/*') ? 'active' : '' }}">
                                <a href="{{ route('admin.users.index') }}">{{ trans('cruds.user.title') }}</a>
                                <span class="icon-thumbnail"><i class="fa fa-user-plus"></i></span>
                            </li>
                        {{-- @endcan --}}
                         <li class="{{ request()->is('admin/usergroups') || request()->is('admin/usergroups/*') ? 'active' : '' }}">
                                <a href="{{ route('admin.usergroups.index') }}">{{ trans('cruds.usergroups.title') }}</a>
                                <span class="icon-thumbnail"><i class="fa fa-users"></i></span>
                            </li>
                            <li class="{{ request()->is('admin/forms') || request()->is('admin/forms/*') ? 'active' : '' }}">
                                <a href="{{ route('admin.forms.index') }}">Forms</a>
                                <span class="icon-thumbnail"><i class="fa fa-wpforms"></i></span>
                            </li>


                    </ul>
                </li>
            @endcan






            {{-- Endpoint Configuration Menu - TODO: Restrict to Super Admin when user management is ready --}}
            @if(auth()->check())
                <li class="m-t-30 {{ request()->is('admin/endpoint-configurations*') ? 'active' : '' }}">
                    <a href="{{ route('admin.endpoint-configurations.index') }}" class="">
                        <span class="title">{{ trans('cruds.endpointConfiguration.title') }}</span>
                    </a>
                    <span class="icon-thumbnail">
                        <i class="fa fa-cogs"></i>
                    </span>
                </li>
            @endif

            {{-- Form Integration Settings Menu - TODO: Restrict to Super Admin when user management is ready --}}
            @if(auth()->check())
                <li class="m-t-30 {{ request()->is('admin/form-integration-settings*') ? 'active' : '' }}">
                    <a href="{{ route('admin.form-integration-settings.index') }}" class="">
                        <span class="title">Form Integration Settings</span>
                    </a>
                    <span class="icon-thumbnail">
                        <i class="fa fa-link"></i>
                    </span>
                </li>
            @endif

        </ul>
        <div class="clearfix"></div>
    </div>

    <!-- END SIDEBAR MENU -->
</nav>
