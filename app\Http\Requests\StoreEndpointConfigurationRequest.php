<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreEndpointConfigurationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // TODO: Re-enable permission checking when user management system is ready
        // return auth()->check() && auth()->user()->type === 1; // TYPE_SUPER_ADMIN
        return auth()->check(); // Temporarily allow any authenticated user
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:255|unique:endpoint_configurations,name',
            'url' => 'required|url|max:2000',
            'body_data_field' => 'nullable|json',
            'erp_selection' => 'required|in:CSI,SAP',
            'process_selection' => 'required|in:Misc Issue,Misc Receipt,Quantity Move',
            'endpoint_type' => 'required|in:API,Stored Procedure',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'The configuration name is required.',
            'name.unique' => 'A configuration with this name already exists.',
            'url.required' => 'The endpoint URL is required.',
            'url.url' => 'Please enter a valid URL.',
            'erp_selection.required' => 'Please select an ERP system.',
            'erp_selection.in' => 'Invalid ERP selection.',
            'process_selection.required' => 'Please select a process type.',
            'process_selection.in' => 'Invalid process selection.',
            'endpoint_type.required' => 'Please select an endpoint type.',
            'endpoint_type.in' => 'Invalid endpoint type selection.',
        ];
    }
}
