<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreFormIntegrationSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // TODO: Re-enable permission checking when user management system is ready
        // return auth()->user()->type === 1; // Super admin only
        return auth()->check(); // Temporarily allow any authenticated user
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                'unique:form_integration_settings,name'
            ],
            'form_id' => [
                'required',
                'integer',
                'exists:forms,id'
            ],
            'endpoint_configuration_id' => [
                'required',
                'integer',
                'exists:endpoint_configurations,id'
            ],
            'field_mappings' => [
                'required',
                'array',
                'min:1'
            ],
            'field_mappings.*' => [
                'required',
                'string',
                'max:255'
            ],
            'description' => [
                'nullable',
                'string',
                'max:1000'
            ],
            'is_active' => [
                'boolean'
            ]
        ];
    }

    /**
     * Get custom validation messages
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'The integration setting name is required.',
            'name.unique' => 'An integration setting with this name already exists.',
            'form_id.required' => 'Please select a form.',
            'form_id.exists' => 'The selected form does not exist.',
            'endpoint_configuration_id.required' => 'Please select an endpoint configuration.',
            'endpoint_configuration_id.exists' => 'The selected endpoint configuration does not exist.',
            'field_mappings.required' => 'At least one field mapping is required.',
            'field_mappings.min' => 'At least one field mapping is required.',
            'field_mappings.*.required' => 'All field mappings must have a value.',
            'field_mappings.*.string' => 'Field mapping values must be text.',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Check for unique form-endpoint combination
            $formId = $this->input('form_id');
            $endpointId = $this->input('endpoint_configuration_id');
            
            if ($formId && $endpointId) {
                $exists = \App\Models\FormIntegrationSetting::where('form_id', $formId)
                    ->where('endpoint_configuration_id', $endpointId)
                    ->exists();
                
                if ($exists) {
                    $validator->errors()->add('endpoint_configuration_id', 
                        'This form is already integrated with the selected endpoint configuration.');
                }
            }

            // Validate that the selected form is active
            if ($formId) {
                $form = \App\Models\Form::find($formId);
                if ($form && !$form->is_active) {
                    $validator->errors()->add('form_id', 'The selected form is not active.');
                }
            }

            // Validate that the selected endpoint configuration is active
            if ($endpointId) {
                $endpoint = \App\Models\EndpointConfiguration::find($endpointId);
                if ($endpoint && !$endpoint->is_active) {
                    $validator->errors()->add('endpoint_configuration_id', 
                        'The selected endpoint configuration is not active.');
                }
            }

            // Validate field mappings against actual form and endpoint fields
            $this->validateFieldMappings($validator, $formId, $endpointId);
        });
    }

    /**
     * Validate field mappings against form and endpoint fields
     */
    private function validateFieldMappings($validator, $formId, $endpointId)
    {
        if (!$formId || !$endpointId) {
            return;
        }

        try {
            $form = \App\Models\Form::find($formId);
            $endpoint = \App\Models\EndpointConfiguration::find($endpointId);
            
            if (!$form || !$endpoint) {
                return;
            }

            // Get form fields
            $formFields = $this->extractFormFields($form->content ?? []);
            $formFieldKeys = array_column($formFields, 'key');

            // Get endpoint fields
            $endpointFields = $endpoint->body_data_field ?? [];
            $endpointFieldNames = array_column($endpointFields, 'name');

            // Validate each mapping
            $fieldMappings = $this->input('field_mappings', []);
            
            foreach ($fieldMappings as $formFieldKey => $endpointFieldName) {
                // Check if form field exists
                if (!in_array($formFieldKey, $formFieldKeys)) {
                    $validator->errors()->add("field_mappings.{$formFieldKey}", 
                        "Form field '{$formFieldKey}' does not exist in the selected form.");
                }

                // Check if endpoint field exists
                if (!in_array($endpointFieldName, $endpointFieldNames)) {
                    $validator->errors()->add("field_mappings.{$formFieldKey}", 
                        "Endpoint field '{$endpointFieldName}' does not exist in the selected endpoint configuration.");
                }
            }

            // Check for required endpoint fields that are not mapped
            foreach ($endpointFields as $endpointField) {
                if (($endpointField['required'] ?? false) && !in_array($endpointField['name'], $fieldMappings)) {
                    $validator->errors()->add('field_mappings', 
                        "Required endpoint field '{$endpointField['name']}' is not mapped to any form field.");
                }
            }

        } catch (\Exception $e) {
            $validator->errors()->add('field_mappings', 
                'An error occurred while validating field mappings: ' . $e->getMessage());
        }
    }

    /**
     * Extract form fields from form content
     */
    private function extractFormFields($formContent)
    {
        $fields = [];
        
        if (isset($formContent['components']) && is_array($formContent['components'])) {
            $this->extractFieldsRecursive($formContent['components'], $fields);
        }

        return $fields;
    }

    /**
     * Recursively extract fields from form components
     */
    private function extractFieldsRecursive($components, &$fields)
    {
        foreach ($components as $component) {
            if (isset($component['key']) && isset($component['type'])) {
                $fields[] = [
                    'key' => $component['key'],
                    'label' => $component['label'] ?? $component['key'],
                    'type' => $component['type'],
                ];
            }

            // Handle nested components
            if (isset($component['components']) && is_array($component['components'])) {
                $this->extractFieldsRecursive($component['components'], $fields);
            }
        }
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Ensure field_mappings is an array
        if ($this->has('field_mappings') && is_string($this->field_mappings)) {
            $this->merge([
                'field_mappings' => json_decode($this->field_mappings, true) ?? []
            ]);
        }

        // Set default values
        $this->merge([
            'is_active' => $this->boolean('is_active', true),
        ]);
    }
}
