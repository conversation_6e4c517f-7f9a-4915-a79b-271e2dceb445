<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EndpointConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'url',
        'body_data_field',
        'erp_selection',
        'process_selection',
        'endpoint_type',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'body_data_field' => 'array',
    ];

    // Available options for dropdowns
    public static function getErpOptions()
    {
        return [
            'CSI' => 'CSI',
            'SAP' => 'SAP',
        ];
    }

    public static function getProcessOptions()
    {
        return [
            'Misc Issue' => 'Misc Issue',
            'Misc Receipt' => 'Misc Receipt',
            'Quantity Move' => 'Quantity Move',
        ];
    }

    public static function getEndpointTypeOptions()
    {
        return [
            'API' => 'API',
            'Stored Procedure' => 'Stored Procedure',
        ];
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByErp($query, $erp)
    {
        return $query->where('erp_selection', $erp);
    }

    public function scopeByProcess($query, $process)
    {
        return $query->where('process_selection', $process);
    }

    public function scopeByEndpointType($query, $type)
    {
        return $query->where('endpoint_type', $type);
    }

    /**
     * Get body data field as JSON string for forms
     */
    public function getBodyDataFieldJsonAttribute()
    {
        return is_array($this->body_data_field)
            ? json_encode($this->body_data_field)
            : $this->body_data_field;
    }
}
