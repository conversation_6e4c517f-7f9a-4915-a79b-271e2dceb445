@extends('layouts.admin')
@section('pageTitle', 'Forms')

@section('content')
<div class="card card-default">
    <div class="card-header separator">
        <div class="card-title mainheading">
            <h4>Forms List</h4>
        </div>
        <div class="card-controls">
            <ul>
                @if(auth()->user()->isAdmin() || auth()->user()->isSuperAdmin())
                <li>
                    <a class="btn btn-success" href="{{ route('admin.forms.create') }}">
                        Create Form
                    </a>
                </li>
                @endif
            </ul>
        </div>
    </div>

    <div class="card-block">
        <div class="table-responsive">
            <table class="table table-bordered table-striped table-hover datatable" id="forms-table">
                <thead>
                    <tr>
                        <th width="10">#</th>
                        <th>Title</th>
                        <th>User Group</th>
                        <th>Created By</th>
                        <th>Status</th>
                        <th>Created At</th>
                        <th width="150">Action</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>
@endsection

@section('scripts')
@parent
<script>
$(function() {
    $('#forms-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: "{{ route('admin.forms.index') }}",
        columns: [
            {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
            {data: 'title', name: 'title'},
            {data: 'user_group', name: 'userGroup.name'},
            {data: 'creator_name', name: 'creator.name'},
            {data: 'status', name: 'is_active'},
            {data: 'created_at', name: 'created_at'},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        order: [[5, 'desc']],
        pageLength: 25,
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });
});
</script>
@endsection
