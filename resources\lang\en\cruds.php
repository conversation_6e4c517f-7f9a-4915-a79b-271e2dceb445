<?php

return [
    'userManagement' => [
        'title' => 'User management',
        'title_singular' => 'User management',
    ],
    'permission' => [
        'title' => 'Permissions',
        'title_singular' => 'Permission',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'title' => 'Title',
            'title_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'role' => [
        'title' => 'Roles',
        'title_singular' => 'Role',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'title' => 'Title',
            'title_helper' => ' ',
            'permissions' => 'Permissions',
            'permissions_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'user' => [
        'title' => 'Users',
        'title_singular' => 'User',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'name' => 'Name',
            'name_helper' => ' ',
            'email' => 'Email',
            'email_helper' => ' ',
            'email_verified_at' => 'Email verified at',
            'email_verified_at_helper' => ' ',
            'password' => 'Password',
            'password_helper' => ' ',
            'roles' => 'Roles',
            'roles_helper' => ' ',
            'remember_token' => 'Remember Token',
            'remember_token_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'approved' => 'Approved',
            'approved_helper' => ' ',
            'verified' => 'Verified',
            'verified_helper' => ' ',
            'verified_at' => 'Verified at',
            'verified_at_helper' => ' ',
            'verification_token' => 'Verification token',
            'verification_token_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
        ],
    ],
    'usergroups' => [
        'title' => 'User Groups',
        'title_singular' => 'User Group'
    ],
    'customerCode' => [
        'title' => 'Customer Code',
        'title_singular' => 'Customer Code',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'customer_code' => 'Customer Code',
            'customer_code_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'auditLog' => [
        'title' => 'Audit Logs',
        'title_singular' => 'Audit Log',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'description' => 'Description',
            'description_helper' => ' ',
            'subject_id' => 'Subject ID',
            'subject_id_helper' => ' ',
            'subject_type' => 'Subject Type',
            'subject_type_helper' => ' ',
            'user_id' => 'User ID',
            'user_id_helper' => ' ',
            'properties' => 'Properties',
            'properties_helper' => ' ',
            'host' => 'Host',
            'host_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
        ],
    ],
    'team' => [
        'title' => 'Teams',
        'title_singular' => 'Team',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated At',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted At',
            'deleted_at_helper' => ' ',
            'name' => 'Name',
            'name_helper' => ' ',
        ],
    ],
    'company' => [
        'title' => 'Company',
        'title_singular' => 'Company',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'company_name' => 'Company Name',
            'company_name_helper' => ' ',
            'tin' => 'Company TIN',
            'tin_helper' => ' ',
            'telephone_number' => 'Telephone Number',
            'telephone_number_helper' => ' ',
            'mobile_number' => 'Mobile Number',
            'mobile_number_helper' => ' ',
            'fax' => 'Fax',
            'fax_helper' => ' ',
            'email_address' => 'Email Address',
            'email_address_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'is_client' => 'Is Customer',
            'is_client_helper' => ' ',
            'is_supplier' => 'Is Supplier',
            'is_supplier_helper' => ' ',
            'customer_credit_limit' => 'Customer Credit Limit',
            'customer_credit_limit_helper' => ' ',
            'sales_document_type' => 'Sales Document Type',
            'sales_document_type_helper' => ' ',
            'zero_vat' => 'Zero VAT',
            'zero_vat_helper' => ' ',
            'receive_original_invoice' => 'Receive Original Invoice',
            'receive_original_invoice_helper' => ' ',
            'withholding_tax_agent' => 'Withholding Tax Agent',
            'withholding_tax_agent_helper' => ' ',
            'control_number' => 'Control Number',
            'control_number_helper' => ' ',
            'accounting_info' => 'Accounting Info',
            'accounting_info_helper' => ' ',
            'delivery_info' => 'Delivery Info',
            'delivery_info_helper' => ' ',
            'customer_code' => 'Customer Code',
            'customer_code_helper' => ' ',
            'customer_rating' => 'Customer Rating',
            'customer_rating_helper' => ' ',
            'customer_terms' => 'Customer Terms',
            'customer_terms_helper' => ' ',
            'account_manager' => 'Account Manager',
            'account_manager_helper' => ' ',
            'agent' => 'Agent',
            'agent_helper' => ' ',
            'customer_notes' => 'Customer Notes',
            'customer_notes_helper' => ' ',
            'supplier_code' => 'Supplier Code',
            'supplier_code_helper' => ' ',
            'supplier_type' => 'Supplier Type',
            'supplier_type_helper' => ' ',
            'supplier_terms' => 'Supplier Terms',
            'supplier_terms_helper' => ' ',
            'threshold_shipment_value' => 'Threshold Shipment Value',
            'threshold_shipment_value_helper' => ' ',
            'default_brand' => 'Default Brand',
            'default_brand_helper' => ' ',
            'default_currency' => 'Default Currency',
            'default_currency_helper' => ' ',
            'internal_contact' => 'Internal Contact',
            'internal_contact_helper' => ' ',
            'is_disabled' => 'Is Disabled',
            'is_disabled_helper' => ' ',
            'supplier_notes' => 'Supplier Notes',
            'supplier_notes_helper' => ' ',
        ],
    ],
    'contact' => [
        'title' => 'Customer Contacts',
        'title_singular' => 'Customer Contact',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'contact_type' => 'Contact Type',
            'contact_type_helper' => ' ',
            'telephone_number' => 'Telephone Number',
            'telephone_number_helper' => ' ',
            'mobile_number' => 'Mobile Number',
            'mobile_number_helper' => ' ',
            'email_address' => 'Email Address',
            'email_address_helper' => ' ',
            'notes' => 'Notes',
            'notes_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'company_name' => 'Company Name',
            'company_name_helper' => ' ',
            'contact_name' => 'Contact Name',
            'contact_name_helper' => ' ',
            'street_address' => 'Street Address',
            'street_address_helper' => ' ',
            'city' => 'City',
            'city_helper' => ' ',
            'country' => 'Country',
            'country_helper' => ' ',
            'contact_bank_name' => 'Contact Bank Name',
            'contact_bank_name_helper' => ' ',
            'contact_bank_branch' => 'Contact Bank Branch',
            'contact_bank_branch_helper' => ' ',
            'contact_bank_acct_name' => 'Contact Bank Acct Name',
            'contact_bank_acct_name_helper' => ' ',
            'contact_bank_acct_number' => 'Contact Bank Acct Number',
            'contact_bank_acct_number_helper' => ' ',
            'contact_bank_notes' => 'Contact Bank Notes',
            'contact_bank_notes_helper' => ' ',
        ],
    ],
    'warehouse' => [
        'title' => 'Warehouses',
        'title_singular' => 'Warehouse',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'warehouse' => 'Warehouse ID',
            'warehouse_helper' => ' ',
            'warehouse_name' => 'Warehouse Name',
            'warehouse_name_helper' => ' ',
            'telephone_number' => 'Telephone Number',
            'telephone_number_helper' => ' ',
            'is_default' => 'Is Default',
            'is_default_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'street_address' => 'Street Address',
            'street_address_helper' => ' ',
            'city' => 'City',
            'city_helper' => ' ',
            'country' => 'Country',
            'country_helper' => ' ',
            'warehouse_contact' => 'Warehouse Contact',
            'warehouse_contact_helper' => ' ',
        ],
    ],
    'brand' => [
        'title' => 'Brands',
        'title_singular' => 'Brand',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'brand_name' => 'Brand Name',
            'brand_name_helper' => ' ',
            'description' => 'Description',
            'description_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'freight' => [
        'title' => 'Freights',
        'title_singular' => 'Freight',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'freight_company_name' => 'Freight Company Name',
            'freight_company_name_helper' => ' ',
            'telephone_number' => 'Telephone Number',
            'telephone_number_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'street_address' => 'Street Address',
            'street_address_helper' => ' ',
            'city' => 'City',
            'city_helper' => ' ',
            'country' => 'Country',
            'country_helper' => ' ',
            'contact_name' => 'Contact Name',
            'contact_name_helper' => ' ',
        ],
    ],
    'masterData' => [
        'title' => 'For Deletion',
        'title_singular' => 'For Deletion',
    ],
    'branch' => [
        'title' => 'Branch',
        'title_singular' => 'Branch',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'branch_name' => 'Branch Name',
            'branch_name_helper' => ' ',
            'telephone_number' => 'Telephone Number',
            'telephone_number_helper' => ' ',
            'is_default' => 'Is Default',
            'is_default_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'branch' => 'Branch ID',
            'branch_helper' => ' ',
            'branch_contact' => 'Branch Contact',
            'branch_contact_helper' => ' ',
            'street_address' => 'Street Address',
            'street_address_helper' => ' ',
            'city' => 'City',
            'city_helper' => ' ',
            'country' => 'Country',
            'country_helper' => ' ',
        ],
    ],
    'inventoryItem' => [
        'title' => 'Items',
        'title_singular' => 'Item',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'item_number' => 'Part Number',
            'item_number_helper' => ' ',
            'item_description' => 'Description',
            'item_description_helper' => ' ',
            'item_long_description' => 'Long Description',
            'item_long_description_helper' => ' ',
            'remarks' => 'Remarks',
            'remarks_helper' => ' ',
            'weight' => 'Weight',
            'weight_helper' => ' ',
            'price_rating_0' => 'Price',
            'price_rating_0_helper' => ' ',
            'price_rating_0_placeholder' => '0%',
            'price_rating_2' => 'Price for 2% Customer Rating',
            'price_rating_2_placeholder' => '2%',
            'price_rating_2_helper' => ' ',
            'price_rating_5' => 'Price for 5% Customer Rating',
            'price_rating_5_helper' => ' ',
            'price_rating_5_placeholder' => '5%',
            'price_rating_7' => 'Price for  7% Customer Rating',
            'price_rating_7_helper' => ' ',
            'price_rating_7_placeholder' => '7%',
            'price_rating_10' => 'Price for 10% Customer Rating',
            'price_rating_10_helper' => ' ',
            'price_rating_10_placeholder' => '10%',
            'custom_customer_rating' => 'Custom Customer Rating',
            'custom_customer_rating_helper' => ' ',
            'custom_rating_price' => 'Custom Price',
            'custom_rating_price_placeholder' => 'Custom %',
            'custom_rating_price_helper' => ' ',
            'size' => 'Size',
            'price' => "Price based on Customer Rating",
            'size_1' => 'Size 1',
            'size_1_helper' => ' ',
            'size_2' => 'Size 2',
            'size_2_helper' => ' ',
            'size_3' => 'Size 3',
            'size_3_helper' => ' ',
            'size_4' => 'Size 4',
            'size_4_helper' => ' ',
            'size_5' => 'Size 5',
            'size_5_helper' => ' ',
            'size_6' => 'Size 6',
            'size_6_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'uom' => 'UOM',
            'uom_helper' => ' ',
            'control_no' => 'Control No',
            'control_no_helper' => ' ',
            'type' => 'Type',
            'type_helper' => ' ',
            'danger_run_rate_level' => 'Danger Run Rate Level',
            'danger_run_rate_level_helper' => ' ',
            'min_order_quantity' => 'Minimum Order Qty',
            'min_order_quantity_helper' => ' ',
            'compose_kit_option' => 'Compose Kit Option',
            'compose_kit_option_helper' => ' ',
            'override_agent_commissions' => 'Override Agent Commissions',
            'override_agent_commissions_helper' => ' ',
            'override_op_commissions' => 'Override OP Commissions',
            'override_op_commissions_helper' => ' ',
            'item_images' => 'Item Images',
            'item_images_helper' => ' ',
            'item_files' => 'Item Files',
            'item_files_helper' => ' ',
            'weight_unit' => 'Weight Unit',
            'weight_unit_helper' => ' ',
            'item_classification' => 'Item Classification',
            'item_classification_helper' => ' ',
            'item_machine' => 'Item Machine',
            'item_machine_helper' => ' ',
            'item_application' => 'Item Application',
            'item_application_helper' => ' ',
            'manufacturer_no' => 'Manufacturer No',
            'manufacturer_no_helper' => ' ',
            'replacement_no' => 'Replacement No',
            'replacement_no_helper' => ' ',
        ],
    ],
    'userAlert' => [
        'title' => 'User Alerts',
        'title_singular' => 'User Alert',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'alert_text' => 'Alert Text',
            'alert_text_helper' => ' ',
            'alert_link' => 'Alert Link',
            'alert_link_helper' => ' ',
            'user' => 'Users',
            'user_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
        ],
    ],
    'sale' => [
        'title' => 'Sales',
        'title_singular' => 'Sale',
    ],
    'quotation' => [
        'title' => 'Quotation',
        'title_singular' => 'Quotation',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'control_number' => 'Control Number',
            'control_number_helper' => ' ',
            'company_name' => 'Company Name',
            'company_name_helper' => ' ',
            'branch' => 'Branch',
            'branch_helper' => ' ',
            'warehouse' => 'Warehouse',
            'warehouse_helper' => ' ',
            'validity' => 'Valid Until',
            'validity_helper' => ' ',
            'attention_to' => 'Attention To',
            'attention_to_helper' => ' ',
            'requisition_number' => 'Requisition Number',
            'requisition_number_helper' => ' ',
            'remarks' => 'Remarks',
            'remarks_helper' => ' ',
            'freight_information' => 'Freight Information',
            'freight_information_helper' => ' ',
            'action' => 'Action',
            'action_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'quotation_number' => 'Quotation Number',
            'quotation_number_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
        ],
    ],
    'quotationItem' => [
        'title' => 'Quotation Item',
        'title_singular' => 'Quotation Item',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'item_number' => 'Item Number',
            'item_number_helper' => ' ',
            'quantity' => 'Qty',
            'quantity_helper' => ' ',
            'notes' => 'Notes',
            'notes_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
            'quotation_number' => 'Quotation Number',
            'quotation_number_helper' => ' ',
        ],
    ],
    'salesInvoice' => [
        'title' => 'Sales Invoice',
        'title_singular' => 'Sales Invoice',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'control_number' => 'Control Number',
            'control_number_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'invoice_number' => 'Invoice Number',
            'invoice_number_helper' => ' ',
            'company_name' => 'Company Name',
            'company_name_helper' => ' ',
            'branch' => 'Branch',
            'branch_helper' => ' ',
            'warehouse' => 'Warehouse',
            'warehouse_helper' => ' ',
            'validity' => 'Valid Until',
            'validity_helper' => ' ',
            'customer_terms' => 'Payment Terms',
            'customer_terms_helper' => ' ',
            'reference_po' => 'Reference Po',
            'reference_po_helper' => ' ',
            'remarks' => 'Remarks',
            'remarks_helper' => ' ',
            'freight_information' => 'Freight Information',
            'freight_information_helper' => ' ',
            'notes_for_watermark' => 'Notes For Watermark',
            'notes_for_watermark_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
            'encoded_by' => 'Encoded By',
            'encoded_by_helper' => ' ',
            'prepared_by' => 'Prepared By',
            'prepared_by_helper' => ' ',
            'delivered_by' => 'Delivered By',
            'delivered_by_helper' => ' ',
            'print_zero_vat' => 'Print Zero VAT',
            'print_zero_vat_helper' => ' ',
            'countered' => 'Countered',
            'countered_helper' => ' ',
            'customer_discount' => 'Customer Discount',
            'customer_discount_helper' => ' ',
        ],
    ],
    'acknowledgementForm' => [
        'title' => 'Acknowledgement Form',
        'title_singular' => 'Acknowledgement Form',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'control_number' => 'Control Number',
            'control_number_helper' => ' ',
            'ack_form_number' => 'Acknowledgement Form Number',
            'ack_form_number_helper' => ' ',
            'company_name' => 'Company Name',
            'company_name_helper' => ' ',
            'branch' => 'Branch',
            'branch_helper' => ' ',
            'warehouse' => 'Warehouse',
            'warehouse_helper' => ' ',
            'validity' => 'Valid Until',
            'validity_helper' => ' ',
            'customer_terms' => 'Payment Terms',
            'customer_terms_helper' => ' ',
            'reference_po' => 'Reference Po',
            'reference_po_helper' => ' ',
            'remarks' => 'Remarks',
            'remarks_helper' => ' ',
            'freight_information' => 'Freight Information',
            'freight_information_helper' => ' ',
            'notes_for_watermark' => 'Notes For Watermark',
            'notes_for_watermark_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'encoded_by' => 'Encoded By',
            'encoded_by_helper' => ' ',
            'prepared_by' => 'Prepared By',
            'prepared_by_helper' => ' ',
            'delivered_by' => 'Delivered By',
            'delivered_by_helper' => ' ',
            'print_zero_vat' => 'Print Zero VAT',
            'print_zero_vat_helper' => ' ',
            'countered' => 'Countered',
            'countered_helper' => ' ',
            'customer_discount' => 'Customer Discount',
            'customer_discount_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
        ],
    ],
    'subjectForApproval' => [
        'title' => 'Subject For Approval',
        'title_singular' => 'Subject For Approval',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'control_number' => 'Control Number',
            'control_number_helper' => ' ',
            'sfa_form_number' => 'Subject For Approval Form Number',
            'sfa_form_number_helper' => ' ',
            'company_name' => 'Company Name',
            'company_name_helper' => ' ',
            'branch' => 'Branch',
            'branch_helper' => ' ',
            'warehouse' => 'Warehouse',
            'warehouse_helper' => ' ',
            'validity' => 'Valid Until',
            'validity_helper' => ' ',
            'customer_terms' => 'Payment Terms',
            'customer_terms_helper' => ' ',
            'reference_po' => 'Reference Po',
            'reference_po_helper' => ' ',
            'remarks' => 'Remarks',
            'remarks_helper' => ' ',
            'freight_information' => 'Freight Information',
            'freight_information_helper' => ' ',
            'notes_for_watermark' => 'Notes For Watermark',
            'notes_for_watermark_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'encoded_by' => 'Encoded By',
            'encoded_by_helper' => ' ',
            'prepared_by' => 'Prepared By',
            'prepared_by_helper' => ' ',
            'delivered_by' => 'Delivered By',
            'delivered_by_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
        ],
    ],
    'inventoryManagement' => [
        'title' => 'Inventory',
        'title_singular' => 'Inventory',
    ],
    'financeManagement' => [
        'title' => 'Finance',
        'title_singular' => 'Finance',
    ],
    'stockReceipt' => [
        'title' => 'Stock Receipt',
        'title_singular' => 'Stock Receipt',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'remarks' => 'Remarks',
            'remarks_helper' => ' ',
            'encoder' => 'Encoder',
            'encoder_helper' => ' ',
            'prepared_by' => 'Prepared By',
            'prepared_by_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
            'branch' => 'Branch',
            'branch_helper' => ' ',
            'msr_control_no' => 'MSR Control No',
            'msr_control_no_helper' => ' ',
            'po_control_no' => 'PO Control No',
            'po_control_no_helper' => ' ',
            'warehouse' => 'Warehouse',
            'warehouse_helper' => ' ',
        ],
    ],
    'statementOfAccount' => [
        'title' => 'Statement Of Account',
        'title_singular' => 'Statement Of Account',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'control_number' => 'Control Number',
            'control_number_helper' => ' ',
            'company_name' => 'Company Name',
            'company_name_helper' => ' ',
            'branch' => 'Branch',
            'branch_helper' => ' ',
            'from_date' => 'From Date',
            'from_date_helper' => ' ',
            'to_date' => 'To Date',
            'to_date_helper' => ' ',
            'prev_sales_balance' => 'Previous Sales Balance',
            'prev_sales_balance_helper' => ' ',
            'current_sales_balance' => 'Current Sales Balance',
            'current_sales_balance_helper' => ' ',
            'prev_freight_balance' => 'Previous Freight Balance',
            'prev_freight_balance_helper' => ' ',
            'current_freight_balance' => 'Current Freight Balance',
            'current_freight_balance_helper' => ' ',
            'encoder' => 'Encoder',
            'encoder_helper' => ' ',
            'courier' => 'Courier',
            'courier_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
        ],
    ],
    'machine' => [
        'title' => 'Machines',
        'title_singular' => 'Machine',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'machine_name' => 'Machine Name',
            'machine_name_helper' => ' ',
            'description' => 'Description',
            'description_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'supplierContact' => [
        'title' => 'Supplier Contacts',
        'title_singular' => 'Supplier Contact',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'contact_name' => 'Contact Name',
            'contact_name_helper' => ' ',
            'telephone_number' => 'Telephone Number',
            'telephone_number_helper' => ' ',
            'mobile_number' => 'Mobile Number',
            'mobile_number_helper' => ' ',
            'email_address' => 'Email Address',
            'email_address_helper' => ' ',
            'notes' => 'Notes',
            'notes_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'contact_position' => 'Contact Position',
            'contact_position_helper' => ' ',
            'supplier_name' => 'Supplier Company Name',
            'supplier_name_helper' => ' ',
        ],
    ],
    'inventoryMasterData' => [
        'title' => 'Inventory Master Data',
        'title_singular' => 'Inventory Master Data',
    ],
    'companyManagement' => [
        'title' => 'Company',
        'title_singular' => 'Company',
    ],
    'stockAdjustmentType' => [
        'title' => 'Stock Adjustment Types',
        'title_singular' => 'Stock Adjustment Type',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'code' => 'Code',
            'code_helper' => ' ',
            'adjustment_type' => 'Adjustment Type',
            'adjustment_type_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'inventoryClassification' => [
        'title' => 'Classifications',
        'title_singular' => 'Classification',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'classification_name' => 'Classification Name',
            'classification_name_helper' => ' ',
            'description' => 'Description',
            'description_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'parent' => 'Parent',
            'parent_helper' => ' ',
        ],
    ],
    'supplierShortlist' => [
        'title' => 'Supplier Shortlist',
        'title_singular' => 'Supplier Shortlist',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'supplier_name' => 'Supplier Name',
            'supplier_name_helper' => ' ',
            'shortlist_control_no' => 'Shortlist Control No',
            'shortlist_control_no_helper' => ' ',
            'total_shortlist_value' => 'Total Shortlist Value',
            'total_shortlist_value_helper' => ' ',
            'supplier_threshold_value' => 'Supplier Threshold Value',
            'supplier_threshold_value_helper' => ' ',
            'shortlist_status' => 'Shortlist Status',
            'shortlist_status_helper' => ' ',
            'items_below_rr' => "No of items below RR",
            'type' => "Type"
        ],
    ],
    'supplierShortlistItem' => [
        'title' => 'Supplier Shortlist Item',
        'title_singular' => 'Supplier Shortlist Item',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'shortlist_control_no' => 'Shortlist Control No',
            'shortlist_control_no_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'quantity' => 'Qty',
            'quantity_helper' => ' ',
            'part_number' => 'Part Number',
            'part_number_helper' => ' ',
            'notes' => 'notes',
            'notes_helper' => ' ',
            'req_no' => 'Req No',
            'req_no_helper' => ' ',
            'inventory_quantity' => 'Inventory Qty',
            'inventory_quantity_helper' => ' ',
            'rr_level' => 'RR Level',
            'rr_level_helper' => ' ',
            'brand' => 'Brand',
            'brand_helper' => ' ',
        ],
    ],
    'purchaseRequisition' => [
        'title' => 'Purchase Requisition',
        'title_singular' => 'Purchase Requisition',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'supplier_name' => 'Supplier Name',
            'supplier_name_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'pr_control_no' => 'PR Control No',
            'pr_control_no_helper' => ' ',
            'shortlist_control_no' => 'Shortlist Control No',
            'shortlist_control_no_helper' => ' ',
            'total_pr_value' => 'Total PR Value',
            'total_pr_value_helper' => ' ',
            'date_sent' => 'Date Created',
            'date_sent_helper' => ' ',
            'date_expected' => 'Date Expected',
            'date_expected_helper' => ' ',
            'remarks' => 'Remarks',
            'remarks_helper' => ' ',
            'source_currency' => 'Source Currency',
            'source_currency_helper' => ' ',
            'source_currency_amount' => 'Source Currency Amount',
            'source_currency_amount_helper' => ' ',
            'php_currency_amount' => 'PHP Currency Amount',
            'php_currency_amount_helper' => ' ',
            'pr_status' => 'PR Status',
            'pr_status_helper' => ' ',
        ],
    ],
    'purchaseRequisitionItem' => [
        'title' => 'Purchase Requisition Item',
        'title_singular' => 'Purchase Requisition Item',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'quantity' => 'Qty',
            'quantity_helper' => ' ',
            'part_number' => 'Part Number',
            'part_number_helper' => ' ',
            'notes' => 'notes',
            'notes_helper' => ' ',
            'req_no' => 'Req No',
            'req_no_helper' => ' ',
            'inventory_quantity' => 'Inventory Qty',
            'inventory_quantity_helper' => ' ',
            'rr_level' => 'RR Level',
            'rr_level_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'pr_control_no' => 'PR Control No',
            'pr_control_no_helper' => ' ',
            'brand' => 'Brand',
            'brand_helper' => ' ',
        ],
    ],
    'itemApplication' => [
        'title' => 'Item Applications',
        'title_singular' => 'Item Application',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'application_name' => 'Application Name',
            'application_name_helper' => ' ',
            'description' => 'Description',
            'description_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'supplier' => [
        'title' => 'Supplier',
        'title_singular' => 'Supplier',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'company_name' => 'Company Name',
            'company_name_helper' => ' ',
            'supplier_control_number' => 'Supplier Control Number',
            'supplier_control_number_helper' => ' ',
            'supplier_code_name' => 'Supplier Code Name',
            'supplier_code_name_helper' => ' ',
            'tin' => 'Company TIN',
            'tin_helper' => ' ',
            'telephone_number' => 'Telephone Number',
            'telephone_number_helper' => ' ',
            'mobile_number' => 'Mobile Number',
            'mobile_number_helper' => ' ',
            'fax' => 'Fax',
            'fax_helper' => ' ',
            'email_address' => 'Email Address',
            'email_address_helper' => ' ',
            'supplier_type' => 'Supplier Type',
            'supplier_type_helper' => ' ',
            'supplier_terms' => 'Supplier Terms',
            'supplier_terms_helper' => ' ',
            'threshold_shipment_value' => 'Threshold Shipment Value',
            'threshold_shipment_value_helper' => ' ',
            'default_brand' => 'Default Brand',
            'default_brand_helper' => ' ',
            'default_currency' => 'Default Currency',
            'default_currency_helper' => ' ',
            'internal_contact' => 'Internal Contact',
            'internal_contact_helper' => ' ',
            'is_disabled' => 'Is Disabled',
            'is_disabled_helper' => ' ',
            'supplier_notes' => 'Supplier Notes',
            'supplier_notes_helper' => ' ',
            'supplier_bank_name' => 'Supplier Bank Name',
            'supplier_bank_name_helper' => ' ',
            'supplier_bank_branch' => 'Supplier Bank Branch',
            'supplier_bank_branch_helper' => ' ',
            'supplier_bank_acct_name' => 'Supplier Bank Acct Name',
            'supplier_bank_acct_name_helper' => ' ',
            'supplier_bank_acct_number' => 'Supplier Bank Acct Number',
            'supplier_bank_acct_number_helper' => ' ',
            'supplier_bank_notes' => 'Supplier Bank Notes',
            'supplier_bank_notes_helper' => ' ',
            'street_address' => 'Street Address',
            'street_address_helper' => ' ',
            'city' => 'City',
            'city_helper' => ' ',
            'country' => 'Country',
            'country_helper' => ' ',
            'notes' => 'Notes',
            'notes_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'customer' => [
        'title' => 'Customer',
        'title_singular' => 'Customer',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'control_number' => 'Control Number',
            'control_number_helper' => ' ',
            'company_name' => 'Company Name',
            'company_name_helper' => ' ',
            'tin' => 'Company TIN',
            'tin_helper' => ' ',
            'telephone_number' => 'Telephone Number',
            'telephone_number_helper' => ' ',
            'mobile_number' => 'Mobile Number',
            'mobile_number_helper' => ' ',
            'fax' => 'Fax',
            'fax_helper' => ' ',
            'email_address' => 'Email Address',
            'email_address_helper' => ' ',
            'sales_document_type' => 'Sales Document Type',
            'sales_document_type_helper' => ' ',
            'customer_code' => 'Customer Code',
            'customer_code_helper' => ' ',
            'customer_rating' => 'Customer Rating',
            'customer_rating_helper' => ' ',
            'customer_terms' => 'Customer Terms',
            'customer_terms_helper' => ' ',
            'customer_credit_limit' => 'Customer Credit Limit',
            'customer_credit_limit_helper' => ' ',
            'zero_vat' => 'Zero VAT',
            'zero_vat_helper' => ' ',
            'receive_original_invoice' => 'Receive Original Invoice',
            'receive_original_invoice_helper' => ' ',
            'withholding_tax_agent' => 'Withholding Tax Agent',
            'withholding_tax_agent_helper' => ' ',
            'accounting_info' => 'Accounting Info',
            'accounting_info_helper' => ' ',
            'delivery_info' => 'Delivery Info',
            'delivery_info_helper' => ' ',
            'account_manager' => 'Account Manager',
            'account_manager_helper' => ' ',
            'customer_notes' => 'Customer Notes',
            'customer_notes_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'agent' => 'Agent',
            'agent_helper' => ' ',
        ],
    ],
    'customerBank' => [
        'title' => 'Customer Bank',
        'title_singular' => 'Customer Bank',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'bank_name' => 'Bank Name',
            'bank_name_helper' => ' ',
            'bank_initials' => 'Bank Initials',
            'bank_initials_helper' => ' ',
            'account_number' => 'Account Number',
            'account_number_helper' => ' ',
            'account_type' => 'Account Type',
            'account_type_helper' => ' ',
            'contact_name' => 'Contact Name',
            'contact_name_helper' => ' ',
            'street_address' => 'Street Address',
            'street_address_helper' => ' ',
            'city' => 'City',
            'city_helper' => ' ',
            'country' => 'Country',
            'country_helper' => ' ',
            'telephone_number' => 'Telephone Number',
            'telephone_number_helper' => ' ',
            'notes' => 'Notes',
            'notes_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'customer_name' => 'Customer Name',
            'customer_name_helper' => ' ',
        ],
    ],
    'purchaseOrder' => [
        'title' => 'Purchase Order',
        'title_singular' => 'Purchase Order',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'supplier_name' => 'Supplier Name',
            'supplier_name_helper' => ' ',
            'pr_control_no' => 'Pr Control No',
            'pr_control_no_helper' => ' ',
            'shortlist_control_no' => 'Shortlist Control No',
            'shortlist_control_no_helper' => ' ',
            'total_po_value' => 'Total PO Value',
            'total_po_value_helper' => ' ',
            'date_sent' => 'Date Created',
            'date_sent_helper' => ' ',
            'date_expected' => 'Date Expected',
            'date_expected_helper' => ' ',
            'remarks' => 'Remarks',
            'remarks_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'po_control_no' => 'PO Control No',
            'po_control_no_helper' => ' ',
            'total_returns' => 'Total Returns',
            'total_returns_helper' => ' ',
            'total_discount' => 'Total Discount',
            'total_discount_helper' => ' ',
            'total_net_amount' => 'Total Net Amount',
            'total_net_amount_helper' => ' ',
            'po_status' => 'PO Status',
            'po_status_helper' => ' ',
        ],
    ],
    'purchaseOrderItem' => [
        'title' => 'Purchase Order Item',
        'title_singular' => 'Purchase Order Item',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'quantity' => 'Qty',
            'quantity_helper' => ' ',
            'part_number' => 'Part Number',
            'part_number_helper' => ' ',
            'notes' => 'remarks',
            'notes_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'po_control_no' => 'PO Control No',
            'po_control_no_helper' => ' ',
            'currency' => 'Currency',
            'currency_helper' => ' ',
            'unit_price' => 'Unit Price',
            'unit_price_helper' => ' ',
            'total_price' => 'Total Price',
            'total_price_helper' => ' ',
            'brand' => 'Brand',
            'brand_helper' => ' ',
        ],
    ],
    'purchaseOrderReturn' => [
        'title' => 'Purchase Order Returns',
        'title_singular' => 'Purchase Order Return',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'po_control_no' => 'PO Control No',
            'po_control_no_helper' => ' ',
            'quantity' => 'Qty',
            'quantity_helper' => ' ',
            'part_number' => 'Part Number',
            'part_number_helper' => ' ',
            'currency' => 'Currency',
            'currency_helper' => ' ',
            'notes' => 'remarks',
            'notes_helper' => ' ',
            'unit_price' => 'Unit Price',
            'unit_price_helper' => ' ',
            'total_price' => 'Total Price',
            'total_price_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'purchase_return_slip_no' => 'Purchase Return Slip No',
            'purchase_return_slip_no_helper' => ' ',
            'msr_control_no' => 'MSR Control No',
            'msr_control_no_helper' => ' ',
            'brand' => 'Brand',
            'brand_helper' => ' ',
        ],
    ],
    'purchaseOrderPayment' => [
        'title' => 'Purchase Order Payments',
        'title_singular' => 'Purchase Order Payment',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'po_payment_receipt_no' => 'PO Payment Receipt No',
            'po_payment_receipt_no_helper' => ' ',
            'po_control_no' => 'PO Control No',
            'po_control_no_helper' => ' ',
            'quantity' => 'Qty',
            'quantity_helper' => ' ',
            'part_number' => 'Part Number',
            'part_number_helper' => ' ',
            'currency' => 'Currency',
            'currency_helper' => ' ',
            'notes' => 'remarks',
            'notes_helper' => ' ',
            'unit_price' => 'Unit Price',
            'unit_price_helper' => ' ',
            'total_price' => 'Total Price',
            'total_price_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'total_discount' => 'Total Discount',
            'total_discount_helper' => ' ',
            'msr_control_no' => 'MSR Control No',
            'msr_control_no_helper' => ' ',
            'brand' => 'Brand',
            'brand_helper' => ' ',
        ],
    ],
    'stockReceiptItem' => [
        'title' => 'Stock Receipt Item',
        'title_singular' => 'Stock Receipt Item',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'po_control_no' => 'PO Control No',
            'po_control_no_helper' => ' ',
            'quantity' => 'Qty',
            'quantity_helper' => ' ',
            'part_number' => 'Part Number',
            'part_number_helper' => ' ',
            'currency' => 'Currency',
            'currency_helper' => ' ',
            'notes' => 'remarks',
            'notes_helper' => ' ',
            'unit_price' => 'Unit Price',
            'unit_price_helper' => ' ',
            'total_price' => 'Total Price',
            'total_price_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'msr_control_no' => 'MSR Control No',
            'msr_control_no_helper' => ' ',
            'brand' => 'Brand',
            'brand_helper' => ' ',
        ],
    ],
    'stockStorage' => [
        'title' => 'Stock Storage',
        'title_singular' => 'Stock Storage',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'msr_control_no' => 'MSR Control No',
            'msr_control_no_helper' => ' ',
            'branch' => 'Branch',
            'branch_helper' => ' ',
            'warehouse' => 'Warehouse',
            'warehouse_helper' => ' ',
            'remarks' => 'Remarks',
            'remarks_helper' => ' ',
            'encoder' => 'Encoder',
            'encoder_helper' => ' ',
            'prepared_by' => 'Prepared By',
            'prepared_by_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
            'mss_control_no' => 'MSS Control No',
            'mss_control_no_helper' => ' ',
        ],
    ],
    'stockStorageItem' => [
        'title' => 'Stock Storage Item',
        'title_singular' => 'Stock Storage Item',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'part_number' => 'Part Number',
            'part_number_helper' => ' ',
            'quantity' => 'Qty',
            'quantity_helper' => ' ',
            'currency' => 'Currency',
            'currency_helper' => ' ',
            'notes' => 'remarks',
            'notes_helper' => ' ',
            'unit_price' => 'Unit Price',
            'unit_price_helper' => ' ',
            'total_price' => 'Total Price',
            'total_price_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'mss_control_no' => 'MSS Control No',
            'mss_control_no_helper' => ' ',
            'brand' => 'Brand',
            'brand_helper' => ' ',
        ],
    ],
    'salesInvoiceItem' => [
        'title' => 'Sales Invoice Item',
        'title_singular' => 'Sales Invoice Item',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'invoice_number' => 'Invoice Number',
            'invoice_number_helper' => ' ',
            'item_number' => 'Item Number',
            'item_number_helper' => ' ',
            'quantity' => 'Qty',
            'quantity_helper' => ' ',
            'notes' => 'Notes',
            'notes_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
        ],
    ],
    'ackFormItem' => [
        'title' => 'Ack Form Item',
        'title_singular' => 'Ack Form Item',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'ack_form_number' => 'Ack Form Number',
            'ack_form_number_helper' => ' ',
            'item_number' => 'Item Number',
            'item_number_helper' => ' ',
            'quantity' => 'Qty',
            'quantity_helper' => ' ',
            'notes' => 'Notes',
            'notes_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
        ],
    ],
    'sfaItem' => [
        'title' => 'SFA Item',
        'title_singular' => 'SFA Item',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'sfa_form_number' => 'SFA Form Number',
            'sfa_form_number_helper' => ' ',
            'item_number' => 'Item Number',
            'item_number_helper' => ' ',
            'quantity' => 'Qty',
            'quantity_helper' => ' ',
            'notes' => 'Notes',
            'notes_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
        ],
    ],
    'salesReturnItem' => [
        'title' => 'Sales Return Item',
        'title_singular' => 'Sales Return Item',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'invoice_number' => 'Invoice Number',
            'invoice_number_helper' => ' ',
            'item_number' => 'Item Number',
            'item_number_helper' => ' ',
            'quantity' => 'Qty',
            'quantity_helper' => ' ',
            'notes' => 'Notes',
            'notes_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
            'sales_return_control_no' => 'Sales Return Control No',
            'sales_return_control_no_helper' => ' ',
            'ack_form_number' => 'Ack Form Number',
            'ack_form_number_helper' => ' ',
            'sfa_control_number' => 'Sfa Control Number',
            'sfa_control_number_helper' => ' ',
        ],
    ],
    'commission' => [
        'title' => 'Commissions',
        'title_singular' => 'Commission',
    ],
    'salesPayment' => [
        'title' => 'Sales Payments',
        'title_singular' => 'Sales Payment',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'sales_payments_control_number' => 'Sales Payments Control Number',
            'sales_payments_control_number_helper' => ' ',
            'invoice_number' => 'Invoice Number',
            'invoice_number_helper' => ' ',
            'ack_form_number' => 'Ack Form Number',
            'ack_form_number_helper' => ' ',
            'sfa_number' => 'Sfa Number',
            'sfa_number_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
        ],
    ],
    'salesPaymentsItem' => [
        'title' => 'Sales Payments Item',
        'title_singular' => 'Sales Payments Item',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'sales_payments_control_no' => 'Sales Payments Control No',
            'sales_payments_control_no_helper' => ' ',
            'notes' => 'Notes',
            'notes_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'invoice_number' => 'Invoice Number',
            'invoice_number_helper' => ' ',
            'ack_form_number' => 'Ack Form Number',
            'ack_form_number_helper' => ' ',
            'sfa_control_number' => 'Sfa Control Number',
            'sfa_control_number_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
            'deductions_total' => 'Deductions Total',
            'deductions_total_helper' => ' ',
            'sales_total' => 'Sales Total',
            'sales_total_helper' => ' ',
            'freight_total' => 'Freight Total',
            'freight_total_helper' => ' ',
        ],
    ],
    'salesPaymentMode' => [
        'title' => 'Sales Payment Modes',
        'title_singular' => 'Sales Payment Mode',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'sales_payments_control_no' => 'Sales Payments Control No',
            'sales_payments_control_no_helper' => ' ',
            'notes' => 'Notes',
            'notes_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
            'payment_mode' => 'Payment Mode',
            'payment_mode_helper' => ' ',
            'payment_amount' => 'Payment Amount',
            'payment_amount_helper' => ' ',
            'customer_bank' => 'Customer Bank',
            'customer_bank_helper' => ' ',
            'tax_wh' => 'Tax Wh',
            'tax_wh_helper' => ' ',
            'amount_wh' => 'Amount Withheld',
            'amount_wh_helper' => ' ',
            'cert_given' => 'Cert Given',
            'cert_given_helper' => ' ',
            'check_no' => 'Check No',
            'check_no_helper' => ' ',
            'check_date' => 'Check Date',
            'check_date_helper' => ' ',
        ],
    ],
    'replacementNo' => [
        'title' => 'Replacement Nos',
        'title_singular' => 'Replacement No',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'replacement_no' => 'Replacement No',
            'replacement_no_helper' => ' ',
            'description' => 'Description',
            'description_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'brand' => 'Brand',
            'brand_helper' => ' ',
        ],
    ],
    'manufacturerNo' => [
        'title' => 'Manufacturer Nos',
        'title_singular' => 'Manufacturer No',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'manufacturer_no' => 'Manufacturer No',
            'manufacturer_no_helper' => ' ',
            'description' => 'Description',
            'description_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'brand' => 'Brand',
            'brand_helper' => ' ',
        ],
    ],
    'stockAdjustment' => [
        'title' => 'Stock Adjustment',
        'title_singular' => 'Stock Adjustment',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'branch' => 'Branch',
            'branch_helper' => ' ',
            'warehouse' => 'Warehouse',
            'warehouse_helper' => ' ',
            'remarks' => 'Remarks',
            'remarks_helper' => ' ',
            'encoder' => 'Encoder',
            'encoder_helper' => ' ',
            'prepared_by' => 'Prepared By',
            'prepared_by_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
            'msa_control_no' => 'MSA Control No',
            'msa_control_no_helper' => ' ',
        ],
    ],
    'stockAdjustmentItem' => [
        'title' => 'Stock Adjustment Item',
        'title_singular' => 'Stock Adjustment Item',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'part_number' => 'Part Number',
            'part_number_helper' => ' ',
            'quantity' => 'Qty',
            'quantity_helper' => ' ',
            'notes' => 'remarks',
            'notes_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'msa_control_no' => 'MSA Control No',
            'msa_control_no_helper' => ' ',
            'brand' => 'Brand',
            'brand_helper' => ' ',
            'adjustment_reason' => 'Adjustment Reason',
            'adjustment_reason_helper' => ' ',
        ],
    ],
    'stockTransfer' => [
        'title' => 'Stock Transfer',
        'title_singular' => 'Stock Transfer',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'branch' => 'Branch',
            'branch_helper' => ' ',
            'encoder' => 'Encoder',
            'encoder_helper' => ' ',
            'prepared_by' => 'Prepared By',
            'prepared_by_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
            'mst_control_no' => 'MST Control No',
            'mst_control_no_helper' => ' ',
            'from_warehouse' => 'From Warehouse',
            'from_warehouse_helper' => ' ',
            'to_warehouse' => 'To Warehouse',
            'to_warehouse_helper' => ' ',
            'transfer_reason' => 'Transfer Reason',
            'transfer_reason_helper' => ' ',
        ],
    ],
    'stockTransferItem' => [
        'title' => 'Stock Transfer Item',
        'title_singular' => 'Stock Transfer Item',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'part_number' => 'Part Number',
            'part_number_helper' => ' ',
            'brand' => 'Brand',
            'brand_helper' => ' ',
            'quantity' => 'Qty',
            'quantity_helper' => ' ',
            'notes' => 'remarks',
            'notes_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'mst_control_no' => 'MST Control No',
            'mst_control_no_helper' => ' ',
        ],
    ],
    'inventoryDashboard' => [
        'title' => 'Inventory',
        'title_singular' => 'Inventory',
    ],
    'salesDashboard' => [
        'title' => 'Sales',
        'title_singular' => 'Sales',
    ],
    'financeDashboard' => [
        'title' => 'Finance',
        'title_singular' => 'Finance',
    ],
    'stockManagement' => [
        'title' => 'Stock Management',
        'title_singular' => 'Stock Management',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'part_number' => 'Part Number',
            'part_number_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'brand' => 'Brand',
            'brand_helper' => ' ',
            'warehouse_name' => 'Warehouse Name',
            'warehouse_name_helper' => ' ',
            'quantity' => 'Qty',
            'quantity_helper' => ' ',
        ],
    ],
    'salesReturn' => [
        'title' => 'Sales Return',
        'title_singular' => 'Sales Return',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'sales_return_control_no' => 'Sales Return Control No',
            'sales_return_control_no_helper' => ' ',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted at',
            'deleted_at_helper' => ' ',
            'team' => 'Team',
            'team_helper' => ' ',
            'invoice_number' => 'Invoice Number',
            'invoice_number_helper' => ' ',
            'ack_form_number' => 'Ack Form Number',
            'ack_form_number_helper' => ' ',
            'sfa_number' => 'Sfa Number',
            'sfa_number_helper' => ' ',
        ],
    ],
    'endpointConfiguration' => [
        'title' => 'Endpoint Configurations',
        'title_singular' => 'Endpoint Configuration',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'name' => 'Configuration Name',
            'name_helper' => 'A unique name to identify this endpoint configuration',
            'url' => 'Endpoint URL',
            'url_helper' => 'The relative endpoint path (e.g., IDORequestService/ido/invoke/SLStockActItems?method=ItemMiscReceiptSp)',
            'body_data_field' => 'Body Data Field Mapping',
            'body_data_field_helper' => 'JSON structure for mapping form fields to API body data',
            'erp_selection' => 'ERP System',
            'erp_selection_helper' => 'Select the ERP system (CSI or SAP)',
            'process_selection' => 'Process Type',
            'process_selection_helper' => 'Select the process type (Misc Issue, Misc Receipt, or Quantity Move)',
            'endpoint_type' => 'Endpoint Type',
            'endpoint_type_helper' => 'Select whether this is an API endpoint or Stored Procedure',
            'is_active' => 'Active Status',
            'is_active_helper' => 'Whether this configuration is currently active and available for use',
            'created_at' => 'Created at',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated at',
            'updated_at_helper' => ' ',
        ],
    ],
];
