<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Form;
use App\Models\UserGroup;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Auth;

class FormsController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $user = Auth::user();
            $query = Form::with(['userGroup', 'creator'])
                ->accessibleByUser($user)
                ->orderBy('created_at', 'desc');
            
            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('action', function ($row) use ($user) {
                    $btn = '<div class="btn-group" role="group">';
                    
                    $btn .= '<a href="' . route('admin.forms.show', $row->id) . '" class="btn btn-sm btn-primary">View</a>';
                    
                    if ($user->isAdmin() || $user->isSuperAdmin()) {
                        $btn .= '<a href="' . route('admin.forms.edit', $row->id) . '" class="btn btn-sm btn-info">Edit</a>';
                        
                        $btn .= '<form method="POST" action="' . route('admin.forms.destroy', $row->id) . '" style="display:inline-block;" onsubmit="return confirm(\'Are you sure?\')">';
                        $btn .= csrf_field();
                        $btn .= method_field('DELETE');
                        $btn .= '<button type="submit" class="btn btn-sm btn-danger">Delete</button>';
                        $btn .= '</form>';
                    }
                    
                    $btn .= '</div>';
                    
                    return $btn;
                })
                ->addColumn('user_group', function ($row) {
                    return $row->userGroup ? $row->userGroup->name : 'No Group';
                })
                ->addColumn('creator_name', function ($row) {
                    return $row->creator ? $row->creator->name : 'Unknown';
                })
                ->addColumn('status', function ($row) {
                    return $row->is_active ? 
                        '<span class="badge badge-success">Active</span>' : 
                        '<span class="badge badge-danger">Inactive</span>';
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('Y-m-d H:i:s');
                })
                ->rawColumns(['action', 'status'])
                ->make(true);
        }

        return view('admin.forms.index');
    }

    public function create()
    {
        $user = Auth::user();
        
        // Only admin users can create forms
        if (!$user->isAdmin() && !$user->isSuperAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        
        $userGroups = UserGroup::active()->pluck('name', 'id');
        return view('admin.forms.create', compact('userGroups'));
    }

    public function store(Request $request)
    {
        $user = Auth::user();
        
        // Only admin users can create forms
        if (!$user->isAdmin() && !$user->isSuperAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|json',
            'user_group_id' => 'nullable|exists:user_groups,id',
            'is_active' => 'boolean'
        ]);

        $data = $request->all();
        $data['created_by'] = $user->id;

        Form::create($data);

        return redirect()->route('admin.forms.index')
            ->with('success', 'Form created successfully.');
    }

    public function show(Form $form)
    {
        $user = Auth::user();
        
        // Check if user can access this form
        if (!$user->isAdmin() && !$user->isSuperAdmin() && $form->user_group_id != $user->user_group_id) {
            abort(403, 'Unauthorized action.');
        }
        
        $form->load(['userGroup', 'creator']);
        return view('admin.forms.show', compact('form'));
    }

    public function edit(Form $form)
    {
        $user = Auth::user();
        
        // Only admin users can edit forms
        if (!$user->isAdmin() && !$user->isSuperAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        
        $userGroups = UserGroup::active()->pluck('name', 'id');
        return view('admin.forms.edit', compact('form', 'userGroups'));
    }

    public function update(Request $request, Form $form)
    {
        $user = Auth::user();
        
        // Only admin users can update forms
        if (!$user->isAdmin() && !$user->isSuperAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|json',
            'user_group_id' => 'nullable|exists:user_groups,id',
            'is_active' => 'boolean'
        ]);

        $form->update($request->all());

        return redirect()->route('admin.forms.index')
            ->with('success', 'Form updated successfully.');
    }

    public function destroy(Form $form)
    {
        $user = Auth::user();
        
        // Only admin users can delete forms
        if (!$user->isAdmin() && !$user->isSuperAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        
        $form->delete();

        return redirect()->route('admin.forms.index')
            ->with('success', 'Form deleted successfully.');
    }

    public function preview(Form $form)
    {
        $user = Auth::user();
        
        // Check if user can access this form
        if (!$user->isAdmin() && !$user->isSuperAdmin() && $form->user_group_id != $user->user_group_id) {
            abort(403, 'Unauthorized action.');
        }
        
        return view('admin.forms.preview', compact('form'));
    }
}
