var GritterAlert = {
    alert: null,
    showError: function () {

        $('.page-content-wrapper').pgNotification({
            style: 'flip',
            message: "There were some errors.",
            position: "top-right",
            timeout: 4000,
            type: "danger"
        }).show();
//        $.gritter.add({title: 'Error!', text: 'There were some errors.', image: '/components/assets/plugins/gritter/images/error.png'});
//        if (this.alert)
//            $.gritter.remove(this.alert);
    },
    showErrorText: function (text) {
        $('.page-content-wrapper').pgNotification({
            style: 'flip',
            message: text,
            position: "top-right",
            timeout: 4000,
            type: "danger"
        }).show();
//        $.gritter.add({title: 'Error!', text: text, image: '/components/assets/plugins/gritter/images/error.png'});
//        if (this.alert)
//            $.gritter.remove(this.alert);
    },
    showSuccess: function () {
        $('.page-content-wrapper').pgNotification({
            style: 'flip',
            message: "Changes are saved.",
            position: "top-right",
            timeout: 4000,
            type: "success"
        }).show();
//        $.gritter.add({title: 'Success!', text: 'Operation Successful', image: '/components/assets/plugins/gritter/images/tick.png'});
//        if (this.alert)
//            $.gritter.remove(this.alert);
    },
    showSuccessText: function (text) {
        $('.page-content-wrapper').pgNotification({
            style: 'flip',
            message: text,
            position: "top-right",
            timeout: 4000,
            type: "success"
        }).show();
//        $.gritter.add({title: 'Success!', text: text, image: '/components/assets/plugins/gritter/images/tick.png'});
//        if (this.alert)
//            $.gritter.remove(this.alert);
    },
    showInProcess: function () {
        return   $('.page-content-wrapper').pgNotification({
            style: 'flip',
            message: ' <div class="progress-circle-indeterminate"></div>',
            position: "top-right",
            timeout: 4000,
            type: "info"
        }).show();

//        return this.alert = $.gritter.add({title: 'Processing!', text: 'Operation is processing', image: '/components/assets/plugins/gritter/images/processing.gif'});
    },
    showInProcessSticky: function () {
        return   $('.page-content-wrapper').pgNotification({
            style: 'flip',
            message: ' <div class="row"><div class="col-md-2"><div class="progress-circle-indeterminate"></div></div> <div class="col-md-10" style="line-height: 30px;">Processing</div></div>',
            position: "top-right",
            timeout: 0,
            type: "info",
            showClose: false,
        }).show();
//        return this.alert = $.gritter.add({title: 'Processing!', text: 'Operation is processing', image: '/components/assets/plugins/gritter/images/processing.gif', sticky: true});
    },
    removeNotification: function (loadingGrit) {
        $(loadingGrit).remove();
    }
};
