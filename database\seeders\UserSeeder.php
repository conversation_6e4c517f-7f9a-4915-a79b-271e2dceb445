<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create Super Admin User
        User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('Admin@1234'),
            'type' => User::TYPE_SUPER_ADMIN,
            'email_verified_at' => now(),
        ]);

        // Create Admin User
        User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('Admin@1234'),
            'type' => User::TYPE_ADMIN,
            'email_verified_at' => now(),
        ]);
    }
}
