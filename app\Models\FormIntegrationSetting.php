<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormIntegrationSetting extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'form_id',
        'endpoint_configuration_id',
        'field_mappings',
        'description',
        'is_active',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'field_mappings' => 'array',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function form()
    {
        return $this->belongsTo(Form::class);
    }

    public function endpointConfiguration()
    {
        return $this->belongsTo(EndpointConfiguration::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('form_integration_settings.is_active', true);
    }

    public function scopeByForm($query, $formId)
    {
        return $query->where('form_id', $formId);
    }

    public function scopeByEndpoint($query, $endpointId)
    {
        return $query->where('endpoint_configuration_id', $endpointId);
    }

    public function scopeByErp($query, $erp)
    {
        return $query->whereHas('endpointConfiguration', function ($q) use ($erp) {
            $q->where('erp_selection', $erp);
        });
    }

    public function scopeByProcess($query, $process)
    {
        return $query->whereHas('endpointConfiguration', function ($q) use ($process) {
            $q->where('process_selection', $process);
        });
    }

    public function scopeAccessibleByUser($query, User $user)
    {
        if ($user->isAdmin() || $user->isSuperAdmin()) {
            return $query;
        }

        return $query->whereHas('form', function ($q) use ($user) {
            $q->where('user_group_id', $user->user_group_id);
        });
    }

    // Helper methods
    public function getFormFieldsAttribute()
    {
        if (!$this->form || !$this->form->content) {
            return [];
        }

        return $this->extractFormFields($this->form->content);
    }

    public function getEndpointFieldsAttribute()
    {
        if (!$this->endpointConfiguration || !$this->endpointConfiguration->body_data_field) {
            return [];
        }

        return $this->endpointConfiguration->body_data_field;
    }

    public function validateFieldMappings()
    {
        $formFields = $this->form_fields;
        $endpointFields = $this->endpoint_fields;
        $mappings = $this->field_mappings ?? [];

        $errors = [];

        foreach ($mappings as $formField => $endpointField) {
            // Check if form field exists
            if (!$this->fieldExistsInForm($formField, $formFields)) {
                $errors[] = "Form field '{$formField}' does not exist in the selected form.";
            }

            // Check if endpoint field exists
            if (!$this->fieldExistsInEndpoint($endpointField, $endpointFields)) {
                $errors[] = "Endpoint field '{$endpointField}' does not exist in the selected endpoint.";
            }
        }

        return $errors;
    }

    private function extractFormFields($formContent)
    {
        $fields = [];

        if (isset($formContent['components']) && is_array($formContent['components'])) {
            $this->extractFieldsRecursive($formContent['components'], $fields);
        }

        return $fields;
    }

    private function extractFieldsRecursive($components, &$fields)
    {
        foreach ($components as $component) {
            if (isset($component['key']) && isset($component['type'])) {
                $fields[] = [
                    'key' => $component['key'],
                    'label' => $component['label'] ?? $component['key'],
                    'type' => $component['type'],
                    'required' => $component['validate']['required'] ?? false,
                ];
            }

            // Handle nested components (like panels, fieldsets, etc.)
            if (isset($component['components']) && is_array($component['components'])) {
                $this->extractFieldsRecursive($component['components'], $fields);
            }
        }
    }

    private function fieldExistsInForm($fieldKey, $formFields)
    {
        foreach ($formFields as $field) {
            if ($field['key'] === $fieldKey) {
                return true;
            }
        }
        return false;
    }

    private function fieldExistsInEndpoint($fieldName, $endpointFields)
    {
        foreach ($endpointFields as $field) {
            if ($field['name'] === $fieldName) {
                return true;
            }
        }
        return false;
    }

    // Static helper methods
    public static function getAvailableForms()
    {
        return Form::active()->get(['id', 'title']);
    }

    public static function getAvailableEndpoints()
    {
        return EndpointConfiguration::active()->get(['id', 'name', 'erp_selection', 'process_selection', 'endpoint_type']);
    }

    public static function getFilteredEndpoints($erp = null, $process = null, $endpointType = null)
    {
        $query = EndpointConfiguration::active();

        if ($erp) {
            $query->byErp($erp);
        }

        if ($process) {
            $query->byProcess($process);
        }

        if ($endpointType) {
            $query->byEndpointType($endpointType);
        }

        return $query->get(['id', 'name', 'erp_selection', 'process_selection', 'endpoint_type']);
    }
}
